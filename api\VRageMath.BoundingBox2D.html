﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingBox2D
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingBox2D
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingBox2D">
  
  
  <h1 id="VRageMath_BoundingBox2D" data-uid="VRageMath.BoundingBox2D" class="text-break">Class BoundingBox2D
  </h1>
  <div class="markdown level0 summary"><p>Defines an axis-aligned box-shaped 2D volume.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingBox2D</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingBox2D_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class BoundingBox2D : ValueType, IEquatable&lt;BoundingBox2D&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingBox2D__ctor_" data-uid="VRageMath.BoundingBox2D.#ctor*"></a>
  <h4 id="VRageMath_BoundingBox2D__ctor_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.#ctor(VRageMath.Vector2D,VRageMath.Vector2D)">BoundingBox2D(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D(Vector2D min, Vector2D max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum point the BoundingBox2D includes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum point the BoundingBox2D includes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingBox2D_CornerCount" data-uid="VRageMath.BoundingBox2D.CornerCount">CornerCount</h4>
  <div class="markdown level1 summary"><p>Specifies the total number of corners (8) in the BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int CornerCount = 8</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBox2D_Max" data-uid="VRageMath.BoundingBox2D.Max">Max</h4>
  <div class="markdown level1 summary"><p>The maximum point the BoundingBox2D contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D Max</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBox2D_Min" data-uid="VRageMath.BoundingBox2D.Min">Min</h4>
  <div class="markdown level1 summary"><p>The minimum point the BoundingBox2D contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D Min</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingBox2D_Center_" data-uid="VRageMath.BoundingBox2D.Center*"></a>
  <h4 id="VRageMath_BoundingBox2D_Center" data-uid="VRageMath.BoundingBox2D.Center">Center</h4>
  <div class="markdown level1 summary"><p>Calculates center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D Center { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Extents_" data-uid="VRageMath.BoundingBox2D.Extents*"></a>
  <h4 id="VRageMath_BoundingBox2D_Extents" data-uid="VRageMath.BoundingBox2D.Extents">Extents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D Extents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_HalfExtents_" data-uid="VRageMath.BoundingBox2D.HalfExtents*"></a>
  <h4 id="VRageMath_BoundingBox2D_HalfExtents" data-uid="VRageMath.BoundingBox2D.HalfExtents">HalfExtents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D HalfExtents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Height_" data-uid="VRageMath.BoundingBox2D.Height*"></a>
  <h4 id="VRageMath_BoundingBox2D_Height" data-uid="VRageMath.BoundingBox2D.Height">Height</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Height { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Size_" data-uid="VRageMath.BoundingBox2D.Size*"></a>
  <h4 id="VRageMath_BoundingBox2D_Size" data-uid="VRageMath.BoundingBox2D.Size">Size</h4>
  <div class="markdown level1 summary"><p>Size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Width_" data-uid="VRageMath.BoundingBox2D.Width*"></a>
  <h4 id="VRageMath_BoundingBox2D_Width" data-uid="VRageMath.BoundingBox2D.Width">Width</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Width { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingBox2D_Area_" data-uid="VRageMath.BoundingBox2D.Area*"></a>
  <h4 id="VRageMath_BoundingBox2D_Area" data-uid="VRageMath.BoundingBox2D.Area">Area()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Area()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Contains_" data-uid="VRageMath.BoundingBox2D.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2D_Contains_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.Contains(VRageMath.BoundingBox2D)">Contains(BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2D contains another BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2D to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Contains_" data-uid="VRageMath.BoundingBox2D.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2D_Contains_VRageMath_BoundingBox2D__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBox2D.Contains(VRageMath.BoundingBox2D@,VRageMath.ContainmentType@)">Contains(ref BoundingBox2D, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2D contains a BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBox2D box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2D to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Contains_" data-uid="VRageMath.BoundingBox2D.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2D_Contains_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Contains(VRageMath.Vector2D)">Contains(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2D contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector2D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Contains_" data-uid="VRageMath.BoundingBox2D.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2D_Contains_VRageMath_Vector2D__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBox2D.Contains(VRageMath.Vector2D@,VRageMath.ContainmentType@)">Contains(ref Vector2D, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2D contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector2D point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateFromHalfExtent_" data-uid="VRageMath.BoundingBox2D.CreateFromHalfExtent*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateFromHalfExtent_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.BoundingBox2D.CreateFromHalfExtent(VRageMath.Vector2D,System.Double)">CreateFromHalfExtent(Vector2D, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2D CreateFromHalfExtent(Vector2D center, double halfExtent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">halfExtent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateFromHalfExtent_" data-uid="VRageMath.BoundingBox2D.CreateFromHalfExtent*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateFromHalfExtent_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.CreateFromHalfExtent(VRageMath.Vector2D,VRageMath.Vector2D)">CreateFromHalfExtent(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2D CreateFromHalfExtent(Vector2D center, Vector2D halfExtent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">halfExtent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateFromPoints_" data-uid="VRageMath.BoundingBox2D.CreateFromPoints*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateFromPoints_System_Collections_Generic_IEnumerable_VRageMath_Vector2D__" data-uid="VRageMath.BoundingBox2D.CreateFromPoints(System.Collections.Generic.IEnumerable{VRageMath.Vector2D})">CreateFromPoints(IEnumerable&lt;Vector2D&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2D that will contain a group of points.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2D CreateFromPoints(IEnumerable&lt;Vector2D&gt; points)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>&gt;</td>
        <td><span class="parametername">points</span></td>
        <td><p>A list of points the BoundingBox2D should contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateInvalid_" data-uid="VRageMath.BoundingBox2D.CreateInvalid*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateInvalid" data-uid="VRageMath.BoundingBox2D.CreateInvalid">CreateInvalid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2D CreateInvalid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateMerged_" data-uid="VRageMath.BoundingBox2D.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateMerged_VRageMath_BoundingBox2D_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.CreateMerged(VRageMath.BoundingBox2D,VRageMath.BoundingBox2D)">CreateMerged(BoundingBox2D, BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2D that contains the two specified BoundingBox2D instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2D CreateMerged(BoundingBox2D original, BoundingBox2D additional)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBox2Ds to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBox2Ds to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_CreateMerged_" data-uid="VRageMath.BoundingBox2D.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBox2D_CreateMerged_VRageMath_BoundingBox2D__VRageMath_BoundingBox2D__VRageMath_BoundingBox2D__" data-uid="VRageMath.BoundingBox2D.CreateMerged(VRageMath.BoundingBox2D@,VRageMath.BoundingBox2D@,VRageMath.BoundingBox2D@)">CreateMerged(ref BoundingBox2D, ref BoundingBox2D, out BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2D that contains the two specified BoundingBox2D instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateMerged(ref BoundingBox2D original, ref BoundingBox2D additional, out BoundingBox2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBox2D instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBox2D instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBox2D.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Distance_" data-uid="VRageMath.BoundingBox2D.Distance*"></a>
  <h4 id="VRageMath_BoundingBox2D_Distance_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Distance(VRageMath.Vector2D)">Distance(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Distance(Vector2D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Equals_" data-uid="VRageMath.BoundingBox2D.Equals*"></a>
  <h4 id="VRageMath_BoundingBox2D_Equals_System_Object_" data-uid="VRageMath.BoundingBox2D.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2D are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingBox2D.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Equals_" data-uid="VRageMath.BoundingBox2D.Equals*"></a>
  <h4 id="VRageMath_BoundingBox2D_Equals_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.Equals(VRageMath.BoundingBox2D)">Equals(BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2D are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingBox2D other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingBox2D to compare with the current BoundingBox2D.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_GetCorners_" data-uid="VRageMath.BoundingBox2D.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBox2D_GetCorners" data-uid="VRageMath.BoundingBox2D.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_GetCorners_" data-uid="VRageMath.BoundingBox2D.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBox2D_GetCorners_VRageMath_Vector2D___" data-uid="VRageMath.BoundingBox2D.GetCorners(VRageMath.Vector2D[])">GetCorners(Vector2D[])</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector2D[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector2D points where the corners of the BoundingBox2D are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_GetCornersUnsafe_" data-uid="VRageMath.BoundingBox2D.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingBox2D_GetCornersUnsafe_VRageMath_Vector2D__" data-uid="VRageMath.BoundingBox2D.GetCornersUnsafe(VRageMath.Vector2D*)">GetCornersUnsafe(Vector2D*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector2D*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector2D points where the corners of the BoundingBox2D are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_GetHashCode_" data-uid="VRageMath.BoundingBox2D.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingBox2D_GetHashCode" data-uid="VRageMath.BoundingBox2D.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_GetIncluded_" data-uid="VRageMath.BoundingBox2D.GetIncluded*"></a>
  <h4 id="VRageMath_BoundingBox2D_GetIncluded_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.GetIncluded(VRageMath.Vector2D)">GetIncluded(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D GetIncluded(Vector2D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.BoundingBox2D)">Include(BoundingBox2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_BoundingBox2D__" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.BoundingBox2D@)">Include(ref BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(ref BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.Vector2D)">Include(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(Vector2D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D)">Include(Vector2D, Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(Vector2D p0, Vector2D p1, Vector2D p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_Vector2D__" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.Vector2D@)">Include(ref Vector2D)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(ref Vector2D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Include_" data-uid="VRageMath.BoundingBox2D.Include*"></a>
  <h4 id="VRageMath_BoundingBox2D_Include_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.BoundingBox2D.Include(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Include(ref Vector2D, ref Vector2D, ref Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Include(ref Vector2D p0, ref Vector2D p1, ref Vector2D p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Inflate_" data-uid="VRageMath.BoundingBox2D.Inflate*"></a>
  <h4 id="VRageMath_BoundingBox2D_Inflate_System_Double_" data-uid="VRageMath.BoundingBox2D.Inflate(System.Double)">Inflate(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Inflate(double size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_InflateToMinimum_" data-uid="VRageMath.BoundingBox2D.InflateToMinimum*"></a>
  <h4 id="VRageMath_BoundingBox2D_InflateToMinimum_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.InflateToMinimum(VRageMath.Vector2D)">InflateToMinimum(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InflateToMinimum(Vector2D minimumSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">minimumSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Intersect_" data-uid="VRageMath.BoundingBox2D.Intersect*"></a>
  <h4 id="VRageMath_BoundingBox2D_Intersect_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.Intersect(VRageMath.BoundingBox2D)">Intersect(BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Returns bounding box which is intersection of this and box
It's called 'Prunik'
Result is invalid box when there's no intersection (Min &gt; Max)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Intersect(BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Intersects_" data-uid="VRageMath.BoundingBox2D.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2D_Intersects_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.Intersects(VRageMath.BoundingBox2D)">Intersects(BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox2D intersects another BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2D to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Intersects_" data-uid="VRageMath.BoundingBox2D.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2D_Intersects_VRageMath_BoundingBox2D__" data-uid="VRageMath.BoundingBox2D.Intersects(VRageMath.BoundingBox2D@)">Intersects(ref BoundingBox2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingBox2D box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Intersects_" data-uid="VRageMath.BoundingBox2D.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2D_Intersects_VRageMath_BoundingBox2D__System_Boolean__" data-uid="VRageMath.BoundingBox2D.Intersects(VRageMath.BoundingBox2D@,System.Boolean@)">Intersects(ref BoundingBox2D, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox2D intersects another BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox2D box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2D to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingBox2D instances intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Perimeter_" data-uid="VRageMath.BoundingBox2D.Perimeter*"></a>
  <h4 id="VRageMath_BoundingBox2D_Perimeter" data-uid="VRageMath.BoundingBox2D.Perimeter">Perimeter()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Perimeter()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Scale_" data-uid="VRageMath.BoundingBox2D.Scale*"></a>
  <h4 id="VRageMath_BoundingBox2D_Scale_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Scale(VRageMath.Vector2D)">Scale(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Scale(Vector2D scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_ToString_" data-uid="VRageMath.BoundingBox2D.ToString*"></a>
  <h4 id="VRageMath_BoundingBox2D_ToString" data-uid="VRageMath.BoundingBox2D.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingBox2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_Translate_" data-uid="VRageMath.BoundingBox2D.Translate*"></a>
  <h4 id="VRageMath_BoundingBox2D_Translate_VRageMath_Vector2D_" data-uid="VRageMath.BoundingBox2D.Translate(VRageMath.Vector2D)">Translate(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Translate</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2D Translate(Vector2D vctTranlsation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vctTranlsation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingBox2D_op_Equality_" data-uid="VRageMath.BoundingBox2D.op_Equality*"></a>
  <h4 id="VRageMath_BoundingBox2D_op_Equality_VRageMath_BoundingBox2D_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.op_Equality(VRageMath.BoundingBox2D,VRageMath.BoundingBox2D)">Equality(BoundingBox2D, BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2D are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingBox2D a, BoundingBox2D b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>BoundingBox2D to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>BoundingBox2D to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2D_op_Inequality_" data-uid="VRageMath.BoundingBox2D.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingBox2D_op_Inequality_VRageMath_BoundingBox2D_VRageMath_BoundingBox2D_" data-uid="VRageMath.BoundingBox2D.op_Inequality(VRageMath.BoundingBox2D,VRageMath.BoundingBox2D)">Inequality(BoundingBox2D, BoundingBox2D)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2D are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingBox2D a, BoundingBox2D b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
