﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.Game.ObjectBuilders
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.Game.ObjectBuilders
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.ObjectBuilders">
  
  <h1 id="VRage_Game_ObjectBuilders" data-uid="VRage.Game.ObjectBuilders" class="text-break">Namespace VRage.Game.ObjectBuilders
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.GunObjectBuilderExtensions.html">GunObjectBuilderExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyComponentBlockEntry.html">MyComponentBlockEntry</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyGroupedIds.html">MyGroupedIds</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyGroupedIds.GroupedId.html">MyGroupedIds.GroupedId</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyMappedId.html">MyMappedId</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationControllerDefinition.html">MyObjectBuilder_AnimationControllerDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationFootIkChain.html">MyObjectBuilder_AnimationFootIkChain</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationLayer.html">MyObjectBuilder_AnimationLayer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationLayer.MyLayerMode.html">MyObjectBuilder_AnimationLayer.MyLayerMode</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSM.html">MyObjectBuilder_AnimationSM</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMCondition.html">MyObjectBuilder_AnimationSMCondition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMCondition.MyOperationType.html">MyObjectBuilder_AnimationSMCondition.MyOperationType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMConditionsConjunction.html">MyObjectBuilder_AnimationSMConditionsConjunction</a></h4>
      <section><p>Conjunction of several simple conditions. This conjunction is true if all contained conditions are true.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMNode.html">MyObjectBuilder_AnimationSMNode</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMNode.MySMNodeType.html">MyObjectBuilder_AnimationSMNode.MySMNodeType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMTransition.html">MyObjectBuilder_AnimationSMTransition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTree.html">MyObjectBuilder_AnimationTree</a></h4>
      <section><p>Root node of the whole animation tree. Supports storing of orphaned nodes.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNode.html">MyObjectBuilder_AnimationTreeNode</a></h4>
      <section><p>Base class of all object builders of animation tree nodes.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeAdd.html">MyObjectBuilder_AnimationTreeNodeAdd</a></h4>
      <section><p>Additive node. Child nodes are base node + additive node.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeDynamicTrack.html">MyObjectBuilder_AnimationTreeNodeDynamicTrack</a></h4>
      <section><p>Track node, playing track given from action</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeIkTarget.html">MyObjectBuilder_AnimationTreeNodeIkTarget</a></h4>
      <section><p>Track node, storing information about track and playing settings.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeMix1D.html">MyObjectBuilder_AnimationTreeNodeMix1D</a></h4>
      <section><p>Linear mixing node. Maps child nodes on 1D axis, interpolates according to parameter value.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeSetter.html">MyObjectBuilder_AnimationTreeNodeSetter</a></h4>
      <section><p>Setter node, storing information about timed variable setting.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeSetter.ValueAssignment.html">MyObjectBuilder_AnimationTreeNodeSetter.ValueAssignment</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNodeTrack.html">MyObjectBuilder_AnimationTreeNodeTrack</a></h4>
      <section><p>Track node, storing information about track and playing settings.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_CampaignSessionComponent.html">MyObjectBuilder_CampaignSessionComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_CargoContainerInventoryBagEntity.html">MyObjectBuilder_CargoContainerInventoryBagEntity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EntityStat.html">MyObjectBuilder_EntityStat</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EntityStatRegenEffect.html">MyObjectBuilder_EntityStatRegenEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EnvironmentalParticleLogic.html">MyObjectBuilder_EnvironmentalParticleLogic</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EnvironmentalParticleLogicFireFly.html">MyObjectBuilder_EnvironmentalParticleLogicFireFly</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EnvironmentalParticleLogicGrassland.html">MyObjectBuilder_EnvironmentalParticleLogicGrassland</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EnvironmentalParticleLogicSpace.html">MyObjectBuilder_EnvironmentalParticleLogicSpace</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_ForageableEntity.html">MyObjectBuilder_ForageableEntity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_InventoryBagEntity.html">MyObjectBuilder_InventoryBagEntity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_Localization.html">MyObjectBuilder_Localization</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_Localization.KeyEntry.html">MyObjectBuilder_Localization.KeyEntry</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_ModifiableEntity.html">MyObjectBuilder_ModifiableEntity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_NeutralShipSpawner.html">MyObjectBuilder_NeutralShipSpawner</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_NeutralShipSpawner.ShipTimePair.html">MyObjectBuilder_NeutralShipSpawner.ShipTimePair</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_PlanetMapProvider.html">MyObjectBuilder_PlanetMapProvider</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_PlanetTextureMapProvider.html">MyObjectBuilder_PlanetTextureMapProvider</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_RadialMenu.html">MyObjectBuilder_RadialMenu</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_RadialMenuItemVoxelHand.html">MyObjectBuilder_RadialMenuItemVoxelHand</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_RadialMenuItemVoxelHandBrush.html">MyObjectBuilder_RadialMenuItemVoxelHandBrush</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_SchematicItem.html">MyObjectBuilder_SchematicItem</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_SectorWeatherComponent.html">MyObjectBuilder_SectorWeatherComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_SkinInventory.html">MyObjectBuilder_SkinInventory</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_ToolbarItemUsable.html">MyObjectBuilder_ToolbarItemUsable</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_UsableItem.html">MyObjectBuilder_UsableItem</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MyParameterAnimTreeNodeMapping.html">MyParameterAnimTreeNodeMapping</a></h4>
      <section><p>Helper struct: parameter mapping.</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.MySerializedTextPanelData.html">MySerializedTextPanelData</a></h4>
      <section></section>
    <h3 id="interfaces">Interfaces
  </h3>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.IMyObjectBuilder_GunObject-1.html">IMyObjectBuilder_GunObject&lt;T&gt;</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
