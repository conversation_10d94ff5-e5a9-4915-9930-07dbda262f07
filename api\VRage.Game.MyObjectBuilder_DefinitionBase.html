﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_DefinitionBase
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_DefinitionBase
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_DefinitionBase" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase" class="text-break">Class MyObjectBuilder_DefinitionBase
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><span class="xref">MyObjectBuilder_DefinitionBase</span></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.Definitions.MyObjectBuilder_ExhaustEffectDefinition.html">MyObjectBuilder_ExhaustEffectDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AiCommandDefinition.html">MyObjectBuilder_AiCommandDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AmmoDefinition.html">MyObjectBuilder_AmmoDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AnimationDefinition.html">MyObjectBuilder_AnimationDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AssetModifierDefinition.html">MyObjectBuilder_AssetModifierDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AsteroidGeneratorDefinition.html">MyObjectBuilder_AsteroidGeneratorDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AudioDefinition.html">MyObjectBuilder_AudioDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_AudioEffectDefinition.html">MyObjectBuilder_AudioEffectDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_BehaviorTreeDefinition.html">MyObjectBuilder_BehaviorTreeDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_BlueprintClassDefinition.html">MyObjectBuilder_BlueprintClassDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_BlueprintDefinition.html">MyObjectBuilder_BlueprintDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html">MyObjectBuilder_BotDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CharacterDefinition.html">MyObjectBuilder_CharacterDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ComponentDefinitionBase.html">MyObjectBuilder_ComponentDefinitionBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ComponentGroupDefinition.html">MyObjectBuilder_ComponentGroupDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CompositeBlueprintDefinition.html">MyObjectBuilder_CompositeBlueprintDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ContainerDefinition.html">MyObjectBuilder_ContainerDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ContainerTypeDefinition.html">MyObjectBuilder_ContainerTypeDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ControllerSchemaDefinition.html">MyObjectBuilder_ControllerSchemaDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockTagDefinition.html">MyObjectBuilder_CubeBlockTagDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CurveDefinition.html">MyObjectBuilder_CurveDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CuttingDefinition.html">MyObjectBuilder_CuttingDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DebrisDefinition.html">MyObjectBuilder_DebrisDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DecalDefinition.html">MyObjectBuilder_DecalDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DecalGlobalsDefinition.html">MyObjectBuilder_DecalGlobalsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionsToPreload.html">MyObjectBuilder_DefinitionsToPreload</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DropContainerDefinition.html">MyObjectBuilder_DropContainerDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EdgesDefinition.html">MyObjectBuilder_EdgesDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EmissiveColorDefinition.html">MyObjectBuilder_EmissiveColorDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EmissiveColorStatePresetDefinition.html">MyObjectBuilder_EmissiveColorStatePresetDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EntityStatDefinition.html">MyObjectBuilder_EntityStatDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EnvironmentDefinition.html">MyObjectBuilder_EnvironmentDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EnvironmentItemsDefinition.html">MyObjectBuilder_EnvironmentItemsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_FlareDefinition.html">MyObjectBuilder_FlareDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_FontDefinition.html">MyObjectBuilder_FontDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_GlobalEventDefinition.html">MyObjectBuilder_GlobalEventDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_GridCreateToolDefinition.html">MyObjectBuilder_GridCreateToolDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiBlockCategoryDefinition.html">MyObjectBuilder_GuiBlockCategoryDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_HandItemDefinition.html">MyObjectBuilder_HandItemDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_LCDTextureDefinition.html">MyObjectBuilder_LCDTextureDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_MaterialPropertiesDefinition.html">MyObjectBuilder_MaterialPropertiesDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ParticleEffect.html">MyObjectBuilder_ParticleEffect</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalItemDefinition.html">MyObjectBuilder_PhysicalItemDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalMaterialDefinition.html">MyObjectBuilder_PhysicalMaterialDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelCollectionDefinition.html">MyObjectBuilder_PhysicalModelCollectionDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html">MyObjectBuilder_PhysicalModelDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PlanetGeneratorDefinition.html">MyObjectBuilder_PlanetGeneratorDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PlanetPrefabDefinition.html">MyObjectBuilder_PlanetPrefabDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PrefabDefinition.html">MyObjectBuilder_PrefabDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_RespawnShipDefinition.html">MyObjectBuilder_RespawnShipDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ScenarioDefinition.html">MyObjectBuilder_ScenarioDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ShadowTextureSetDefinition.html">MyObjectBuilder_ShadowTextureSetDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_SoundCategoryDefinition.html">MyObjectBuilder_SoundCategoryDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_SpawnGroupDefinition.html">MyObjectBuilder_SpawnGroupDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_StoryCategoryDefinition.html">MyObjectBuilder_StoryCategoryDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_StoryDefinition.html">MyObjectBuilder_StoryDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_TargetingGroupDefinition.html">MyObjectBuilder_TargetingGroupDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_TransparentMaterialDefinition.html">MyObjectBuilder_TransparentMaterialDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VisualSettingsDefinition.html">MyObjectBuilder_VisualSettingsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VoxelHandDefinition.html">MyObjectBuilder_VoxelHandDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VoxelMapStorageDefinition.html">MyObjectBuilder_VoxelMapStorageDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VoxelMaterialDefinition.html">MyObjectBuilder_VoxelMaterialDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VoxelMaterialModifierDefinition.html">MyObjectBuilder_VoxelMaterialModifierDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_WeaponDefinition.html">MyObjectBuilder_WeaponDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.html">MyObjectBuilder_WeatherEffectDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_WheelModelsDefinition.html">MyObjectBuilder_WheelModelsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_BlockVariantGroup.html">MyObjectBuilder_BlockVariantGroup</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeDefinition.html">MyObjectBuilder_ContractTypeDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControllerSchemeDefinition.html">MyObjectBuilder_ControllerSchemeDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DebugScreenSearchCacheDefinition.html">MyObjectBuilder_DebugScreenSearchCacheDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DlcDefinition.html">MyObjectBuilder_DlcDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DroneBehaviorDefinition.html">MyObjectBuilder_DroneBehaviorDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_EmoteDefinition.html">MyObjectBuilder_EmoteDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionDefinition.html">MyObjectBuilder_FactionDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionNameDefinition.html">MyObjectBuilder_FactionNameDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionTypeDefinition.html">MyObjectBuilder_FactionTypeDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GameDefinition.html">MyObjectBuilder_GameDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GasProperties.html">MyObjectBuilder_GasProperties</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GhostCharacterDefinition.html">MyObjectBuilder_GhostCharacterDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GpsCollectionDefinition.html">MyObjectBuilder_GpsCollectionDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GuiTextureAtlasDefinition.html">MyObjectBuilder_GuiTextureAtlasDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_HudDefinition.html">MyObjectBuilder_HudDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_MainMenuInventorySceneDefinition.html">MyObjectBuilder_MainMenuInventorySceneDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_OffensiveWords.html">MyObjectBuilder_OffensiveWords</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_PirateAntennaDefinition.html">MyObjectBuilder_PirateAntennaDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_QuickStartDefinition.html">MyObjectBuilder_QuickStartDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchBlockDefinition.html">MyObjectBuilder_ResearchBlockDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchDefinition.html">MyObjectBuilder_ResearchDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchGroupDefinition.html">MyObjectBuilder_ResearchGroupDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResourceDistributionGroup.html">MyObjectBuilder_ResourceDistributionGroup</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SafeZoneSettingsDefinition.html">MyObjectBuilder_SafeZoneSettingsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SafeZoneTexturesDefinition.html">MyObjectBuilder_SafeZoneTexturesDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ScenarioCategoryDefinition.html">MyObjectBuilder_ScenarioCategoryDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SessionComponentDefinition.html">MyObjectBuilder_SessionComponentDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.html">MyObjectBuilder_ShipSoundsDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundSystemDefinition.html">MyObjectBuilder_ShipSoundSystemDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StationsListDefinition.html">MyObjectBuilder_StationsListDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationControllerDefinition.html">MyObjectBuilder_AnimationControllerDefinition</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_RadialMenu.html">MyObjectBuilder_RadialMenu</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_DefinitionBase_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public abstract class MyObjectBuilder_DefinitionBase : MyObjectBuilder_Base</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_DefinitionBase__ctor_" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase__ctor" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.#ctor">MyObjectBuilder_DefinitionBase()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected MyObjectBuilder_DefinitionBase()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_AvailableInSurvival" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.AvailableInSurvival">AvailableInSurvival</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AvailableInSurvival</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_Description" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.Description">Description</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Description</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_DescriptionArgs" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.DescriptionArgs">DescriptionArgs</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string DescriptionArgs</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_DisplayName" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.DisplayName">DisplayName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string DisplayName</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_DLCs" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.DLCs">DLCs</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string[] DLCs</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_Enabled" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.Enabled">Enabled</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Enabled</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_Icons" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.Icons">Icons</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[ModdableContentFile(new string[]{&quot;dds&quot;, &quot;png&quot;})]
public string[] Icons</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_Id" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.Id">Id</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableDefinitionId Id</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.SerializableDefinitionId.html">SerializableDefinitionId</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_DefinitionBase_Public" data-uid="VRage.Game.MyObjectBuilder_DefinitionBase.Public">Public</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Public</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
