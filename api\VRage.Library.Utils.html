﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.Library.Utils
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.Library.Utils
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Library.Utils">
  
  <h1 id="VRage_Library_Utils" data-uid="VRage.Library.Utils" class="text-break">Namespace VRage.Library.Utils
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.Library.Utils.BlittableHelper-1.html">BlittableHelper&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.DebugUtils.html">DebugUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.Disposable.html">Disposable</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.DontCheckAttribute.html">DontCheckAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.InterpolationHandler-1.html">InterpolationHandler&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyDefaultLogInject.html">MyDefaultLogInject</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyEnum-1.html">MyEnum&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyEnum-1.Range.html">MyEnum&lt;T&gt;.Range</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyGameModeEnum.html">MyGameModeEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyGameTimer.html">MyGameTimer</a></h4>
      <section><p>Global thread-safe timer.
Time for update and time for draw must be copied at the beginning of update and draw.</p>
</section>
      <h4><a class="xref" href="VRage.Library.Utils.MyHashRandomUtils.html">MyHashRandomUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyImageHeaderUtils.html">MyImageHeaderUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyImageHeaderUtils.DDS_HEADER.html">MyImageHeaderUtils.DDS_HEADER</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyImageHeaderUtils.DDS_PIXELFORMAT.html">MyImageHeaderUtils.DDS_PIXELFORMAT</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyInterpolationQueue-1.html">MyInterpolationQueue&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html">MyIterableComplementSetExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyLibraryUtils.html">MyLibraryUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyRandom.html">MyRandom</a></h4>
      <section><p>Original C# implementation which allows settings the seed.</p>
</section>
      <h4><a class="xref" href="VRage.Library.Utils.MyRandom.State.html">MyRandom.State</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyRandom.StateToken.html">MyRandom.StateToken</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></h4>
      <section><p>Hi-resolution time span. Beware: the resolution can be different on different systems!</p>
</section>
      <h4><a class="xref" href="VRage.Library.Utils.MyValueAggregator.html">MyValueAggregator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.PathUtils.html">PathUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Library.Utils.WaitForTargetFrameRate.html">WaitForTargetFrameRate</a></h4>
      <section></section>
    <h3 id="interfaces">Interfaces
  </h3>
      <h4><a class="xref" href="VRage.Library.Utils.IMyCondition.html">IMyCondition</a></h4>
      <section><p>Interface of totally generic condition.</p>
</section>
      <h4><a class="xref" href="VRage.Library.Utils.IMyCondition-1.html">IMyCondition&lt;T&gt;</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
