﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyOrientedBoundingBox
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyOrientedBoundingBox
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.MyOrientedBoundingBox">
  
  
  <h1 id="VRageMath_MyOrientedBoundingBox" data-uid="VRageMath.MyOrientedBoundingBox" class="text-break">Class MyOrientedBoundingBox
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyOrientedBoundingBox</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_MyOrientedBoundingBox_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MyOrientedBoundingBox : ValueType, IEquatable&lt;MyOrientedBoundingBox&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_MyOrientedBoundingBox__ctor_" data-uid="VRageMath.MyOrientedBoundingBox.#ctor*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox__ctor_VRageMath_Matrix__" data-uid="VRageMath.MyOrientedBoundingBox.#ctor(VRageMath.Matrix@)">MyOrientedBoundingBox(ref Matrix)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of the MyOrientedBoundingBox.
Scale of matrix is size of box</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyOrientedBoundingBox(ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox__ctor_" data-uid="VRageMath.MyOrientedBoundingBox.#ctor*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox__ctor_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Quaternion_" data-uid="VRageMath.MyOrientedBoundingBox.#ctor(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Quaternion)">MyOrientedBoundingBox(Vector3, Vector3, Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyOrientedBoundingBox(Vector3 center, Vector3 halfExtents, Quaternion orientation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfExtents</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_Center" data-uid="VRageMath.MyOrientedBoundingBox.Center">Center</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Center</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_CornerCount" data-uid="VRageMath.MyOrientedBoundingBox.CornerCount">CornerCount</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int CornerCount = 8</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_EndVertices" data-uid="VRageMath.MyOrientedBoundingBox.EndVertices">EndVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] EndVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_EndXVertices" data-uid="VRageMath.MyOrientedBoundingBox.EndXVertices">EndXVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] EndXVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_EndYVertices" data-uid="VRageMath.MyOrientedBoundingBox.EndYVertices">EndYVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] EndYVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_EndZVertices" data-uid="VRageMath.MyOrientedBoundingBox.EndZVertices">EndZVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] EndZVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_HalfExtent" data-uid="VRageMath.MyOrientedBoundingBox.HalfExtent">HalfExtent</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 HalfExtent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_Orientation" data-uid="VRageMath.MyOrientedBoundingBox.Orientation">Orientation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Quaternion Orientation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_StartVertices" data-uid="VRageMath.MyOrientedBoundingBox.StartVertices">StartVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] StartVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_StartXVertices" data-uid="VRageMath.MyOrientedBoundingBox.StartXVertices">StartXVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] StartXVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_StartYVertices" data-uid="VRageMath.MyOrientedBoundingBox.StartYVertices">StartYVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] StartYVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_StartZVertices" data-uid="VRageMath.MyOrientedBoundingBox.StartZVertices">StartZVertices</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int[] StartZVertices</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_XNeighbourVectorsBack" data-uid="VRageMath.MyOrientedBoundingBox.XNeighbourVectorsBack">XNeighbourVectorsBack</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] XNeighbourVectorsBack</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_XNeighbourVectorsForw" data-uid="VRageMath.MyOrientedBoundingBox.XNeighbourVectorsForw">XNeighbourVectorsForw</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] XNeighbourVectorsForw</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_YNeighbourVectorsBack" data-uid="VRageMath.MyOrientedBoundingBox.YNeighbourVectorsBack">YNeighbourVectorsBack</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] YNeighbourVectorsBack</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_YNeighbourVectorsForw" data-uid="VRageMath.MyOrientedBoundingBox.YNeighbourVectorsForw">YNeighbourVectorsForw</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] YNeighbourVectorsForw</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_ZNeighbourVectorsBack" data-uid="VRageMath.MyOrientedBoundingBox.ZNeighbourVectorsBack">ZNeighbourVectorsBack</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] ZNeighbourVectorsBack</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MyOrientedBoundingBox_ZNeighbourVectorsForw" data-uid="VRageMath.MyOrientedBoundingBox.ZNeighbourVectorsForw">ZNeighbourVectorsForw</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3[] ZNeighbourVectorsForw</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingBox@)">Contains(ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(ref BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingBox__VRageMath_MyOrientedBoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingBox@,VRageMath.MyOrientedBoundingBox@)">Contains(ref BoundingBox, ref MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ContainmentType Contains(ref BoundingBox boxA, ref MyOrientedBoundingBox oboxB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">boxA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">oboxB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingFrustum_" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingFrustum)">Contains(BoundingFrustum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingFrustum_VRageMath_MyOrientedBoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingFrustum,VRageMath.MyOrientedBoundingBox@)">Contains(BoundingFrustum, ref MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ContainmentType Contains(BoundingFrustum frustum, ref MyOrientedBoundingBox obox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">obox</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingSphere__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingSphere@)">Contains(ref BoundingSphere)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(ref BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_BoundingSphere__VRageMath_MyOrientedBoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.BoundingSphere@,VRageMath.MyOrientedBoundingBox@)">Contains(ref BoundingSphere, ref MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ContainmentType Contains(ref BoundingSphere sphere, ref MyOrientedBoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_MyOrientedBoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.MyOrientedBoundingBox@)">Contains(ref MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(ref MyOrientedBoundingBox other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Contains_" data-uid="VRageMath.MyOrientedBoundingBox.Contains*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Contains_VRageMath_Vector3__" data-uid="VRageMath.MyOrientedBoundingBox.Contains(VRageMath.Vector3@)">Contains(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(ref Vector3 point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_ContainsRelativeBox_" data-uid="VRageMath.MyOrientedBoundingBox.ContainsRelativeBox*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_ContainsRelativeBox_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.MyOrientedBoundingBox.ContainsRelativeBox(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Matrix@)">ContainsRelativeBox(ref Vector3, ref Vector3, ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static ContainmentType ContainsRelativeBox(ref Vector3 hA, ref Vector3 hB, ref Matrix mB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">hA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">hB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">mB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_ConvertToFrustum_" data-uid="VRageMath.MyOrientedBoundingBox.ConvertToFrustum*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_ConvertToFrustum" data-uid="VRageMath.MyOrientedBoundingBox.ConvertToFrustum">ConvertToFrustum()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingFrustum ConvertToFrustum()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Create_" data-uid="VRageMath.MyOrientedBoundingBox.Create*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Create_VRageMath_BoundingBox_VRageMath_Matrix_" data-uid="VRageMath.MyOrientedBoundingBox.Create(VRageMath.BoundingBox,VRageMath.Matrix)">Create(BoundingBox, Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyOrientedBoundingBox Create(BoundingBox boundingBox, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_CreateFromBoundingBox_" data-uid="VRageMath.MyOrientedBoundingBox.CreateFromBoundingBox*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_CreateFromBoundingBox_VRageMath_BoundingBox_" data-uid="VRageMath.MyOrientedBoundingBox.CreateFromBoundingBox(VRageMath.BoundingBox)">CreateFromBoundingBox(BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyOrientedBoundingBox CreateFromBoundingBox(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Equals_" data-uid="VRageMath.MyOrientedBoundingBox.Equals*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Equals_System_Object_" data-uid="VRageMath.MyOrientedBoundingBox.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Equals_" data-uid="VRageMath.MyOrientedBoundingBox.Equals*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Equals_VRageMath_MyOrientedBoundingBox_" data-uid="VRageMath.MyOrientedBoundingBox.Equals(VRageMath.MyOrientedBoundingBox)">Equals(MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(MyOrientedBoundingBox other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_GetAABB_" data-uid="VRageMath.MyOrientedBoundingBox.GetAABB*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_GetAABB" data-uid="VRageMath.MyOrientedBoundingBox.GetAABB">GetAABB()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox GetAABB()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_GetCorners_" data-uid="VRageMath.MyOrientedBoundingBox.GetCorners*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_GetCorners_VRageMath_Vector3___System_Int32_" data-uid="VRageMath.MyOrientedBoundingBox.GetCorners(VRageMath.Vector3[],System.Int32)">GetCorners(Vector3[], Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector3[] corners, int startIndex)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">startIndex</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_GetHashCode_" data-uid="VRageMath.MyOrientedBoundingBox.GetHashCode*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_GetHashCode" data-uid="VRageMath.MyOrientedBoundingBox.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_GetNormalBetweenEdges_" data-uid="VRageMath.MyOrientedBoundingBox.GetNormalBetweenEdges*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_GetNormalBetweenEdges_System_Int32_System_Int32_System_Int32_VRageMath_Vector3__" data-uid="VRageMath.MyOrientedBoundingBox.GetNormalBetweenEdges(System.Int32,System.Int32,System.Int32,VRageMath.Vector3@)">GetNormalBetweenEdges(Int32, Int32, Int32, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns normal between two cube edge of same direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetNormalBetweenEdges(int axis, int edge0, int edge1, out Vector3 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>Edge direction axis (0 = X, 1 = Y, 2 = Z)</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">edge0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">edge1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>false if edges are not neighbors</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_BoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.BoundingBox@)">Intersects(ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_BoundingFrustum_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.BoundingFrustum)">Intersects(BoundingFrustum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_BoundingSphere__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.BoundingSphere@)">Intersects(ref BoundingSphere)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_Line__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.Line@)">Intersects(ref Line)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(ref Line line)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_MyOrientedBoundingBox__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.MyOrientedBoundingBox@)">Intersects(ref MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref MyOrientedBoundingBox other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_Plane__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.Plane@)">Intersects(ref Plane)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(ref Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Intersects_" data-uid="VRageMath.MyOrientedBoundingBox.Intersects*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Intersects_VRageMath_Ray__" data-uid="VRageMath.MyOrientedBoundingBox.Intersects(VRageMath.Ray@)">Intersects(ref Ray)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(ref Ray ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_ToString_" data-uid="VRageMath.MyOrientedBoundingBox.ToString*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_ToString" data-uid="VRageMath.MyOrientedBoundingBox.ToString">ToString()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Transform_" data-uid="VRageMath.MyOrientedBoundingBox.Transform*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Transform_System_Single_VRageMath_Quaternion_VRageMath_Vector3_" data-uid="VRageMath.MyOrientedBoundingBox.Transform(System.Single,VRageMath.Quaternion,VRageMath.Vector3)">Transform(Single, Quaternion, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyOrientedBoundingBox Transform(float scale, Quaternion rotation, Vector3 translation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">translation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Transform_" data-uid="VRageMath.MyOrientedBoundingBox.Transform*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Transform_VRageMath_Matrix_" data-uid="VRageMath.MyOrientedBoundingBox.Transform(VRageMath.Matrix)">Transform(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Transform(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_Transform_" data-uid="VRageMath.MyOrientedBoundingBox.Transform*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_Transform_VRageMath_Quaternion_VRageMath_Vector3_" data-uid="VRageMath.MyOrientedBoundingBox.Transform(VRageMath.Quaternion,VRageMath.Vector3)">Transform(Quaternion, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyOrientedBoundingBox Transform(Quaternion rotation, Vector3 translation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">translation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_op_Equality_" data-uid="VRageMath.MyOrientedBoundingBox.op_Equality*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_op_Equality_VRageMath_MyOrientedBoundingBox_VRageMath_MyOrientedBoundingBox_" data-uid="VRageMath.MyOrientedBoundingBox.op_Equality(VRageMath.MyOrientedBoundingBox,VRageMath.MyOrientedBoundingBox)">Equality(MyOrientedBoundingBox, MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(MyOrientedBoundingBox a, MyOrientedBoundingBox b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyOrientedBoundingBox_op_Inequality_" data-uid="VRageMath.MyOrientedBoundingBox.op_Inequality*"></a>
  <h4 id="VRageMath_MyOrientedBoundingBox_op_Inequality_VRageMath_MyOrientedBoundingBox_VRageMath_MyOrientedBoundingBox_" data-uid="VRageMath.MyOrientedBoundingBox.op_Inequality(VRageMath.MyOrientedBoundingBox,VRageMath.MyOrientedBoundingBox)">Inequality(MyOrientedBoundingBox, MyOrientedBoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(MyOrientedBoundingBox a, MyOrientedBoundingBox b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
