﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MySimpleObjectDraw
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MySimpleObjectDraw
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MySimpleObjectDraw">
  
  
  <h1 id="VRage_Game_MySimpleObjectDraw" data-uid="VRage.Game.MySimpleObjectDraw" class="text-break">Class MySimpleObjectDraw
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MySimpleObjectDraw</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MySimpleObjectDraw_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class MySimpleObjectDraw : Object</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MySimpleObjectDraw_ATTACHED_WIREFRAME_MAX_GRID_SIZE" data-uid="VRage.Game.MySimpleObjectDraw.ATTACHED_WIREFRAME_MAX_GRID_SIZE">ATTACHED_WIREFRAME_MAX_GRID_SIZE</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float ATTACHED_WIREFRAME_MAX_GRID_SIZE</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MySimpleObjectDraw_ATTACHED_WIREFRAME_MIN_GRID_SIZE" data-uid="VRage.Game.MySimpleObjectDraw.ATTACHED_WIREFRAME_MIN_GRID_SIZE">ATTACHED_WIREFRAME_MIN_GRID_SIZE</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float ATTACHED_WIREFRAME_MIN_GRID_SIZE</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawAttachedTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawAttachedTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawAttachedTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__System_UInt32_VRageMath_MatrixD__VRage_Game_MySimpleObjectRasterizer_System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_" data-uid="VRage.Game.MySimpleObjectDraw.DrawAttachedTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,System.UInt32,VRageMath.MatrixD@,VRage.Game.MySimpleObjectRasterizer,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean)">DrawAttachedTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, UInt32, ref MatrixD, MySimpleObjectRasterizer, Int32, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawAttachedTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, uint renderObjectID, ref MatrixD worldToLocal, MySimpleObjectRasterizer rasterization, int wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldToLocal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawAttachedTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawAttachedTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawAttachedTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__System_UInt32_VRageMath_MatrixD__VRage_Game_MySimpleObjectRasterizer_VRageMath_Vector3I_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MySimpleObjectDraw.DrawAttachedTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,System.UInt32,VRageMath.MatrixD@,VRage.Game.MySimpleObjectRasterizer,VRageMath.Vector3I,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,VRageRender.MyBillboard.BlendTypeEnum)">DrawAttachedTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, UInt32, ref MatrixD, MySimpleObjectRasterizer, Vector3I, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawAttachedTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, uint renderObjectID, ref MatrixD worldToLocal, MySimpleObjectRasterizer rasterization, Vector3I wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, MyBillboard.BlendTypeEnum blendType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldToLocal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawLine_" data-uid="VRage.Game.MySimpleObjectDraw.DrawLine*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawLine_VRageMath_Vector3D_VRageMath_Vector3D_System_Nullable_VRage_Utils_MyStringId__VRageMath_Vector4__System_Single_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MySimpleObjectDraw.DrawLine(VRageMath.Vector3D,VRageMath.Vector3D,System.Nullable{VRage.Utils.MyStringId},VRageMath.Vector4@,System.Single,VRageRender.MyBillboard.BlendTypeEnum)">DrawLine(Vector3D, Vector3D, Nullable&lt;MyStringId&gt;, ref Vector4, Single, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawLine(Vector3D start, Vector3D end, Nullable&lt;MyStringId&gt; material, ref Vector4 color, float thickness, MyBillboard.BlendTypeEnum blendtype)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">end</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendtype</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">DrawTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, MySimpleObjectRasterizer, Int32, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, MySimpleObjectRasterizer rasterization, int wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">DrawTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, ref Color, MySimpleObjectRasterizer, Int32, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, ref Color frontFaceColor, MySimpleObjectRasterizer rasterization, int wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">frontFaceColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_VRageMath_Vector3I_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,VRageMath.Vector3I,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">DrawTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, ref Color, MySimpleObjectRasterizer, Vector3I, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"><p>DrawTransparentBox</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, ref Color frontFaceColor, MySimpleObjectRasterizer rasterization, Vector3I wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">frontFaceColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">DrawTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, ref Color, ref Color, ref Color, ref Color, ref Color, ref Color, MySimpleObjectRasterizer, Int32, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color faceX_P, ref Color faceY_P, ref Color faceZ_P, ref Color faceX_N, ref Color faceY_N, ref Color faceZ_N, ref Color wire, MySimpleObjectRasterizer rasterization, int wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceX_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceY_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceZ_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceX_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceY_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceZ_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">wire</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentBox_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_VRageMath_Vector3I_System_Single_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentBox(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,VRageMath.Vector3I,System.Single,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">DrawTransparentBox(ref MatrixD, ref BoundingBoxD, ref Color, ref Color, ref Color, ref Color, ref Color, ref Color, ref Color, MySimpleObjectRasterizer, Vector3I, Single, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"><p>Definitely not thread safe due to use shared temporary_faces to avoid reinitializations</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentBox(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color faceX_P, ref Color faceY_P, ref Color faceZ_P, ref Color faceX_N, ref Color faceY_N, ref Color faceZ_N, ref Color wire, MySimpleObjectRasterizer rasterization, Vector3I wireDivideRatio, float lineWidth = 0.001F, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceX_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceY_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceZ_P</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceX_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceY_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">faceZ_N</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">wire</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentCapsule_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCapsule*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentCapsule_VRageMath_MatrixD__System_Single_System_Single_VRageMath_Color__System_Int32_System_Nullable_VRage_Utils_MyStringId__System_Int32_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCapsule(VRageMath.MatrixD@,System.Single,System.Single,VRageMath.Color@,System.Int32,System.Nullable{VRage.Utils.MyStringId},System.Int32,VRageRender.MyBillboard.BlendTypeEnum)">DrawTransparentCapsule(ref MatrixD, Single, Single, ref Color, Int32, Nullable&lt;MyStringId&gt;, Int32, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentCapsule(ref MatrixD worldMatrix, float radius, float height, ref Color color, int wireDivideRatio, Nullable&lt;MyStringId&gt; faceMaterial = null, int customViewProjectionMatrix = -1, MyBillboard.BlendTypeEnum blendType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjectionMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentCone_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCone*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentCone_VRageMath_MatrixD__System_Single_System_Single_VRageMath_Color__System_Int32_System_Nullable_VRage_Utils_MyStringId__System_Int32_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCone(VRageMath.MatrixD@,System.Single,System.Single,VRageMath.Color@,System.Int32,System.Nullable{VRage.Utils.MyStringId},System.Int32)">DrawTransparentCone(ref MatrixD, Single, Single, ref Color, Int32, Nullable&lt;MyStringId&gt;, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentCone(ref MatrixD worldMatrix, float radius, float height, ref Color color, int wireDivideRatio, Nullable&lt;MyStringId&gt; faceMaterial = null, int customViewProjectionMatrix = -1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjectionMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentCuboid_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCuboid*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentCuboid_VRageMath_MatrixD__VRageMath_MyCuboid_VRageMath_Vector4__System_Boolean_System_Single_System_Nullable_VRage_Utils_MyStringId__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCuboid(VRageMath.MatrixD@,VRageMath.MyCuboid,VRageMath.Vector4@,System.Boolean,System.Single,System.Nullable{VRage.Utils.MyStringId})">DrawTransparentCuboid(ref MatrixD, MyCuboid, ref Vector4, Boolean, Single, Nullable&lt;MyStringId&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentCuboid(ref MatrixD worldMatrix, MyCuboid cuboid, ref Vector4 vctColor, bool bWireFramed, float thickness, Nullable&lt;MyStringId&gt; lineMaterial = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyCuboid.html">MyCuboid</a></td>
        <td><span class="parametername">cuboid</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vctColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">bWireFramed</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentCylinder_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCylinder*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentCylinder_VRageMath_MatrixD__System_Single_System_Single_System_Single_VRageMath_Vector4__System_Boolean_System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentCylinder(VRageMath.MatrixD@,System.Single,System.Single,System.Single,VRageMath.Vector4@,System.Boolean,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId})">DrawTransparentCylinder(ref MatrixD, Single, Single, Single, ref Vector4, Boolean, Int32, Single, Nullable&lt;MyStringId&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentCylinder(ref MatrixD worldMatrix, float radius1, float radius2, float length, ref Vector4 vctColor, bool bWireFramed, int wireDivideRatio, float thickness, Nullable&lt;MyStringId&gt; lineMaterial = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vctColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">bWireFramed</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentPyramid_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentPyramid*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentPyramid_VRageMath_Vector3D__VRageMath_MyQuad__VRageMath_Vector4__System_Int32_System_Single_System_Nullable_VRage_Utils_MyStringId__" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentPyramid(VRageMath.Vector3D@,VRageMath.MyQuad@,VRageMath.Vector4@,System.Int32,System.Single,System.Nullable{VRage.Utils.MyStringId})">DrawTransparentPyramid(ref Vector3D, ref MyQuad, ref Vector4, Int32, Single, Nullable&lt;MyStringId&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentPyramid(ref Vector3D start, ref MyQuad backQuad, ref Vector4 vctColor, int divideRatio, float thickness, Nullable&lt;MyStringId&gt; lineMaterial = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuad.html">MyQuad</a></td>
        <td><span class="parametername">backQuad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vctColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">divideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentRamp_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentRamp*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentRamp_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__System_Nullable_VRage_Utils_MyStringId__System_Boolean_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentRamp(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,System.Nullable{VRage.Utils.MyStringId},System.Boolean,System.Int32,VRageRender.MyBillboard.BlendTypeEnum)">DrawTransparentRamp(ref MatrixD, ref BoundingBoxD, ref Color, Nullable&lt;MyStringId&gt;, Boolean, Int32, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentRamp(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, Nullable&lt;MyStringId&gt; faceMaterial = null, bool onlyFrontFaces = false, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">onlyFrontFaces</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentRoundedCorner_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentRoundedCorner*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentRoundedCorner_VRageMath_MatrixD__VRageMath_BoundingBoxD__VRageMath_Color__System_Nullable_VRage_Utils_MyStringId__System_Int32_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentRoundedCorner(VRageMath.MatrixD@,VRageMath.BoundingBoxD@,VRageMath.Color@,System.Nullable{VRage.Utils.MyStringId},System.Int32)">DrawTransparentRoundedCorner(ref MatrixD, ref BoundingBoxD, ref Color, Nullable&lt;MyStringId&gt;, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentRoundedCorner(ref MatrixD worldMatrix, ref BoundingBoxD localbox, ref Color color, Nullable&lt;MyStringId&gt; faceMaterial = null, int customViewProjection = -1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentSphere_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentSphere*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentSphere_System_Collections_Generic_List_VRageMath_Vector3D__System_Single_VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Single_System_Int32_System_Collections_Generic_List_VRageRender_MyBillboard__VRageRender_MyBillboard_BlendTypeEnum_System_Single_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentSphere(System.Collections.Generic.List{VRageMath.Vector3D},System.Single,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Single,System.Int32,System.Collections.Generic.List{VRageRender.MyBillboard},VRageRender.MyBillboard.BlendTypeEnum,System.Single)">DrawTransparentSphere(List&lt;Vector3D&gt;, Single, ref Color, MySimpleObjectRasterizer, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Single, Int32, List&lt;MyBillboard&gt;, MyBillboard.BlendTypeEnum, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentSphere(List&lt;Vector3D&gt; verticesBuffer, float radius, ref Color color, MySimpleObjectRasterizer rasterization, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, float lineThickness = -1F, int customViewProjectionMatrix = -1, List&lt;MyBillboard&gt; persistentBillboards = null, MyBillboard.BlendTypeEnum blendType, float intensity = 1F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">verticesBuffer</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineThickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjectionMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_DrawTransparentSphere_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentSphere*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_DrawTransparentSphere_VRageMath_MatrixD__System_Single_VRageMath_Color__VRage_Game_MySimpleObjectRasterizer_System_Int32_System_Nullable_VRage_Utils_MyStringId__System_Nullable_VRage_Utils_MyStringId__System_Single_System_Int32_System_Collections_Generic_List_VRageRender_MyBillboard__VRageRender_MyBillboard_BlendTypeEnum_System_Single_" data-uid="VRage.Game.MySimpleObjectDraw.DrawTransparentSphere(VRageMath.MatrixD@,System.Single,VRageMath.Color@,VRage.Game.MySimpleObjectRasterizer,System.Int32,System.Nullable{VRage.Utils.MyStringId},System.Nullable{VRage.Utils.MyStringId},System.Single,System.Int32,System.Collections.Generic.List{VRageRender.MyBillboard},VRageRender.MyBillboard.BlendTypeEnum,System.Single)">DrawTransparentSphere(ref MatrixD, Single, ref Color, MySimpleObjectRasterizer, Int32, Nullable&lt;MyStringId&gt;, Nullable&lt;MyStringId&gt;, Single, Int32, List&lt;MyBillboard&gt;, MyBillboard.BlendTypeEnum, Single)</h4>
  <div class="markdown level1 summary"><p>DrawTransparentSphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DrawTransparentSphere(ref MatrixD worldMatrix, float radius, ref Color color, MySimpleObjectRasterizer rasterization, int wireDivideRatio, Nullable&lt;MyStringId&gt; faceMaterial = null, Nullable&lt;MyStringId&gt; lineMaterial = null, float lineThickness = -1F, int customViewProjectionMatrix = -1, List&lt;MyBillboard&gt; persistentBillboards = null, MyBillboard.BlendTypeEnum blendType, float intensity = 1F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.MySimpleObjectRasterizer.html">MySimpleObjectRasterizer</a></td>
        <td><span class="parametername">rasterization</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">wireDivideRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">faceMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>&gt;</td>
        <td><span class="parametername">lineMaterial</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineThickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjectionMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_FaceVisible_" data-uid="VRage.Game.MySimpleObjectDraw.FaceVisible*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_FaceVisible_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRage.Game.MySimpleObjectDraw.FaceVisible(VRageMath.Vector3D,VRageMath.Vector3D)">FaceVisible(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool FaceVisible(Vector3D center, Vector3D normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MySimpleObjectDraw_FaceVisibleRelative_" data-uid="VRage.Game.MySimpleObjectDraw.FaceVisibleRelative*"></a>
  <h4 id="VRage_Game_MySimpleObjectDraw_FaceVisibleRelative_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRage.Game.MySimpleObjectDraw.FaceVisibleRelative(VRageMath.Vector3D,VRageMath.Vector3D)">FaceVisibleRelative(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool FaceVisibleRelative(Vector3D center, Vector3D normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
