﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class PhysicsSettings
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class PhysicsSettings
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.PhysicsSettings">
  
  
  <h1 id="VRage_ModAPI_PhysicsSettings" data-uid="VRage.ModAPI.PhysicsSettings" class="text-break">Class PhysicsSettings
  </h1>
  <div class="markdown level0 summary"><p>Implements Physics Settings</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">PhysicsSettings</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_PhysicsSettings_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class PhysicsSettings : ValueType</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_AngularDamping" data-uid="VRage.ModAPI.PhysicsSettings.AngularDamping">AngularDamping</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the Angular Damping. SE default value is 0.1</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float AngularDamping</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_CollisionLayer" data-uid="VRage.ModAPI.PhysicsSettings.CollisionLayer">CollisionLayer</h4>
  <div class="markdown level1 summary"><p>Collision layer. Makes your entity react on collisions with entities that having certain layers
TargetDummyLayer = 6
BlockPlacementTestCollisionLayer = 7
MissileLayer = 8
NoVoxelCollisionLayer = 9
LightFloatingObjectCollisionLayer = 10
VoxelLod1CollisionLayer = 11
NotCollideWithStaticLayer = 12
StaticCollisionLayer = 13
CollideWithStaticLayer = 14
DefaultCollisionLayer = 15
DynamicDoubledCollisionLayer = 16
KinematicDoubledCollisionLayer = 17
CharacterCollisionLayer = 18
NoCollisionLayer = 19
DebrisCollisionLayer = 20
GravityPhantomLayer = 21
CharacterNetworkCollisionLayer = 22
FloatingObjectCollisionLayer = 23
ObjectDetectionCollisionLayer = 24
VirtualMassLayer = 25
CollectorCollisionLayer = 26
AmmoLayer = 27
VoxelCollisionLayer = 28
ExplosionRaycastLayer = 29
CollisionLayerWithoutCharacter = 30
RagdollCollisionLayer = 31</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ushort CollisionLayer</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt16</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_DetectorColliderCallback" data-uid="VRage.ModAPI.PhysicsSettings.DetectorColliderCallback">DetectorColliderCallback</h4>
  <div class="markdown level1 summary"><p>If it is not null, then it would be call this callback each time entity Enters/Leaves it's collision.
Also removes physical collision (Now all entities can go through it)
CollisionCallback is called from parallel thread, and called once per HkBody. Grids can have more than 100 bodies.
Try find best collision layer, to filter unneeded collisions and improve performance
If you trying detect grids:</p>
<pre><code>protected MyConcurrentHashSet&lt;long> m_containedEntities = new MyConcurrentHashSet&lt;long>();
var topEntity = entity.GetTopMostParent() as MyEntity;
if (m_containedEntities.Add(topEntity.EntityId))
{
    MyAPIGateway.Utilities.InvokeOnGameThread(() =>
    {
        //Called once in main thread
    });
}</code></pre>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Action&lt;IMyEntity, bool&gt; DetectorColliderCallback</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>, <span class="xref">System.Boolean</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_Entity" data-uid="VRage.ModAPI.PhysicsSettings.Entity">Entity</h4>
  <div class="markdown level1 summary"><p>For modders:
You can create your own entities with this code.</p>
<pre><code>var entity = new MyEntity();          
entity.WorldMatrix = MatrixD.Identity;
entity.Init(new StringBuilder(&quot;Name&quot;), &quot;Models\\Cubes\\Large\\GeneratorLarge.mwm&quot;, null, null, &quot;Models\\Cubes\\Large\\GeneratorLarge.mwm&quot;);
MyAPIGateway.Entities.AddEntity(entity);</code></pre>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IMyEntity Entity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_IsPhantom" data-uid="VRage.ModAPI.PhysicsSettings.IsPhantom">IsPhantom</h4>
  <div class="markdown level1 summary"><p>Is mainly used, to detect if block can be placed at this position</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsPhantom</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_LinearDamping" data-uid="VRage.ModAPI.PhysicsSettings.LinearDamping">LinearDamping</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the Linear Damping in meters per tick</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LinearDamping</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_LocalCenter" data-uid="VRage.ModAPI.PhysicsSettings.LocalCenter">LocalCenter</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the Center of physical shape</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 LocalCenter</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_Mass" data-uid="VRage.ModAPI.PhysicsSettings.Mass">Mass</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the mass of object</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;ModAPIMass&gt; Mass</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.ModAPI.ModAPIMass.html">ModAPIMass</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_MaterialType" data-uid="VRage.ModAPI.PhysicsSettings.MaterialType">MaterialType</h4>
  <div class="markdown level1 summary"><p>Gets or Sets From what material object is. Example: Rock,Metal,Ammo,Character,Wood,Thruster_large,Thruster_small,Missile</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStringHash MaterialType</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringHash.html">MyStringHash</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_RigidBodyFlags" data-uid="VRage.ModAPI.PhysicsSettings.RigidBodyFlags">RigidBodyFlags</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the Flags, defining behavioral features</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public RigidBodyFlag RigidBodyFlags</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.RigidBodyFlag.html">RigidBodyFlag</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_PhysicsSettings_WorldMatrix" data-uid="VRage.ModAPI.PhysicsSettings.WorldMatrix">WorldMatrix</h4>
  <div class="markdown level1 summary"><p>Gets or Sets the World matrix of physical shape</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD WorldMatrix</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
