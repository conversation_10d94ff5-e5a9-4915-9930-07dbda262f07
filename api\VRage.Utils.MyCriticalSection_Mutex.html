﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyCriticalSection_Mutex
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyCriticalSection_Mutex
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils.MyCriticalSection_Mutex">
  
  
  <h1 id="VRage_Utils_MyCriticalSection_Mutex" data-uid="VRage.Utils.MyCriticalSection_Mutex" class="text-break">Class MyCriticalSection_Mutex
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyCriticalSection_Mutex</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Utils.html">VRage.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Utils_MyCriticalSection_Mutex_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyCriticalSection_Mutex : Object, ISharedCriticalSection, IDisposable</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex__ctor_" data-uid="VRage.Utils.MyCriticalSection_Mutex.#ctor*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex__ctor" data-uid="VRage.Utils.MyCriticalSection_Mutex.#ctor">MyCriticalSection_Mutex()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyCriticalSection_Mutex()</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex_Dispose_" data-uid="VRage.Utils.MyCriticalSection_Mutex.Dispose*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex_Dispose" data-uid="VRage.Utils.MyCriticalSection_Mutex.Dispose">Dispose()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dispose()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex_EnterShared_" data-uid="VRage.Utils.MyCriticalSection_Mutex.EnterShared*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex_EnterShared" data-uid="VRage.Utils.MyCriticalSection_Mutex.EnterShared">EnterShared()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SharedCriticalSection_SharedLock EnterShared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Threading.SharedCriticalSection_SharedLock</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex_EnterUnique_" data-uid="VRage.Utils.MyCriticalSection_Mutex.EnterUnique*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex_EnterUnique" data-uid="VRage.Utils.MyCriticalSection_Mutex.EnterUnique">EnterUnique()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SharedCriticalSection_UniqueLock EnterUnique()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Threading.SharedCriticalSection_UniqueLock</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex_LeaveShared_Internal_" data-uid="VRage.Utils.MyCriticalSection_Mutex.LeaveShared_Internal*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex_LeaveShared_Internal" data-uid="VRage.Utils.MyCriticalSection_Mutex.LeaveShared_Internal">LeaveShared_Internal()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void LeaveShared_Internal()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyCriticalSection_Mutex_LeaveUnique_Internal_" data-uid="VRage.Utils.MyCriticalSection_Mutex.LeaveUnique_Internal*"></a>
  <h4 id="VRage_Utils_MyCriticalSection_Mutex_LeaveUnique_Internal" data-uid="VRage.Utils.MyCriticalSection_Mutex.LeaveUnique_Internal">LeaveUnique_Internal()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void LeaveUnique_Internal()</code></pre>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
