﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyInterpolationQueue&lt;T&gt;
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyInterpolationQueue&lt;T&gt;
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Library.Utils.MyInterpolationQueue`1">
  
  
  <h1 id="VRage_Library_Utils_MyInterpolationQueue_1" data-uid="VRage.Library.Utils.MyInterpolationQueue`1" class="text-break">Class MyInterpolationQueue&lt;T&gt;
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyInterpolationQueue&lt;T&gt;</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Library.Utils.html">VRage.Library.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.Library.dll</h6>
  <h5 id="VRage_Library_Utils_MyInterpolationQueue_1_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyInterpolationQueue&lt;T&gt; : Object</code></pre>
  </div>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1__ctor_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.#ctor*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1__ctor_System_Int32_VRage_Library_Utils_InterpolationHandler__0__" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.#ctor(System.Int32,VRage.Library.Utils.InterpolationHandler{`0})">MyInterpolationQueue(Int32, InterpolationHandler&lt;T&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyInterpolationQueue(int defaultCapacity, InterpolationHandler&lt;T&gt; interpolator)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">defaultCapacity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.InterpolationHandler-1.html">InterpolationHandler</a>&lt;T&gt;</td>
        <td><span class="parametername">interpolator</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_Count_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Count*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_Count" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Count">Count</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Count { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_LastSample_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.LastSample*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_LastSample" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.LastSample">LastSample</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyTimeSpan LastSample { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_AddSample_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.AddSample*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_AddSample__0__VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.AddSample(`0@,VRage.Library.Utils.MyTimeSpan)">AddSample(ref T, MyTimeSpan)</h4>
  <div class="markdown level1 summary"><p>Adds sample with timestamp, it must be larger than last timestamp!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddSample(ref T item, MyTimeSpan sampleTimestamp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">sampleTimestamp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_Clear_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Clear*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_Clear" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_DiscardOld_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.DiscardOld*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_DiscardOld_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.DiscardOld(VRage.Library.Utils.MyTimeSpan)">DiscardOld(MyTimeSpan)</h4>
  <div class="markdown level1 summary"><p>Discards old samples, keeps at least 2 samples to be able to interpolate or extrapolate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DiscardOld(MyTimeSpan currentTimestamp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">currentTimestamp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyInterpolationQueue_1_Interpolate_" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Interpolate*"></a>
  <h4 id="VRage_Library_Utils_MyInterpolationQueue_1_Interpolate_VRage_Library_Utils_MyTimeSpan__0__" data-uid="VRage.Library.Utils.MyInterpolationQueue`1.Interpolate(VRage.Library.Utils.MyTimeSpan,`0@)">Interpolate(MyTimeSpan, out T)</h4>
  <div class="markdown level1 summary"><p>Discards old frame (keeps one older) and interpolates between two samples using interpolator.
Returns interpolator
There must be at least one sample!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Interpolate(MyTimeSpan currentTimestamp, out T result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">currentTimestamp</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
