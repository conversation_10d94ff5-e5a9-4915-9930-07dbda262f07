﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyTSSArtificialHorizon
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyTSSArtificialHorizon
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon">
  
  
  <h1 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon" class="text-break">Class MyTSSArtificialHorizon
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html">MyTextSurfaceScriptBase</a></div>
    <div class="level2"><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html">MyTSSCommon</a></div>
    <div class="level3"><span class="xref">MyTSSArtificialHorizon</span></div>
  </div>
  <div classs="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.IMyTextSurfaceScript.html">IMyTextSurfaceScript</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_m_fontId">MyTSSCommon.m_fontId</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_m_fontScale">MyTSSCommon.m_fontScale</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_AddBackground_VRage_Game_GUI_TextPanel_MySpriteDrawFrame_System_Nullable_VRageMath_Color__">MyTSSCommon.AddBackground(MySpriteDrawFrame, Nullable&lt;Color&gt;)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_AddBrackets_VRage_Game_GUI_TextPanel_MySpriteDrawFrame_VRageMath_Vector2_System_Single_System_Single_">MyTSSCommon.AddBrackets(MySpriteDrawFrame, Vector2, Single, Single)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_AddProgressBar_VRage_Game_GUI_TextPanel_MySpriteDrawFrame_VRageMath_Vector2_VRageMath_Vector2_System_Single_VRageMath_Color_VRageMath_Color_System_String_System_String_">MyTSSCommon.AddProgressBar(MySpriteDrawFrame, Vector2, Vector2, Single, Color, Color, String, String)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_AddTextBox_VRage_Game_GUI_TextPanel_MySpriteDrawFrame_VRageMath_Vector2_VRageMath_Vector2_System_String_System_String_System_Single_VRageMath_Color_VRageMath_Color_System_String_System_Single_">MyTSSCommon.AddTextBox(MySpriteDrawFrame, Vector2, Vector2, String, String, Single, Color, Color, String, Single)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_AddLine_VRage_Game_GUI_TextPanel_MySpriteDrawFrame_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Color_System_Single_">MyTSSCommon.AddLine(MySpriteDrawFrame, Vector2, Vector2, Color, Single)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_DEFAULT_BACKGROUND_COLOR">MyTextSurfaceScriptBase.DEFAULT_BACKGROUND_COLOR</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_DEFAULT_FONT_COLOR">MyTextSurfaceScriptBase.DEFAULT_FONT_COLOR</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_surface">MyTextSurfaceScriptBase.m_surface</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_block">MyTextSurfaceScriptBase.m_block</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_size">MyTextSurfaceScriptBase.m_size</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_halfSize">MyTextSurfaceScriptBase.m_halfSize</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_scale">MyTextSurfaceScriptBase.m_scale</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_backgroundColor">MyTextSurfaceScriptBase.m_backgroundColor</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_m_foregroundColor">MyTextSurfaceScriptBase.m_foregroundColor</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_Dispose">MyTextSurfaceScriptBase.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_FitRect_VRageMath_Vector2_VRageMath_Vector2__">MyTextSurfaceScriptBase.FitRect(Vector2, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_Surface">MyTextSurfaceScriptBase.Surface</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_Block">MyTextSurfaceScriptBase.Block</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_ForegroundColor">MyTextSurfaceScriptBase.ForegroundColor</a>
    </div>
    <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_BackgroundColor">MyTextSurfaceScriptBase.BackgroundColor</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.html">Sandbox.Game.GameSystems.TextSurfaceScripts</a></h6>
  <h6><strong>Assembly</strong>: Sandbox.Game.dll</h6>
  <h5 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyTextSurfaceScript(&quot;TSS_ArtificialHorizon&quot;, &quot;DisplayName_TSS_ArtificialHorizon&quot;)]
public class MyTSSArtificialHorizon : MyTSSCommon, IMyTextSurfaceScript, IDisposable</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon__ctor_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.#ctor*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon__ctor_Sandbox_ModAPI_Ingame_IMyTextSurface_VRage_Game_ModAPI_Ingame_IMyCubeBlock_VRageMath_Vector2_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.#ctor(Sandbox.ModAPI.Ingame.IMyTextSurface,VRage.Game.ModAPI.Ingame.IMyCubeBlock,VRageMath.Vector2)">MyTSSArtificialHorizon(IMyTextSurface, IMyCubeBlock, Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyTSSArtificialHorizon(IMyTextSurface surface, IMyCubeBlock block, Vector2 size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Sandbox.ModAPI.Ingame.IMyTextSurface.html">IMyTextSurface</a></td>
        <td><span class="parametername">surface</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.Ingame.IMyCubeBlock.html">IMyCubeBlock</a></td>
        <td><span class="parametername">block</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon_NeedsUpdate_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.NeedsUpdate*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon_NeedsUpdate" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.NeedsUpdate">NeedsUpdate</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override ScriptUpdate NeedsUpdate { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.ScriptUpdate.html">ScriptUpdate</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptBase.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptBase_NeedsUpdate">MyTextSurfaceScriptBase.NeedsUpdate</a></div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon_Run_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.Run*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSArtificialHorizon_Run" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSArtificialHorizon.Run">Run()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Run()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTSSCommon.html#Sandbox_Game_GameSystems_TextSurfaceScripts_MyTSSCommon_Run">MyTSSCommon.Run()</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.IMyTextSurfaceScript.html">IMyTextSurfaceScript</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
