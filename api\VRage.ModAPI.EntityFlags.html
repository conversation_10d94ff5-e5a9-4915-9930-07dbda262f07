﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class EntityFlags
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class EntityFlags
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.EntityFlags">
  
  
  <h1 id="VRage_ModAPI_EntityFlags" data-uid="VRage.ModAPI.EntityFlags" class="text-break">Class EntityFlags
  </h1>
  <div class="markdown level0 summary"><p>Entity flags.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">EntityFlags</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_EntityFlags_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class EntityFlags : Enum</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Default" data-uid="VRage.ModAPI.EntityFlags.Default">Default</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Default</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_DrawOutsideViewDistance" data-uid="VRage.ModAPI.EntityFlags.DrawOutsideViewDistance">DrawOutsideViewDistance</h4>
  <div class="markdown level1 summary"><p>Flags would be delivered to render component. <span class="xref">VRageRender.RenderFlags.DrawOutsideViewDistance</span></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags DrawOutsideViewDistance</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_FastCastShadowResolve" data-uid="VRage.ModAPI.EntityFlags.FastCastShadowResolve">FastCastShadowResolve</h4>
  <div class="markdown level1 summary"><p>Flags would be delivered to render component. <span class="xref">VRageRender.RenderFlags.FastCastShadowResolve</span></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags FastCastShadowResolve</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_InvalidateOnMove" data-uid="VRage.ModAPI.EntityFlags.InvalidateOnMove">InvalidateOnMove</h4>
  <div class="markdown level1 summary"><p>If object is moved, invalidate its renderobjects (update render)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags InvalidateOnMove</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_IsGamePrunningStructureObject" data-uid="VRage.ModAPI.EntityFlags.IsGamePrunningStructureObject">IsGamePrunningStructureObject</h4>
  <div class="markdown level1 summary"><p>Can be added, removed, or updated in <strong>Sandbox.Game.Entities.MyGamePruningStructure</strong></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags IsGamePrunningStructureObject</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_IsNotGamePrunningStructureObject" data-uid="VRage.ModAPI.EntityFlags.IsNotGamePrunningStructureObject">IsNotGamePrunningStructureObject</h4>
  <div class="markdown level1 summary"><p>Do not use in prunning, even though it is a root entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags IsNotGamePrunningStructureObject</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Near" data-uid="VRage.ModAPI.EntityFlags.Near">Near</h4>
  <div class="markdown level1 summary"><p>Specifies whether entity is &quot;near&quot;, near entities are cockpit and weapons, these entities are rendered in special way</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Near</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsDraw" data-uid="VRage.ModAPI.EntityFlags.NeedsDraw">NeedsDraw</h4>
  <div class="markdown level1 summary"><p>Draw method of this entity will be called when suitable. <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_NeedsDraw">NeedsDraw</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsDraw</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsDrawFromParent" data-uid="VRage.ModAPI.EntityFlags.NeedsDrawFromParent">NeedsDrawFromParent</h4>
  <div class="markdown level1 summary"><p>Draw method of this entity will be called when suitable and only from parent</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsDrawFromParent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsResolveCastShadow" data-uid="VRage.ModAPI.EntityFlags.NeedsResolveCastShadow">NeedsResolveCastShadow</h4>
  <div class="markdown level1 summary"><p>Flags would be delivered to render component. <span class="xref">VRageRender.RenderFlags.NeedsResolveCastShadow</span></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsResolveCastShadow</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsSimulate" data-uid="VRage.ModAPI.EntityFlags.NeedsSimulate">NeedsSimulate</h4>
  <div class="markdown level1 summary"><p>Entity has special simulation update. <a class="xref" href="VRage.ModAPI.MyEntityUpdateEnum.html#VRage_ModAPI_MyEntityUpdateEnum_SIMULATE">SIMULATE</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsSimulate</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsUpdate" data-uid="VRage.ModAPI.EntityFlags.NeedsUpdate">NeedsUpdate</h4>
  <div class="markdown level1 summary"><p>On this entity and its children will be called UpdateBeforeSimulation and UpdateAfterSimulation each frame</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsUpdate</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsUpdate10" data-uid="VRage.ModAPI.EntityFlags.NeedsUpdate10">NeedsUpdate10</h4>
  <div class="markdown level1 summary"><p>Entity updated each 10th frame</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsUpdate10</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsUpdate100" data-uid="VRage.ModAPI.EntityFlags.NeedsUpdate100">NeedsUpdate100</h4>
  <div class="markdown level1 summary"><p>Entity updated each 100th frame</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsUpdate100</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsUpdateAfter" data-uid="VRage.ModAPI.EntityFlags.NeedsUpdateAfter">NeedsUpdateAfter</h4>
  <div class="markdown level1 summary"><p>On this entity and its children will be called UpdateAfterSimulation each frame</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsUpdateAfter</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsUpdateBeforeNextFrame" data-uid="VRage.ModAPI.EntityFlags.NeedsUpdateBeforeNextFrame">NeedsUpdateBeforeNextFrame</h4>
  <div class="markdown level1 summary"><p>Entity updated once before first frame.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsUpdateBeforeNextFrame</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_NeedsWorldMatrix" data-uid="VRage.ModAPI.EntityFlags.NeedsWorldMatrix">NeedsWorldMatrix</h4>
  <div class="markdown level1 summary"><p>If child, its world matrix must be always updated</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags NeedsWorldMatrix</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_None" data-uid="VRage.ModAPI.EntityFlags.None">None</h4>
  <div class="markdown level1 summary"><p>No flags</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags None</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Save" data-uid="VRage.ModAPI.EntityFlags.Save">Save</h4>
  <div class="markdown level1 summary"><p>Specifies whether save entity when saving sector or not</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Save</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_ShadowBoxLod" data-uid="VRage.ModAPI.EntityFlags.ShadowBoxLod">ShadowBoxLod</h4>
  <div class="markdown level1 summary"><p>Draw LOD shadow as box</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags ShadowBoxLod</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_SkipIfTooSmall" data-uid="VRage.ModAPI.EntityFlags.SkipIfTooSmall">SkipIfTooSmall</h4>
  <div class="markdown level1 summary"><p>Flags would be delivered to render component. <span class="xref">VRageRender.RenderFlags.SkipIfTooSmall</span></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags SkipIfTooSmall</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Sync" data-uid="VRage.ModAPI.EntityFlags.Sync">Sync</h4>
  <div class="markdown level1 summary"><p>Synchronize object during multiplayer</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Sync</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Transparent" data-uid="VRage.ModAPI.EntityFlags.Transparent">Transparent</h4>
  <div class="markdown level1 summary"><p>Render the entity using dithering to simulate transparency</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Transparent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_UpdateRender" data-uid="VRage.ModAPI.EntityFlags.UpdateRender">UpdateRender</h4>
  <div class="markdown level1 summary"><p>Entity call <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_UpdateRenderObject_System_Boolean_System_Boolean_">UpdateRenderObject(Boolean, Boolean)</a> on OnAddedToScene</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags UpdateRender</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_value__" data-uid="VRage.ModAPI.EntityFlags.value__">value__</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int value__</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_EntityFlags_Visible" data-uid="VRage.ModAPI.EntityFlags.Visible">Visible</h4>
  <div class="markdown level1 summary"><p>Specifies whether draw this entity or not.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const EntityFlags Visible</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
