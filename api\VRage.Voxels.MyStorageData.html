﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyStorageData
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyStorageData
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Voxels.MyStorageData">
  
  
  <h1 id="VRage_Voxels_MyStorageData" data-uid="VRage.Voxels.MyStorageData" class="text-break">Class MyStorageData
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyStorageData</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Voxels.html">VRage.Voxels</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Voxels_MyStorageData_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyStorageData : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Voxels_MyStorageData__ctor_" data-uid="VRage.Voxels.MyStorageData.#ctor*"></a>
  <h4 id="VRage_Voxels_MyStorageData__ctor" data-uid="VRage.Voxels.MyStorageData.#ctor">MyStorageData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStorageData()</code></pre>
  </div>
  
  
  <a id="VRage_Voxels_MyStorageData__ctor_" data-uid="VRage.Voxels.MyStorageData.#ctor*"></a>
  <h4 id="VRage_Voxels_MyStorageData__ctor_VRage_Voxels_MyStorageDataTypeFlags_" data-uid="VRage.Voxels.MyStorageData.#ctor(VRage.Voxels.MyStorageDataTypeFlags)">MyStorageData(MyStorageDataTypeFlags)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStorageData(MyStorageDataTypeFlags typesToStore)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">typesToStore</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData__ctor_" data-uid="VRage.Voxels.MyStorageData.#ctor*"></a>
  <h4 id="VRage_Voxels_MyStorageData__ctor_VRageMath_Vector3I_System_Byte___System_Byte___" data-uid="VRage.Voxels.MyStorageData.#ctor(VRageMath.Vector3I,System.Byte[],System.Byte[])">MyStorageData(Vector3I, Byte[], Byte[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStorageData(Vector3I size, byte[] content = null, byte[] material = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td><span class="parametername">content</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Voxels_MyStorageData_Item_" data-uid="VRage.Voxels.MyStorageData.Item*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Item_VRage_Voxels_MyStorageDataTypeEnum_" data-uid="VRage.Voxels.MyStorageData.Item(VRage.Voxels.MyStorageDataTypeEnum)">Item[MyStorageDataTypeEnum]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte[] this[MyStorageDataTypeEnum type] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Size3D_" data-uid="VRage.Voxels.MyStorageData.Size3D*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Size3D" data-uid="VRage.Voxels.MyStorageData.Size3D">Size3D</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Size3D { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_SizeLinear_" data-uid="VRage.Voxels.MyStorageData.SizeLinear*"></a>
  <h4 id="VRage_Voxels_MyStorageData_SizeLinear" data-uid="VRage.Voxels.MyStorageData.SizeLinear">SizeLinear</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int SizeLinear { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Step_" data-uid="VRage.Voxels.MyStorageData.Step*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Step" data-uid="VRage.Voxels.MyStorageData.Step">Step</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Step { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_StepLinear_" data-uid="VRage.Voxels.MyStorageData.StepLinear*"></a>
  <h4 id="VRage_Voxels_MyStorageData_StepLinear" data-uid="VRage.Voxels.MyStorageData.StepLinear">StepLinear</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int StepLinear { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_StepX_" data-uid="VRage.Voxels.MyStorageData.StepX*"></a>
  <h4 id="VRage_Voxels_MyStorageData_StepX" data-uid="VRage.Voxels.MyStorageData.StepX">StepX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int StepX { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_StepY_" data-uid="VRage.Voxels.MyStorageData.StepY*"></a>
  <h4 id="VRage_Voxels_MyStorageData_StepY" data-uid="VRage.Voxels.MyStorageData.StepY">StepY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int StepY { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_StepZ_" data-uid="VRage.Voxels.MyStorageData.StepZ*"></a>
  <h4 id="VRage_Voxels_MyStorageData_StepZ" data-uid="VRage.Voxels.MyStorageData.StepZ">StepZ</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int StepZ { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Voxels_MyStorageData_BlockFill_" data-uid="VRage.Voxels.MyStorageData.BlockFill*"></a>
  <h4 id="VRage_Voxels_MyStorageData_BlockFill_VRage_Voxels_MyStorageDataTypeEnum_VRageMath_Vector3I_VRageMath_Vector3I_System_Byte_" data-uid="VRage.Voxels.MyStorageData.BlockFill(VRage.Voxels.MyStorageDataTypeEnum,VRageMath.Vector3I,VRageMath.Vector3I,System.Byte)">BlockFill(MyStorageDataTypeEnum, Vector3I, Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void BlockFill(MyStorageDataTypeEnum type, Vector3I min, Vector3I max, byte content)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">content</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_BlockFillContent_" data-uid="VRage.Voxels.MyStorageData.BlockFillContent*"></a>
  <h4 id="VRage_Voxels_MyStorageData_BlockFillContent_VRageMath_Vector3I_VRageMath_Vector3I_System_Byte_" data-uid="VRage.Voxels.MyStorageData.BlockFillContent(VRageMath.Vector3I,VRageMath.Vector3I,System.Byte)">BlockFillContent(Vector3I, Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void BlockFillContent(Vector3I min, Vector3I max, byte content)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">content</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_BlockFillMaterial_" data-uid="VRage.Voxels.MyStorageData.BlockFillMaterial*"></a>
  <h4 id="VRage_Voxels_MyStorageData_BlockFillMaterial_VRageMath_Vector3I_VRageMath_Vector3I_System_Byte_" data-uid="VRage.Voxels.MyStorageData.BlockFillMaterial(VRageMath.Vector3I,VRageMath.Vector3I,System.Byte)">BlockFillMaterial(Vector3I, Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void BlockFillMaterial(Vector3I min, Vector3I max, byte materialIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">materialIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_BlockFillMaterialConsiderContent_" data-uid="VRage.Voxels.MyStorageData.BlockFillMaterialConsiderContent*"></a>
  <h4 id="VRage_Voxels_MyStorageData_BlockFillMaterialConsiderContent_VRageMath_Vector3I_VRageMath_Vector3I_System_Byte_" data-uid="VRage.Voxels.MyStorageData.BlockFillMaterialConsiderContent(VRageMath.Vector3I,VRageMath.Vector3I,System.Byte)">BlockFillMaterialConsiderContent(Vector3I, Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void BlockFillMaterialConsiderContent(Vector3I min, Vector3I max, byte materialIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">materialIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Clear_" data-uid="VRage.Voxels.MyStorageData.Clear*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Clear_VRage_Voxels_MyStorageDataTypeEnum_System_Byte_" data-uid="VRage.Voxels.MyStorageData.Clear(VRage.Voxels.MyStorageDataTypeEnum,System.Byte)">Clear(MyStorageDataTypeEnum, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear(MyStorageDataTypeEnum type, byte p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ClearContent_" data-uid="VRage.Voxels.MyStorageData.ClearContent*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ClearContent_System_Byte_" data-uid="VRage.Voxels.MyStorageData.ClearContent(System.Byte)">ClearContent(Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ClearContent(byte p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ClearMaterials_" data-uid="VRage.Voxels.MyStorageData.ClearMaterials*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ClearMaterials_System_Byte_" data-uid="VRage.Voxels.MyStorageData.ClearMaterials(System.Byte)">ClearMaterials(Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ClearMaterials(byte p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ComputeContentConstitution_" data-uid="VRage.Voxels.MyStorageData.ComputeContentConstitution*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ComputeContentConstitution" data-uid="VRage.Voxels.MyStorageData.ComputeContentConstitution">ComputeContentConstitution()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyVoxelContentConstitution ComputeContentConstitution()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyVoxelContentConstitution.html">MyVoxelContentConstitution</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ComputeLinear_" data-uid="VRage.Voxels.MyStorageData.ComputeLinear*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ComputeLinear_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyStorageData.ComputeLinear(VRageMath.Vector3I@)">ComputeLinear(ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ComputeLinear(ref Vector3I p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ComputePosition_" data-uid="VRage.Voxels.MyStorageData.ComputePosition*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ComputePosition_System_Int32_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyStorageData.ComputePosition(System.Int32,VRageMath.Vector3I@)">ComputePosition(Int32, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ComputePosition(int linear, out Vector3I p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linear</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ContainsIsoSurface_" data-uid="VRage.Voxels.MyStorageData.ContainsIsoSurface*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ContainsIsoSurface" data-uid="VRage.Voxels.MyStorageData.ContainsIsoSurface">ContainsIsoSurface()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ContainsIsoSurface()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ContainsVoxelsAboveIsoLevel_" data-uid="VRage.Voxels.MyStorageData.ContainsVoxelsAboveIsoLevel*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ContainsVoxelsAboveIsoLevel" data-uid="VRage.Voxels.MyStorageData.ContainsVoxelsAboveIsoLevel">ContainsVoxelsAboveIsoLevel()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ContainsVoxelsAboveIsoLevel()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Content_" data-uid="VRage.Voxels.MyStorageData.Content*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Content_System_Int32_" data-uid="VRage.Voxels.MyStorageData.Content(System.Int32)">Content(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Content(int linearIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linearIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Content_" data-uid="VRage.Voxels.MyStorageData.Content*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Content_System_Int32_System_Byte_" data-uid="VRage.Voxels.MyStorageData.Content(System.Int32,System.Byte)">Content(Int32, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Content(int linearIdx, byte content)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linearIdx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">content</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Content_" data-uid="VRage.Voxels.MyStorageData.Content*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Content_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Voxels.MyStorageData.Content(System.Int32,System.Int32,System.Int32)">Content(Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Content(int x, int y, int z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">x</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">y</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">z</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Content_" data-uid="VRage.Voxels.MyStorageData.Content*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Content_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyStorageData.Content(VRageMath.Vector3I@)">Content(ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Content(ref Vector3I p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Content_" data-uid="VRage.Voxels.MyStorageData.Content*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Content_VRageMath_Vector3I__System_Byte_" data-uid="VRage.Voxels.MyStorageData.Content(VRageMath.Vector3I@,System.Byte)">Content(ref Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Content(ref Vector3I p, byte content)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">content</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_CopyRange_" data-uid="VRage.Voxels.MyStorageData.CopyRange*"></a>
  <h4 id="VRage_Voxels_MyStorageData_CopyRange_VRage_Voxels_MyStorageData_VRageMath_Vector3I_VRageMath_Vector3I_VRageMath_Vector3I_VRage_Voxels_MyStorageDataTypeEnum_" data-uid="VRage.Voxels.MyStorageData.CopyRange(VRage.Voxels.MyStorageData,VRageMath.Vector3I,VRageMath.Vector3I,VRageMath.Vector3I,VRage.Voxels.MyStorageDataTypeEnum)">CopyRange(MyStorageData, Vector3I, Vector3I, Vector3I, MyStorageDataTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CopyRange(MyStorageData src, Vector3I min, Vector3I max, Vector3I offset, MyStorageDataTypeEnum dataType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">src</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">offset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">dataType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_FromBase64_" data-uid="VRage.Voxels.MyStorageData.FromBase64*"></a>
  <h4 id="VRage_Voxels_MyStorageData_FromBase64_System_String_" data-uid="VRage.Voxels.MyStorageData.FromBase64(System.String)">FromBase64(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyStorageData FromBase64(string str)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">str</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Get_" data-uid="VRage.Voxels.MyStorageData.Get*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Get_VRage_Voxels_MyStorageDataTypeEnum_System_Int32_" data-uid="VRage.Voxels.MyStorageData.Get(VRage.Voxels.MyStorageDataTypeEnum,System.Int32)">Get(MyStorageDataTypeEnum, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Get(MyStorageDataTypeEnum type, int linearIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linearIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Get_" data-uid="VRage.Voxels.MyStorageData.Get*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Get_VRage_Voxels_MyStorageDataTypeEnum_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Voxels.MyStorageData.Get(VRage.Voxels.MyStorageDataTypeEnum,System.Int32,System.Int32,System.Int32)">Get(MyStorageDataTypeEnum, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Get(MyStorageDataTypeEnum type, int x, int y, int z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">x</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">y</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">z</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Get_" data-uid="VRage.Voxels.MyStorageData.Get*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Get_VRage_Voxels_MyStorageDataTypeEnum_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyStorageData.Get(VRage.Voxels.MyStorageDataTypeEnum,VRageMath.Vector3I@)">Get(MyStorageDataTypeEnum, ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Get(MyStorageDataTypeEnum type, ref Vector3I p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Material_" data-uid="VRage.Voxels.MyStorageData.Material*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Material_System_Int32_" data-uid="VRage.Voxels.MyStorageData.Material(System.Int32)">Material(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Material(int linearIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linearIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Material_" data-uid="VRage.Voxels.MyStorageData.Material*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Material_System_Int32_System_Byte_" data-uid="VRage.Voxels.MyStorageData.Material(System.Int32,System.Byte)">Material(Int32, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Material(int linearIdx, byte materialIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">linearIdx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">materialIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Material_" data-uid="VRage.Voxels.MyStorageData.Material*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Material_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyStorageData.Material(VRageMath.Vector3I@)">Material(ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Material(ref Vector3I p)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Material_" data-uid="VRage.Voxels.MyStorageData.Material*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Material_VRageMath_Vector3I__System_Byte_" data-uid="VRage.Voxels.MyStorageData.Material(VRageMath.Vector3I@,System.Byte)">Material(ref Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Material(ref Vector3I p, byte materialIdx)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">materialIdx</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_OpRange_" data-uid="VRage.Voxels.MyStorageData.OpRange*"></a>
  <h4 id="VRage_Voxels_MyStorageData_OpRange__1_VRage_Voxels_MyStorageData_VRageMath_Vector3I_VRageMath_Vector3I_VRageMath_Vector3I_VRage_Voxels_MyStorageDataTypeEnum_" data-uid="VRage.Voxels.MyStorageData.OpRange``1(VRage.Voxels.MyStorageData,VRageMath.Vector3I,VRageMath.Vector3I,VRageMath.Vector3I,VRage.Voxels.MyStorageDataTypeEnum)">OpRange&lt;Op&gt;(MyStorageData, Vector3I, Vector3I, Vector3I, MyStorageDataTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OpRange&lt;Op&gt;(MyStorageData src, Vector3I min, Vector3I max, Vector3I offset, MyStorageDataTypeEnum dataType)
    where Op : struct, MyStorageData.IOperator, ValueType</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">src</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">offset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">dataType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">Op</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Resize_" data-uid="VRage.Voxels.MyStorageData.Resize*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Resize_VRageMath_Vector3I_" data-uid="VRage.Voxels.MyStorageData.Resize(VRageMath.Vector3I)">Resize(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Resize(Vector3I size3D)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">size3D</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Resize_" data-uid="VRage.Voxels.MyStorageData.Resize*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Resize_VRageMath_Vector3I_VRageMath_Vector3I_" data-uid="VRage.Voxels.MyStorageData.Resize(VRageMath.Vector3I,VRageMath.Vector3I)">Resize(Vector3I, Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Resize(Vector3I start, Vector3I end)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">start</span></td>
        <td><p>Inclusive.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">end</span></td>
        <td><p>Inclusive.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_Set_" data-uid="VRage.Voxels.MyStorageData.Set*"></a>
  <h4 id="VRage_Voxels_MyStorageData_Set_VRage_Voxels_MyStorageDataTypeEnum_VRageMath_Vector3I__System_Byte_" data-uid="VRage.Voxels.MyStorageData.Set(VRage.Voxels.MyStorageDataTypeEnum,VRageMath.Vector3I@,System.Byte)">Set(MyStorageDataTypeEnum, ref Vector3I, Byte)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Set(MyStorageDataTypeEnum type, ref Vector3I p, byte value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ToBase64_" data-uid="VRage.Voxels.MyStorageData.ToBase64*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ToBase64" data-uid="VRage.Voxels.MyStorageData.ToBase64">ToBase64()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string ToBase64()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_ValueWhenAllEqual_" data-uid="VRage.Voxels.MyStorageData.ValueWhenAllEqual*"></a>
  <h4 id="VRage_Voxels_MyStorageData_ValueWhenAllEqual_VRage_Voxels_MyStorageDataTypeEnum_" data-uid="VRage.Voxels.MyStorageData.ValueWhenAllEqual(VRage.Voxels.MyStorageDataTypeEnum)">ValueWhenAllEqual(MyStorageDataTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ValueWhenAllEqual(MyStorageDataTypeEnum dataType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeEnum.html">MyStorageDataTypeEnum</a></td>
        <td><span class="parametername">dataType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyStorageData_WrinkleVoxelContent_" data-uid="VRage.Voxels.MyStorageData.WrinkleVoxelContent*"></a>
  <h4 id="VRage_Voxels_MyStorageData_WrinkleVoxelContent_VRageMath_Vector3I__System_Single_System_Single_" data-uid="VRage.Voxels.MyStorageData.WrinkleVoxelContent(VRageMath.Vector3I@,System.Single,System.Single)">WrinkleVoxelContent(ref Vector3I, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool WrinkleVoxelContent(ref Vector3I p, float wrinkleWeightAdd, float wrinkleWeightRemove)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">wrinkleWeightAdd</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">wrinkleWeightRemove</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
