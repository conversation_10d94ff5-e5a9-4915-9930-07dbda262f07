﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Vector4
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Vector4
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Vector4">
  
  
  <h1 id="VRageMath_Vector4" data-uid="VRageMath.Vector4" class="text-break">Class Vector4
  </h1>
  <div class="markdown level0 summary"><p>Defines a vector with four components.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Vector4</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Vector4_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Vector4 : ValueType, IEquatable&lt;Vector4&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Vector4__ctor_" data-uid="VRageMath.Vector4.#ctor*"></a>
  <h4 id="VRageMath_Vector4__ctor_System_Single_" data-uid="VRageMath.Vector4.#ctor(System.Single)">Vector4(Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value to initialize each component to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4__ctor_" data-uid="VRageMath.Vector4.#ctor*"></a>
  <h4 id="VRageMath_Vector4__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Vector4.#ctor(System.Single,System.Single,System.Single,System.Single)">Vector4(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4(float x, float y, float z, float w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>Initial value for the x-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>Initial value for the y-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4__ctor_" data-uid="VRageMath.Vector4.#ctor*"></a>
  <h4 id="VRageMath_Vector4__ctor_VRageMath_Vector2_System_Single_System_Single_" data-uid="VRageMath.Vector4.#ctor(VRageMath.Vector2,System.Single,System.Single)">Vector4(Vector2, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4(Vector2 value, float z, float w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A vector containing the values to initialize x and y components with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4__ctor_" data-uid="VRageMath.Vector4.#ctor*"></a>
  <h4 id="VRageMath_Vector4__ctor_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector4.#ctor(VRageMath.Vector3,System.Single)">Vector4(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4(Vector3 value, float w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A vector containing the values to initialize x, y, and z components with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Vector4_One" data-uid="VRageMath.Vector4.One">One</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 One</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_UnitW" data-uid="VRageMath.Vector4.UnitW">UnitW</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 UnitW</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_UnitX" data-uid="VRageMath.Vector4.UnitX">UnitX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 UnitX</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_UnitY" data-uid="VRageMath.Vector4.UnitY">UnitY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 UnitY</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_UnitZ" data-uid="VRageMath.Vector4.UnitZ">UnitZ</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 UnitZ</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_W" data-uid="VRageMath.Vector4.W">W</h4>
  <div class="markdown level1 summary"><p>Gets or sets the w-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float W</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_X" data-uid="VRageMath.Vector4.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the x-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_Y" data-uid="VRageMath.Vector4.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the y-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_Z" data-uid="VRageMath.Vector4.Z">Z</h4>
  <div class="markdown level1 summary"><p>Gets or sets the z-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Z</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4_Zero" data-uid="VRageMath.Vector4.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Vector4_Item_" data-uid="VRageMath.Vector4.Item*"></a>
  <h4 id="VRageMath_Vector4_Item_System_Int32_" data-uid="VRageMath.Vector4.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float this[int index] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Vector4_Add_" data-uid="VRageMath.Vector4.Add*"></a>
  <h4 id="VRageMath_Vector4_Add_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Add(VRageMath.Vector4,VRageMath.Vector4)">Add(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Add(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Add_" data-uid="VRageMath.Vector4.Add*"></a>
  <h4 id="VRageMath_Vector4_Add_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Add(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Add(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Sum of the source vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Barycentric_" data-uid="VRageMath.Vector4.Barycentric*"></a>
  <h4 id="VRageMath_Vector4_Barycentric_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_System_Single_System_Single_" data-uid="VRageMath.Vector4.Barycentric(VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4,System.Single,System.Single)">Barycentric(Vector4, Vector4, Vector4, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector4 containing the 4D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 4D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Barycentric(Vector4 value1, Vector4 value2, Vector4 value3, float amount1, float amount2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Barycentric_" data-uid="VRageMath.Vector4.Barycentric*"></a>
  <h4 id="VRageMath_Vector4_Barycentric_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__System_Single_System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.Barycentric(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,System.Single,System.Single,VRageMath.Vector4@)">Barycentric(ref Vector4, ref Vector4, ref Vector4, Single, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector4 containing the 4D Cartesian coordinates of a point specified in Barycentric (areal) coordinates relative to a 4D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(ref Vector4 value1, ref Vector4 value2, ref Vector4 value3, float amount1, float amount2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The 4D Cartesian coordinates of the specified point are placed in this Vector4 on exit.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_CatmullRom_" data-uid="VRageMath.Vector4.CatmullRom*"></a>
  <h4 id="VRageMath_Vector4_CatmullRom_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.CatmullRom(VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4,System.Single)">CatmullRom(Vector4, Vector4, Vector4, Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 CatmullRom(Vector4 value1, Vector4 value2, Vector4 value3, Vector4 value4, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_CatmullRom_" data-uid="VRageMath.Vector4.CatmullRom*"></a>
  <h4 id="VRageMath_Vector4_CatmullRom_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.CatmullRom(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">CatmullRom(ref Vector4, ref Vector4, ref Vector4, ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CatmullRom(ref Vector4 value1, ref Vector4 value2, ref Vector4 value3, ref Vector4 value4, float amount, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A vector that is the result of the Catmull-Rom interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Clamp_" data-uid="VRageMath.Vector4.Clamp*"></a>
  <h4 id="VRageMath_Vector4_Clamp_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Clamp(VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4)">Clamp(Vector4, Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Clamp(Vector4 value1, Vector4 min, Vector4 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Clamp_" data-uid="VRageMath.Vector4.Clamp*"></a>
  <h4 id="VRageMath_Vector4_Clamp_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Clamp(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Clamp(ref Vector4, ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Clamp(ref Vector4 value1, ref Vector4 min, ref Vector4 max, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The clamped value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Distance_" data-uid="VRageMath.Vector4.Distance*"></a>
  <h4 id="VRageMath_Vector4_Distance_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Distance(VRageMath.Vector4,VRageMath.Vector4)">Distance(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Distance(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Distance_" data-uid="VRageMath.Vector4.Distance*"></a>
  <h4 id="VRageMath_Vector4_Distance_VRageMath_Vector4__VRageMath_Vector4__System_Single__" data-uid="VRageMath.Vector4.Distance(VRageMath.Vector4@,VRageMath.Vector4@,System.Single@)">Distance(ref Vector4, ref Vector4, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Distance(ref Vector4 value1, ref Vector4 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_DistanceSquared_" data-uid="VRageMath.Vector4.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector4_DistanceSquared_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.DistanceSquared(VRageMath.Vector4,VRageMath.Vector4)">DistanceSquared(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float DistanceSquared(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_DistanceSquared_" data-uid="VRageMath.Vector4.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector4_DistanceSquared_VRageMath_Vector4__VRageMath_Vector4__System_Single__" data-uid="VRageMath.Vector4.DistanceSquared(VRageMath.Vector4@,VRageMath.Vector4@,System.Single@)">DistanceSquared(ref Vector4, ref Vector4, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DistanceSquared(ref Vector4 value1, ref Vector4 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the two vectors squared.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Divide_" data-uid="VRageMath.Vector4.Divide*"></a>
  <h4 id="VRageMath_Vector4_Divide_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.Divide(VRageMath.Vector4,System.Single)">Divide(Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Divide(Vector4 value1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Divide_" data-uid="VRageMath.Vector4.Divide*"></a>
  <h4 id="VRageMath_Vector4_Divide_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Divide(VRageMath.Vector4,VRageMath.Vector4)">Divide(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Divide(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Divide_" data-uid="VRageMath.Vector4.Divide*"></a>
  <h4 id="VRageMath_Vector4_Divide_VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.Divide(VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">Divide(ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector4 value1, float divider, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Divide_" data-uid="VRageMath.Vector4.Divide*"></a>
  <h4 id="VRageMath_Vector4_Divide_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Divide(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Divide(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Dot_" data-uid="VRageMath.Vector4.Dot*"></a>
  <h4 id="VRageMath_Vector4_Dot_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Dot(VRageMath.Vector4,VRageMath.Vector4)">Dot(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Dot(Vector4 vector1, Vector4 vector2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Dot_" data-uid="VRageMath.Vector4.Dot*"></a>
  <h4 id="VRageMath_Vector4_Dot_VRageMath_Vector4__VRageMath_Vector4__System_Single__" data-uid="VRageMath.Vector4.Dot(VRageMath.Vector4@,VRageMath.Vector4@,System.Single@)">Dot(ref Vector4, ref Vector4, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Vector4 vector1, ref Vector4 vector2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the two vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Equals_" data-uid="VRageMath.Vector4.Equals*"></a>
  <h4 id="VRageMath_Vector4_Equals_System_Object_" data-uid="VRageMath.Vector4.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Equals_" data-uid="VRageMath.Vector4.Equals*"></a>
  <h4 id="VRageMath_Vector4_Equals_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Equals(VRageMath.Vector4)">Equals(Vector4)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector4 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Vector4 to compare with the current Vector4.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_GetHashCode_" data-uid="VRageMath.Vector4.GetHashCode*"></a>
  <h4 id="VRageMath_Vector4_GetHashCode" data-uid="VRageMath.Vector4.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Hermite_" data-uid="VRageMath.Vector4.Hermite*"></a>
  <h4 id="VRageMath_Vector4_Hermite_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.Hermite(VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4,VRageMath.Vector4,System.Single)">Hermite(Vector4, Vector4, Vector4, Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Hermite(Vector4 value1, Vector4 tangent1, Vector4 value2, Vector4 tangent2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Hermite_" data-uid="VRageMath.Vector4.Hermite*"></a>
  <h4 id="VRageMath_Vector4_Hermite_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.Hermite(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">Hermite(ref Vector4, ref Vector4, ref Vector4, ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Hermite(ref Vector4 value1, ref Vector4 tangent1, ref Vector4 value2, ref Vector4 tangent2, float amount, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the Hermite spline interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Length_" data-uid="VRageMath.Vector4.Length*"></a>
  <h4 id="VRageMath_Vector4_Length" data-uid="VRageMath.Vector4.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_LengthSquared_" data-uid="VRageMath.Vector4.LengthSquared*"></a>
  <h4 id="VRageMath_Vector4_LengthSquared" data-uid="VRageMath.Vector4.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Lerp_" data-uid="VRageMath.Vector4.Lerp*"></a>
  <h4 id="VRageMath_Vector4_Lerp_VRageMath_Vector4_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.Lerp(VRageMath.Vector4,VRageMath.Vector4,System.Single)">Lerp(Vector4, Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Lerp(Vector4 value1, Vector4 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Lerp_" data-uid="VRageMath.Vector4.Lerp*"></a>
  <h4 id="VRageMath_Vector4_Lerp_VRageMath_Vector4__VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.Lerp(VRageMath.Vector4@,VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">Lerp(ref Vector4, ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Vector4 value1, ref Vector4 value2, float amount, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Max_" data-uid="VRageMath.Vector4.Max*"></a>
  <h4 id="VRageMath_Vector4_Max_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Max(VRageMath.Vector4,VRageMath.Vector4)">Max(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Max(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Max_" data-uid="VRageMath.Vector4.Max*"></a>
  <h4 id="VRageMath_Vector4_Max_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Max(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Max(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Max(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The maximized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Min_" data-uid="VRageMath.Vector4.Min*"></a>
  <h4 id="VRageMath_Vector4_Min_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Min(VRageMath.Vector4,VRageMath.Vector4)">Min(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Min(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Min_" data-uid="VRageMath.Vector4.Min*"></a>
  <h4 id="VRageMath_Vector4_Min_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Min(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Min(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Min(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The minimized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Multiply_" data-uid="VRageMath.Vector4.Multiply*"></a>
  <h4 id="VRageMath_Vector4_Multiply_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.Multiply(VRageMath.Vector4,System.Single)">Multiply(Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Multiply(Vector4 value1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Multiply_" data-uid="VRageMath.Vector4.Multiply*"></a>
  <h4 id="VRageMath_Vector4_Multiply_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Multiply(VRageMath.Vector4,VRageMath.Vector4)">Multiply(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Multiply(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Multiply_" data-uid="VRageMath.Vector4.Multiply*"></a>
  <h4 id="VRageMath_Vector4_Multiply_VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.Multiply(VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">Multiply(ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector4 value1, float scaleFactor, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Multiply_" data-uid="VRageMath.Vector4.Multiply*"></a>
  <h4 id="VRageMath_Vector4_Multiply_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Multiply(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Multiply(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Negate_" data-uid="VRageMath.Vector4.Negate*"></a>
  <h4 id="VRageMath_Vector4_Negate_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Negate(VRageMath.Vector4)">Negate(Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Negate(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Negate_" data-uid="VRageMath.Vector4.Negate*"></a>
  <h4 id="VRageMath_Vector4_Negate_VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Negate(VRageMath.Vector4@,VRageMath.Vector4@)">Negate(ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Vector4 value, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Vector pointing in the opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Normalize_" data-uid="VRageMath.Vector4.Normalize*"></a>
  <h4 id="VRageMath_Vector4_Normalize" data-uid="VRageMath.Vector4.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Turns the current vector into a unit vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector4_Normalize_" data-uid="VRageMath.Vector4.Normalize*"></a>
  <h4 id="VRageMath_Vector4_Normalize_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Normalize(VRageMath.Vector4)">Normalize(Vector4)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Normalize(Vector4 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Normalize_" data-uid="VRageMath.Vector4.Normalize*"></a>
  <h4 id="VRageMath_Vector4_Normalize_VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Normalize(VRageMath.Vector4@,VRageMath.Vector4@)">Normalize(ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a normalized version of the specified vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector4 vector, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The normalized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_PackOrthoMatrix_" data-uid="VRageMath.Vector4.PackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4_PackOrthoMatrix_VRageMath_Matrix__" data-uid="VRageMath.Vector4.PackOrthoMatrix(VRageMath.Matrix@)">PackOrthoMatrix(ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 PackOrthoMatrix(ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_PackOrthoMatrix_" data-uid="VRageMath.Vector4.PackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4_PackOrthoMatrix_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector4.PackOrthoMatrix(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">PackOrthoMatrix(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 PackOrthoMatrix(Vector3 position, Vector3 forward, Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_SmoothStep_" data-uid="VRageMath.Vector4.SmoothStep*"></a>
  <h4 id="VRageMath_Vector4_SmoothStep_VRageMath_Vector4_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.SmoothStep(VRageMath.Vector4,VRageMath.Vector4,System.Single)">SmoothStep(Vector4, Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 SmoothStep(Vector4 value1, Vector4 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_SmoothStep_" data-uid="VRageMath.Vector4.SmoothStep*"></a>
  <h4 id="VRageMath_Vector4_SmoothStep_VRageMath_Vector4__VRageMath_Vector4__System_Single_VRageMath_Vector4__" data-uid="VRageMath.Vector4.SmoothStep(VRageMath.Vector4@,VRageMath.Vector4@,System.Single,VRageMath.Vector4@)">SmoothStep(ref Vector4, ref Vector4, Single, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SmoothStep(ref Vector4 value1, ref Vector4 value2, float amount, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The interpolated value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Subtract_" data-uid="VRageMath.Vector4.Subtract*"></a>
  <h4 id="VRageMath_Vector4_Subtract_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.Subtract(VRageMath.Vector4,VRageMath.Vector4)">Subtract(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Subtract(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Subtract_" data-uid="VRageMath.Vector4.Subtract*"></a>
  <h4 id="VRageMath_Vector4_Subtract_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Subtract(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Subtract(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_ToString_" data-uid="VRageMath.Vector4.ToString*"></a>
  <h4 id="VRageMath_Vector4_ToString" data-uid="VRageMath.Vector4.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector2_VRageMath_Matrix_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector2,VRageMath.Matrix)">Transform(Vector2, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector2 position, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector2_VRageMath_Quaternion_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector2,VRageMath.Quaternion)">Transform(Vector2, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector2 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector2 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector2__VRageMath_Matrix__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector2@,VRageMath.Matrix@,VRageMath.Vector4@)">Transform(ref Vector2, ref Matrix, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 position, ref Matrix matrix, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector2__VRageMath_Quaternion__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector2@,VRageMath.Quaternion@,VRageMath.Vector4@)">Transform(ref Vector2, ref Quaternion, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 value, ref Quaternion rotation, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector2 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector3_VRageMath_Matrix_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector3,VRageMath.Matrix)">Transform(Vector3, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector3 position, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector3_VRageMath_Quaternion_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector3,VRageMath.Quaternion)">Transform(Vector3, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector3 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector4@)">Transform(ref Vector3, ref Matrix, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3 position, ref Matrix matrix, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector3__VRageMath_Quaternion__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector3@,VRageMath.Quaternion@,VRageMath.Vector4@)">Transform(ref Vector3, ref Quaternion, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3 value, ref Quaternion rotation, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4_VRageMath_Matrix_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4,VRageMath.Matrix)">Transform(Vector4, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by the specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector4 vector, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4_VRageMath_Quaternion_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4,VRageMath.Quaternion)">Transform(Vector4, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Transform(Vector4 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4__VRageMath_Matrix__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4@,VRageMath.Matrix@,VRageMath.Vector4@)">Transform(ref Vector4, ref Matrix, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector4 vector, ref Matrix matrix, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4__VRageMath_Quaternion__VRageMath_Vector4__" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4@,VRageMath.Quaternion@,VRageMath.Vector4@)">Transform(ref Vector4, ref Quaternion, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector4 value, ref Quaternion rotation, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4___System_Int32_VRageMath_Matrix__VRageMath_Vector4___System_Int32_System_Int32_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4[],System.Int32,VRageMath.Matrix@,VRageMath.Vector4[],System.Int32,System.Int32)">Transform(Vector4[], Int32, ref Matrix, Vector4[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector4s by a specified Matrix into a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4[] sourceArray, int sourceIndex, ref Matrix matrix, Vector4[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s containing the range to transform.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array of the first Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array of Vector4s into which to write the results.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array of the first result Vector4 to write.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector4s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4___System_Int32_VRageMath_Quaternion__VRageMath_Vector4___System_Int32_System_Int32_" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4[],System.Int32,VRageMath.Quaternion@,VRageMath.Vector4[],System.Int32,System.Int32)">Transform(Vector4[], Int32, ref Quaternion, Vector4[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector4s by a specified Quaternion into a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4[] sourceArray, int sourceIndex, ref Quaternion rotation, Vector4[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s containing the range to transform.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array of the first Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array of Vector4s into which to write the results.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array of the first result Vector4 to write.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector4s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4___VRageMath_Matrix__VRageMath_Vector4___" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4[],VRageMath.Matrix@,VRageMath.Vector4[])">Transform(Vector4[], ref Matrix, Vector4[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector4s by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4[] sourceArray, ref Matrix matrix, Vector4[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array into which the transformed Vector4s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_Transform_" data-uid="VRageMath.Vector4.Transform*"></a>
  <h4 id="VRageMath_Vector4_Transform_VRageMath_Vector4___VRageMath_Quaternion__VRageMath_Vector4___" data-uid="VRageMath.Vector4.Transform(VRageMath.Vector4[],VRageMath.Quaternion@,VRageMath.Vector4[])">Transform(Vector4[], ref Quaternion, Vector4[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector4s by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4[] sourceArray, ref Quaternion rotation, Vector4[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array into which the transformed Vector4s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_UnpackOrthoMatrix_" data-uid="VRageMath.Vector4.UnpackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4_UnpackOrthoMatrix_VRageMath_Vector4__" data-uid="VRageMath.Vector4.UnpackOrthoMatrix(VRageMath.Vector4@)">UnpackOrthoMatrix(ref Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix UnpackOrthoMatrix(ref Vector4 packed)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">packed</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_UnpackOrthoMatrix_" data-uid="VRageMath.Vector4.UnpackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4_UnpackOrthoMatrix_VRageMath_Vector4__VRageMath_Matrix__" data-uid="VRageMath.Vector4.UnpackOrthoMatrix(VRageMath.Vector4@,VRageMath.Matrix@)">UnpackOrthoMatrix(ref Vector4, out Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void UnpackOrthoMatrix(ref Vector4 packed, out Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">packed</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Vector4_op_Addition_" data-uid="VRageMath.Vector4.op_Addition*"></a>
  <h4 id="VRageMath_Vector4_op_Addition_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Addition(VRageMath.Vector4,VRageMath.Vector4)">Addition(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator +(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Division_" data-uid="VRageMath.Vector4.op_Division*"></a>
  <h4 id="VRageMath_Vector4_op_Division_System_Single_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Division(System.Single,VRageMath.Vector4)">Division(Single, Vector4)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator /(float lhs, Vector4 rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Division_" data-uid="VRageMath.Vector4.op_Division*"></a>
  <h4 id="VRageMath_Vector4_op_Division_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.op_Division(VRageMath.Vector4,System.Single)">Division(Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator /(Vector4 value1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Division_" data-uid="VRageMath.Vector4.op_Division*"></a>
  <h4 id="VRageMath_Vector4_op_Division_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Division(VRageMath.Vector4,VRageMath.Vector4)">Division(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator /(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Equality_" data-uid="VRageMath.Vector4.op_Equality*"></a>
  <h4 id="VRageMath_Vector4_op_Equality_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Equality(VRageMath.Vector4,VRageMath.Vector4)">Equality(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Inequality_" data-uid="VRageMath.Vector4.op_Inequality*"></a>
  <h4 id="VRageMath_Vector4_op_Inequality_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Inequality(VRageMath.Vector4,VRageMath.Vector4)">Inequality(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Multiply_" data-uid="VRageMath.Vector4.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4_op_Multiply_System_Single_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Multiply(System.Single,VRageMath.Vector4)">Multiply(Single, Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator *(float scaleFactor, Vector4 value1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Multiply_" data-uid="VRageMath.Vector4.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4_op_Multiply_VRageMath_Vector4_System_Single_" data-uid="VRageMath.Vector4.op_Multiply(VRageMath.Vector4,System.Single)">Multiply(Vector4, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator *(Vector4 value1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Multiply_" data-uid="VRageMath.Vector4.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4_op_Multiply_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Multiply(VRageMath.Vector4,VRageMath.Vector4)">Multiply(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator *(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_Subtraction_" data-uid="VRageMath.Vector4.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector4_op_Subtraction_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_Subtraction(VRageMath.Vector4,VRageMath.Vector4)">Subtraction(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator -(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4_op_UnaryNegation_" data-uid="VRageMath.Vector4.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Vector4_op_UnaryNegation_VRageMath_Vector4_" data-uid="VRageMath.Vector4.op_UnaryNegation(VRageMath.Vector4)">UnaryNegation(Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 operator -(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_UnmultiplyColor_VRageMath_Vector4_">ColorExtensions.UnmultiplyColor(Vector4)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_PremultiplyColor_VRageMath_Vector4_">ColorExtensions.PremultiplyColor(Vector4)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToSRGB_VRageMath_Vector4_">ColorExtensions.ToSRGB(Vector4)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToLinearRGB_VRageMath_Vector4_">ColorExtensions.ToLinearRGB(Vector4)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToGray_VRageMath_Vector4_">ColorExtensions.ToGray(Vector4)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
