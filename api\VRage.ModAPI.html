﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.ModAPI
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.ModAPI
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI">
  
  <h1 id="VRage_ModAPI" data-uid="VRage.ModAPI" class="text-break">Namespace VRage.ModAPI
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></h4>
      <section><p>Entity flags.</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.ModAPIMass.html">ModAPIMass</a></h4>
      <section><p>ModAPI struct, that is available for modders. Copy of <span class="xref">Havok.HkMassProperties</span>.
Created with IMyPhysics.CreateMassCombined, IMyPhysics.CreateMassForBox, IMyPhysics.CreateMassForCapsule, IMyPhysics.CreateMassForCylinder, IMyPhysics.CreateMassForSphere.</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.ModAPIMassElement.html">ModAPIMassElement</a></h4>
      <section><p>ModAPI struct, that is available for modders. Copy of <span class="xref">Havok.HkMassElement</span>.</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.MyAPIGatewayShortcuts.html">MyAPIGatewayShortcuts</a></h4>
      <section><p>Links to modapi actions. Delegates are set inside MyAPIGateway.
VRAGE TODO: This is probably a temporary class helping us to remove sandbox.</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.MyAPIGatewayShortcuts.GetLocalPlayerPositionCallback.html">MyAPIGatewayShortcuts.GetLocalPlayerPositionCallback</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.MyAPIGatewayShortcuts.GetMainCameraCallback.html">MyAPIGatewayShortcuts.GetMainCameraCallback</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.html">MyAPIGatewayShortcuts.GetWorldBoundariesCallback</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.MyEntityUpdateEnum.html">MyEntityUpdateEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.MyTerminalControlComboBoxItem.html">MyTerminalControlComboBoxItem</a></h4>
      <section><p>Implements Combo Box Item pair</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.MyTerminalControlListBoxItem.html">MyTerminalControlListBoxItem</a></h4>
      <section><p>This is a list box item in a list box terminal control</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.PhysicsSettings.html">PhysicsSettings</a></h4>
      <section><p>Implements Physics Settings</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.VoxelOperatorFlags.html">VoxelOperatorFlags</a></h4>
      <section></section>
    <h3 id="interfaces">Interfaces
  </h3>
      <h4><a class="xref" href="VRage.ModAPI.IMyCamera.html">IMyCamera</a></h4>
      <section><p>Describes camera (mods interface)</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IMyEntities.html">IMyEntities</a></h4>
      <section><p>Provides API, that granting access to enitities (mods interface)</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.IMyGridConveyorSystem.html">IMyGridConveyorSystem</a></h4>
      <section><p>ModAPI interface giving access to grid-group conveyor system</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IMyHudStat.html">IMyHudStat</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ModAPI.IMyRemapHelper.html">IMyRemapHelper</a></h4>
      <section><p>ModAPI interface giving access to changing id and names of entities</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IMyStorage.html">IMyStorage</a></h4>
      <section><p>ModAPI interface giving access to voxel functions</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IMyVoxelBase.html">IMyVoxelBase</a></h4>
      <section><p>Describes</p>
</section>
      <h4><a class="xref" href="VRage.ModAPI.IVoxelOperator.html">IVoxelOperator</a></h4>
      <section><p>Interface, that is used in <a class="xref" href="VRage.ModAPI.IMyStorage.html#VRage_ModAPI_IMyStorage_ExecuteOperationFast__1___0__VRage_Voxels_MyStorageDataTypeFlags_VRageMath_Vector3I__VRageMath_Vector3I__System_Boolean_">ExecuteOperationFast&lt;TVoxelOperator&gt;(ref TVoxelOperator, MyStorageDataTypeFlags, ref Vector3I, ref Vector3I, Boolean)</a></p>
</section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
