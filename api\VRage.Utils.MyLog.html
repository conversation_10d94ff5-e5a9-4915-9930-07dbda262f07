﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyLog
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyLog
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils.MyLog">
  
  
  <h1 id="VRage_Utils_MyLog" data-uid="VRage.Utils.MyLog" class="text-break">Class MyLog
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyLog</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Utils.html">VRage.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Utils_MyLog_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyLog : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Utils_MyLog__ctor_" data-uid="VRage.Utils.MyLog.#ctor*"></a>
  <h4 id="VRage_Utils_MyLog__ctor_System_Boolean_" data-uid="VRage.Utils.MyLog.#ctor(System.Boolean)">MyLog(Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyLog(bool alwaysFlush = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">alwaysFlush</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Utils_MyLog_AssertLevel" data-uid="VRage.Utils.MyLog.AssertLevel">AssertLevel</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyLogSeverity AssertLevel</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyLogSeverity.html">MyLogSeverity</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Utils_MyLog_OnLog" data-uid="VRage.Utils.MyLog.OnLog">OnLog</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Action&lt;MyLogSeverity, StringBuilder&gt; OnLog</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.Utils.MyLogSeverity.html">MyLogSeverity</a>, <span class="xref">System.Text.StringBuilder</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Utils_MyLog_ReportOutOfStorageAction" data-uid="VRage.Utils.MyLog.ReportOutOfStorageAction">ReportOutOfStorageAction</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Action ReportOutOfStorageAction</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Utils_MyLog_Default_" data-uid="VRage.Utils.MyLog.Default*"></a>
  <h4 id="VRage_Utils_MyLog_Default" data-uid="VRage.Utils.MyLog.Default">Default</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyLog Default { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyLog.html">MyLog</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_LogEnabled_" data-uid="VRage.Utils.MyLog.LogEnabled*"></a>
  <h4 id="VRage_Utils_MyLog_LogEnabled" data-uid="VRage.Utils.MyLog.LogEnabled">LogEnabled</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool LogEnabled { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Options_" data-uid="VRage.Utils.MyLog.Options*"></a>
  <h4 id="VRage_Utils_MyLog_Options" data-uid="VRage.Utils.MyLog.Options">Options</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LoggingOptions Options { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Utils_MyLog_AppendToClosedLog_" data-uid="VRage.Utils.MyLog.AppendToClosedLog*"></a>
  <h4 id="VRage_Utils_MyLog_AppendToClosedLog_System_Exception_" data-uid="VRage.Utils.MyLog.AppendToClosedLog(System.Exception)">AppendToClosedLog(Exception)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AppendToClosedLog(Exception e)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Exception</span></td>
        <td><span class="parametername">e</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_AppendToClosedLog_" data-uid="VRage.Utils.MyLog.AppendToClosedLog*"></a>
  <h4 id="VRage_Utils_MyLog_AppendToClosedLog_System_String_" data-uid="VRage.Utils.MyLog.AppendToClosedLog(System.String)">AppendToClosedLog(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AppendToClosedLog(string text)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">text</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Close_" data-uid="VRage.Utils.MyLog.Close*"></a>
  <h4 id="VRage_Utils_MyLog_Close_System_Boolean_" data-uid="VRage.Utils.MyLog.Close(System.Boolean)">Close(Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Close(bool enableWriting = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">enableWriting</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_DecreaseIndent_" data-uid="VRage.Utils.MyLog.DecreaseIndent*"></a>
  <h4 id="VRage_Utils_MyLog_DecreaseIndent" data-uid="VRage.Utils.MyLog.DecreaseIndent">DecreaseIndent()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DecreaseIndent()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyLog_DecreaseIndent_" data-uid="VRage.Utils.MyLog.DecreaseIndent*"></a>
  <h4 id="VRage_Utils_MyLog_DecreaseIndent_VRage_Utils_LoggingOptions_" data-uid="VRage.Utils.MyLog.DecreaseIndent(VRage.Utils.LoggingOptions)">DecreaseIndent(LoggingOptions)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DecreaseIndent(LoggingOptions option)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></td>
        <td><span class="parametername">option</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Flush_" data-uid="VRage.Utils.MyLog.Flush*"></a>
  <h4 id="VRage_Utils_MyLog_Flush" data-uid="VRage.Utils.MyLog.Flush">Flush()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Flush()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyLog_GetFilePath_" data-uid="VRage.Utils.MyLog.GetFilePath*"></a>
  <h4 id="VRage_Utils_MyLog_GetFilePath" data-uid="VRage.Utils.MyLog.GetFilePath">GetFilePath()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string GetFilePath()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_GetTextWriter_" data-uid="VRage.Utils.MyLog.GetTextWriter*"></a>
  <h4 id="VRage_Utils_MyLog_GetTextWriter" data-uid="VRage.Utils.MyLog.GetTextWriter">GetTextWriter()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TextWriter GetTextWriter()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.IO.TextWriter</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_IncreaseIndent_" data-uid="VRage.Utils.MyLog.IncreaseIndent*"></a>
  <h4 id="VRage_Utils_MyLog_IncreaseIndent" data-uid="VRage.Utils.MyLog.IncreaseIndent">IncreaseIndent()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void IncreaseIndent()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyLog_IncreaseIndent_" data-uid="VRage.Utils.MyLog.IncreaseIndent*"></a>
  <h4 id="VRage_Utils_MyLog_IncreaseIndent_VRage_Utils_LoggingOptions_" data-uid="VRage.Utils.MyLog.IncreaseIndent(VRage.Utils.LoggingOptions)">IncreaseIndent(LoggingOptions)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void IncreaseIndent(LoggingOptions option)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></td>
        <td><span class="parametername">option</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_IndentUsing_" data-uid="VRage.Utils.MyLog.IndentUsing*"></a>
  <h4 id="VRage_Utils_MyLog_IndentUsing_VRage_Utils_LoggingOptions_" data-uid="VRage.Utils.MyLog.IndentUsing(VRage.Utils.LoggingOptions)">IndentUsing(LoggingOptions)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyLog.IndentToken IndentUsing(LoggingOptions options)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></td>
        <td><span class="parametername">options</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyLog.IndentToken.html">MyLog.IndentToken</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Init_" data-uid="VRage.Utils.MyLog.Init*"></a>
  <h4 id="VRage_Utils_MyLog_Init_System_String_System_Text_StringBuilder_" data-uid="VRage.Utils.MyLog.Init(System.String,System.Text.StringBuilder)">Init(String, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Init(string logFileName, StringBuilder appVersionString)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">logFileName</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">appVersionString</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_InitWithDate_" data-uid="VRage.Utils.MyLog.InitWithDate*"></a>
  <h4 id="VRage_Utils_MyLog_InitWithDate_System_String_System_Text_StringBuilder_System_Int32_" data-uid="VRage.Utils.MyLog.InitWithDate(System.String,System.Text.StringBuilder,System.Int32)">InitWithDate(String, StringBuilder, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InitWithDate(string logNameBaseName, StringBuilder appVersionString, int maxLogAgeInDays)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">logNameBaseName</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">appVersionString</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">maxLogAgeInDays</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_IsIndentKeyIncreased_" data-uid="VRage.Utils.MyLog.IsIndentKeyIncreased*"></a>
  <h4 id="VRage_Utils_MyLog_IsIndentKeyIncreased" data-uid="VRage.Utils.MyLog.IsIndentKeyIncreased">IsIndentKeyIncreased()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsIndentKeyIncreased()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Log_" data-uid="VRage.Utils.MyLog.Log*"></a>
  <h4 id="VRage_Utils_MyLog_Log_VRage_Utils_MyLogSeverity_System_String_System_Object___" data-uid="VRage.Utils.MyLog.Log(VRage.Utils.MyLogSeverity,System.String,System.Object[])">Log(MyLogSeverity, String, Object[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Log(MyLogSeverity severity, string format, params object[] args)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyLogSeverity.html">MyLogSeverity</a></td>
        <td><span class="parametername">severity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">format</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span>[]</td>
        <td><span class="parametername">args</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_Log_" data-uid="VRage.Utils.MyLog.Log*"></a>
  <h4 id="VRage_Utils_MyLog_Log_VRage_Utils_MyLogSeverity_System_Text_StringBuilder_" data-uid="VRage.Utils.MyLog.Log(VRage.Utils.MyLogSeverity,System.Text.StringBuilder)">Log(MyLogSeverity, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Log(MyLogSeverity severity, StringBuilder builder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyLogSeverity.html">MyLogSeverity</a></td>
        <td><span class="parametername">severity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">builder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_LogThreadPoolInfo_" data-uid="VRage.Utils.MyLog.LogThreadPoolInfo*"></a>
  <h4 id="VRage_Utils_MyLog_LogThreadPoolInfo" data-uid="VRage.Utils.MyLog.LogThreadPoolInfo">LogThreadPoolInfo()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void LogThreadPoolInfo()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyLog_ReportCodeLine_" data-uid="VRage.Utils.MyLog.ReportCodeLine*"></a>
  <h4 id="VRage_Utils_MyLog_ReportCodeLine_System_String_System_Int32_" data-uid="VRage.Utils.MyLog.ReportCodeLine(System.String,System.Int32)">ReportCodeLine(String, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReportCodeLine(string caller = null, int lineNumber = -1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">caller</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lineNumber</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteLine_" data-uid="VRage.Utils.MyLog.WriteLine*"></a>
  <h4 id="VRage_Utils_MyLog_WriteLine_System_Exception_" data-uid="VRage.Utils.MyLog.WriteLine(System.Exception)">WriteLine(Exception)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteLine(Exception ex)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Exception</span></td>
        <td><span class="parametername">ex</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteLine_" data-uid="VRage.Utils.MyLog.WriteLine*"></a>
  <h4 id="VRage_Utils_MyLog_WriteLine_System_String_" data-uid="VRage.Utils.MyLog.WriteLine(System.String)">WriteLine(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteLine(string msg)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">msg</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteLine_" data-uid="VRage.Utils.MyLog.WriteLine*"></a>
  <h4 id="VRage_Utils_MyLog_WriteLine_System_String_VRage_Utils_LoggingOptions_" data-uid="VRage.Utils.MyLog.WriteLine(System.String,VRage.Utils.LoggingOptions)">WriteLine(String, LoggingOptions)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteLine(string message, LoggingOptions option)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">message</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></td>
        <td><span class="parametername">option</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteLineAndConsole_" data-uid="VRage.Utils.MyLog.WriteLineAndConsole*"></a>
  <h4 id="VRage_Utils_MyLog_WriteLineAndConsole_System_String_" data-uid="VRage.Utils.MyLog.WriteLineAndConsole(System.String)">WriteLineAndConsole(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteLineAndConsole(string msg)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">msg</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteLineToConsole_" data-uid="VRage.Utils.MyLog.WriteLineToConsole*"></a>
  <h4 id="VRage_Utils_MyLog_WriteLineToConsole_System_String_" data-uid="VRage.Utils.MyLog.WriteLineToConsole(System.String)">WriteLineToConsole(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteLineToConsole(string msg)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">msg</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteMemoryUsage_" data-uid="VRage.Utils.MyLog.WriteMemoryUsage*"></a>
  <h4 id="VRage_Utils_MyLog_WriteMemoryUsage_System_String_" data-uid="VRage.Utils.MyLog.WriteMemoryUsage(System.String)">WriteMemoryUsage(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteMemoryUsage(string prefixText)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">prefixText</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyLog_WriteToLogAndAssert_" data-uid="VRage.Utils.MyLog.WriteToLogAndAssert*"></a>
  <h4 id="VRage_Utils_MyLog_WriteToLogAndAssert_System_String_" data-uid="VRage.Utils.MyLog.WriteToLogAndAssert(System.String)">WriteToLogAndAssert(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteToLogAndAssert(string message)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">message</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Debug_VRage_Utils_MyLog_System_String_System_Object___">MyLogExtensions.Debug(MyLog, String, Object[])</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Debug_VRage_Utils_MyLog_System_Text_StringBuilder_">MyLogExtensions.Debug(MyLog, StringBuilder)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Info_VRage_Utils_MyLog_System_String_System_Object___">MyLogExtensions.Info(MyLog, String, Object[])</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Info_VRage_Utils_MyLog_System_Text_StringBuilder_">MyLogExtensions.Info(MyLog, StringBuilder)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Warning_VRage_Utils_MyLog_System_String_System_Object___">MyLogExtensions.Warning(MyLog, String, Object[])</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Warning_VRage_Utils_MyLog_System_Text_StringBuilder_">MyLogExtensions.Warning(MyLog, StringBuilder)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Error_VRage_Utils_MyLog_System_String_System_Object___">MyLogExtensions.Error(MyLog, String, Object[])</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Error_VRage_Utils_MyLog_System_Text_StringBuilder_">MyLogExtensions.Error(MyLog, StringBuilder)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Critical_VRage_Utils_MyLog_System_String_System_Object___">MyLogExtensions.Critical(MyLog, String, Object[])</a>
  </div>
  <div>
      <a class="xref" href="VRage.Utils.MyLogExtensions.html#VRage_Utils_MyLogExtensions_Critical_VRage_Utils_MyLog_System_Text_StringBuilder_">MyLogExtensions.Critical(MyLog, StringBuilder)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
