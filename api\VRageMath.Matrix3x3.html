﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Matrix3x3
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Matrix3x3
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Matrix3x3">
  
  
  <h1 id="VRageMath_Matrix3x3" data-uid="VRageMath.Matrix3x3" class="text-break">Class Matrix3x3
  </h1>
  <div class="markdown level0 summary"><p>Defines a matrix.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Matrix3x3</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Matrix3x3_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Matrix3x3 : ValueType, IEquatable&lt;Matrix3x3&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Matrix3x3__ctor_" data-uid="VRageMath.Matrix3x3.#ctor*"></a>
  <h4 id="VRageMath_Matrix3x3__ctor_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix3x3.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">Matrix3x3(Single, Single, Single, Single, Single, Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix3x3(float m11, float m12, float m13, float m21, float m22, float m23, float m31, float m32, float m33)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m11</span></td>
        <td><p>Value to initialize m11 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m12</span></td>
        <td><p>Value to initialize m12 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m13</span></td>
        <td><p>Value to initialize m13 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m21</span></td>
        <td><p>Value to initialize m21 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m22</span></td>
        <td><p>Value to initialize m22 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m23</span></td>
        <td><p>Value to initialize m23 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m31</span></td>
        <td><p>Value to initialize m31 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m32</span></td>
        <td><p>Value to initialize m32 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m33</span></td>
        <td><p>Value to initialize m33 to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3__ctor_" data-uid="VRageMath.Matrix3x3.#ctor*"></a>
  <h4 id="VRageMath_Matrix3x3__ctor_VRageMath_Matrix3x3_" data-uid="VRageMath.Matrix3x3.#ctor(VRageMath.Matrix3x3)">Matrix3x3(Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix3x3(Matrix3x3 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3__ctor_" data-uid="VRageMath.Matrix3x3.#ctor*"></a>
  <h4 id="VRageMath_Matrix3x3__ctor_VRageMath_MatrixD_" data-uid="VRageMath.Matrix3x3.#ctor(VRageMath.MatrixD)">Matrix3x3(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix3x3(MatrixD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Matrix3x3_Identity" data-uid="VRageMath.Matrix3x3.Identity">Identity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Identity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M11" data-uid="VRageMath.Matrix3x3.M11">M11</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M11</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M12" data-uid="VRageMath.Matrix3x3.M12">M12</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M12</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M13" data-uid="VRageMath.Matrix3x3.M13">M13</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M13</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M21" data-uid="VRageMath.Matrix3x3.M21">M21</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M21</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M22" data-uid="VRageMath.Matrix3x3.M22">M22</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M22</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M23" data-uid="VRageMath.Matrix3x3.M23">M23</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M23</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M31" data-uid="VRageMath.Matrix3x3.M31">M31</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M31</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M32" data-uid="VRageMath.Matrix3x3.M32">M32</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M32</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_M33" data-uid="VRageMath.Matrix3x3.M33">M33</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M33</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix3x3_Zero" data-uid="VRageMath.Matrix3x3.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Matrix3x3_Backward_" data-uid="VRageMath.Matrix3x3.Backward*"></a>
  <h4 id="VRageMath_Matrix3x3_Backward" data-uid="VRageMath.Matrix3x3.Backward">Backward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the backward vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Backward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Col0_" data-uid="VRageMath.Matrix3x3.Col0*"></a>
  <h4 id="VRageMath_Matrix3x3_Col0" data-uid="VRageMath.Matrix3x3.Col0">Col0</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col0 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Col1_" data-uid="VRageMath.Matrix3x3.Col1*"></a>
  <h4 id="VRageMath_Matrix3x3_Col1" data-uid="VRageMath.Matrix3x3.Col1">Col1</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col1 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Col2_" data-uid="VRageMath.Matrix3x3.Col2*"></a>
  <h4 id="VRageMath_Matrix3x3_Col2" data-uid="VRageMath.Matrix3x3.Col2">Col2</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col2 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Down_" data-uid="VRageMath.Matrix3x3.Down*"></a>
  <h4 id="VRageMath_Matrix3x3_Down" data-uid="VRageMath.Matrix3x3.Down">Down</h4>
  <div class="markdown level1 summary"><p>Gets and sets the down vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Down { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Forward_" data-uid="VRageMath.Matrix3x3.Forward*"></a>
  <h4 id="VRageMath_Matrix3x3_Forward" data-uid="VRageMath.Matrix3x3.Forward">Forward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the forward vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Forward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Item_" data-uid="VRageMath.Matrix3x3.Item*"></a>
  <h4 id="VRageMath_Matrix3x3_Item_System_Int32_System_Int32_" data-uid="VRageMath.Matrix3x3.Item(System.Int32,System.Int32)">Item[Int32, Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float this[int row, int column] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">column</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Left_" data-uid="VRageMath.Matrix3x3.Left*"></a>
  <h4 id="VRageMath_Matrix3x3_Left" data-uid="VRageMath.Matrix3x3.Left">Left</h4>
  <div class="markdown level1 summary"><p>Gets and sets the left vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Left { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Right_" data-uid="VRageMath.Matrix3x3.Right*"></a>
  <h4 id="VRageMath_Matrix3x3_Right" data-uid="VRageMath.Matrix3x3.Right">Right</h4>
  <div class="markdown level1 summary"><p>Gets and sets the right vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Right { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Scale_" data-uid="VRageMath.Matrix3x3.Scale*"></a>
  <h4 id="VRageMath_Matrix3x3_Scale" data-uid="VRageMath.Matrix3x3.Scale">Scale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Scale { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Up_" data-uid="VRageMath.Matrix3x3.Up*"></a>
  <h4 id="VRageMath_Matrix3x3_Up" data-uid="VRageMath.Matrix3x3.Up">Up</h4>
  <div class="markdown level1 summary"><p>Gets and sets the up vector of the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Up { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Matrix3x3_Add_" data-uid="VRageMath.Matrix3x3.Add*"></a>
  <h4 id="VRageMath_Matrix3x3_Add_VRageMath_Matrix3x3__VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Add(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Add(ref Matrix3x3, ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_AlignRotationToAxes_" data-uid="VRageMath.Matrix3x3.AlignRotationToAxes*"></a>
  <h4 id="VRageMath_Matrix3x3_AlignRotationToAxes_VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.AlignRotationToAxes(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">AlignRotationToAxes(ref Matrix3x3, ref Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 AlignRotationToAxes(ref Matrix3x3 toAlign, ref Matrix3x3 axisDefinitionMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">toAlign</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">axisDefinitionMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_AssertIsValid_" data-uid="VRageMath.Matrix3x3.AssertIsValid*"></a>
  <h4 id="VRageMath_Matrix3x3_AssertIsValid" data-uid="VRageMath.Matrix3x3.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromAxisAngle_" data-uid="VRageMath.Matrix3x3.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromAxisAngle_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Matrix3x3.CreateFromAxisAngle(VRageMath.Vector3,System.Single)">CreateFromAxisAngle(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix3x3 that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateFromAxisAngle(Vector3 axis, float angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromAxisAngle_" data-uid="VRageMath.Matrix3x3.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromAxisAngle_VRageMath_Vector3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateFromAxisAngle(VRageMath.Vector3@,System.Single,VRageMath.Matrix3x3@)">CreateFromAxisAngle(ref Vector3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix3x3 that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAxisAngle(ref Vector3 axis, float angle, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromDir_" data-uid="VRageMath.Matrix3x3.CreateFromDir*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromDir_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.CreateFromDir(VRageMath.Vector3)">CreateFromDir(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateFromDir(Vector3 dir)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromDir_" data-uid="VRageMath.Matrix3x3.CreateFromDir*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromDir_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.CreateFromDir(VRageMath.Vector3,VRageMath.Vector3)">CreateFromDir(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateFromDir(Vector3 dir, Vector3 suggestedUp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">suggestedUp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromQuaternion_" data-uid="VRageMath.Matrix3x3.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromQuaternion_VRageMath_Quaternion_" data-uid="VRageMath.Matrix3x3.CreateFromQuaternion(VRageMath.Quaternion)">CreateFromQuaternion(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix3x3 from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateFromQuaternion(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix3x3 from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromQuaternion_" data-uid="VRageMath.Matrix3x3.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromQuaternion_VRageMath_Quaternion__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateFromQuaternion(VRageMath.Quaternion@,VRageMath.Matrix3x3@)">CreateFromQuaternion(ref Quaternion, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix3x3 from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromQuaternion(ref Quaternion quaternion, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix3x3 from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromYawPitchRoll_" data-uid="VRageMath.Matrix3x3.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix3x3.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">CreateFromYawPitchRoll(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateFromYawPitchRoll(float yaw, float pitch, float roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateFromYawPitchRoll_" data-uid="VRageMath.Matrix3x3.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateFromYawPitchRoll(System.Single,System.Single,System.Single,VRageMath.Matrix3x3@)">CreateFromYawPitchRoll(Single, Single, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Fills in a rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromYawPitchRoll(float yaw, float pitch, float roll, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing matrix filled in to represent the specified yaw, pitch, and roll.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationFromTwoVectors_" data-uid="VRageMath.Matrix3x3.CreateRotationFromTwoVectors*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationFromTwoVectors_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateRotationFromTwoVectors(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Matrix3x3@)">CreateRotationFromTwoVectors(ref Vector3, ref Vector3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationFromTwoVectors(ref Vector3 fromVector, ref Vector3 toVector, out Matrix3x3 resultMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">fromVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">toVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">resultMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationX_" data-uid="VRageMath.Matrix3x3.CreateRotationX*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationX_System_Single_" data-uid="VRageMath.Matrix3x3.CreateRotationX(System.Single)">CreateRotationX(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateRotationX(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationX_" data-uid="VRageMath.Matrix3x3.CreateRotationX*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationX_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateRotationX(System.Single,VRageMath.Matrix3x3@)">CreateRotationX(Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationX(float radians, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationY_" data-uid="VRageMath.Matrix3x3.CreateRotationY*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationY_System_Single_" data-uid="VRageMath.Matrix3x3.CreateRotationY(System.Single)">CreateRotationY(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateRotationY(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationY_" data-uid="VRageMath.Matrix3x3.CreateRotationY*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationY_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateRotationY(System.Single,VRageMath.Matrix3x3@)">CreateRotationY(Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationY(float radians, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationZ_" data-uid="VRageMath.Matrix3x3.CreateRotationZ*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationZ_System_Single_" data-uid="VRageMath.Matrix3x3.CreateRotationZ(System.Single)">CreateRotationZ(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateRotationZ(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateRotationZ_" data-uid="VRageMath.Matrix3x3.CreateRotationZ*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateRotationZ_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateRotationZ(System.Single,VRageMath.Matrix3x3@)">CreateRotationZ(Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationZ(float radians, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The rotation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_System_Single_" data-uid="VRageMath.Matrix3x3.CreateScale(System.Single)">CreateScale(Single)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateScale(float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Amount to scale by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix3x3.CreateScale(System.Single,System.Single,System.Single)">CreateScale(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateScale(float xScale, float yScale, float zScale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_System_Single_System_Single_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateScale(System.Single,System.Single,System.Single,VRageMath.Matrix3x3@)">CreateScale(Single, Single, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(float xScale, float yScale, float zScale, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateScale(System.Single,VRageMath.Matrix3x3@)">CreateScale(Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(float scale, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Value to scale by.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.CreateScale(VRageMath.Vector3)">CreateScale(Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateScale(Vector3 scales)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateScale_" data-uid="VRageMath.Matrix3x3.CreateScale*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateScale_VRageMath_Vector3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.CreateScale(VRageMath.Vector3@,VRageMath.Matrix3x3@)">CreateScale(ref Vector3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(ref Vector3 scales, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_CreateWorld_" data-uid="VRageMath.Matrix3x3.CreateWorld*"></a>
  <h4 id="VRageMath_Matrix3x3_CreateWorld_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Matrix3x3.CreateWorld(VRageMath.Vector3@,VRageMath.Vector3@)">CreateWorld(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a world matrix with the specified parameters.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 CreateWorld(ref Vector3 forward, ref Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td><p>Forward direction of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td><p>Upward direction of the object; usually [0, 1, 0].</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Determinant_" data-uid="VRageMath.Matrix3x3.Determinant*"></a>
  <h4 id="VRageMath_Matrix3x3_Determinant" data-uid="VRageMath.Matrix3x3.Determinant">Determinant()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Determinant()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Divide_" data-uid="VRageMath.Matrix3x3.Divide*"></a>
  <h4 id="VRageMath_Matrix3x3_Divide_VRageMath_Matrix3x3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Divide(VRageMath.Matrix3x3@,System.Single,VRageMath.Matrix3x3@)">Divide(ref Matrix3x3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Matrix3x3 matrix1, float divider, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Divide_" data-uid="VRageMath.Matrix3x3.Divide*"></a>
  <h4 id="VRageMath_Matrix3x3_Divide_VRageMath_Matrix3x3__VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Divide(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Divide(ref Matrix3x3, ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Equals_" data-uid="VRageMath.Matrix3x3.Equals*"></a>
  <h4 id="VRageMath_Matrix3x3_Equals_System_Object_" data-uid="VRageMath.Matrix3x3.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Equals_" data-uid="VRageMath.Matrix3x3.Equals*"></a>
  <h4 id="VRageMath_Matrix3x3_Equals_VRageMath_Matrix3x3_" data-uid="VRageMath.Matrix3x3.Equals(VRageMath.Matrix3x3)">Equals(Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Matrix3x3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Matrix3x3 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Matrix3x3.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_EqualsFast_" data-uid="VRageMath.Matrix3x3.EqualsFast*"></a>
  <h4 id="VRageMath_Matrix3x3_EqualsFast_VRageMath_Matrix3x3__System_Single_" data-uid="VRageMath.Matrix3x3.EqualsFast(VRageMath.Matrix3x3@,System.Single)">EqualsFast(ref Matrix3x3, Single)</h4>
  <div class="markdown level1 summary"><p>Compares just position, forward and up</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool EqualsFast(ref Matrix3x3 other, float epsilon = 0.0001F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetClosestDirection_" data-uid="VRageMath.Matrix3x3.GetClosestDirection*"></a>
  <h4 id="VRageMath_Matrix3x3_GetClosestDirection_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.GetClosestDirection(VRageMath.Vector3)">GetClosestDirection(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(Vector3 referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetClosestDirection_" data-uid="VRageMath.Matrix3x3.GetClosestDirection*"></a>
  <h4 id="VRageMath_Matrix3x3_GetClosestDirection_VRageMath_Vector3__" data-uid="VRageMath.Matrix3x3.GetClosestDirection(VRageMath.Vector3@)">GetClosestDirection(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(ref Vector3 referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetDirectionVector_" data-uid="VRageMath.Matrix3x3.GetDirectionVector*"></a>
  <h4 id="VRageMath_Matrix3x3_GetDirectionVector_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.Matrix3x3.GetDirectionVector(VRageMath.Base6Directions.Direction)">GetDirectionVector(Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"><p>Gets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 GetDirectionVector(Base6Directions.Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetEulerAnglesXYZ_" data-uid="VRageMath.Matrix3x3.GetEulerAnglesXYZ*"></a>
  <h4 id="VRageMath_Matrix3x3_GetEulerAnglesXYZ_VRageMath_Matrix3x3__VRageMath_Vector3__" data-uid="VRageMath.Matrix3x3.GetEulerAnglesXYZ(VRageMath.Matrix3x3@,VRageMath.Vector3@)">GetEulerAnglesXYZ(ref Matrix3x3, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetEulerAnglesXYZ(ref Matrix3x3 mat, out Vector3 xyz)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">mat</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">xyz</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetHashCode_" data-uid="VRageMath.Matrix3x3.GetHashCode*"></a>
  <h4 id="VRageMath_Matrix3x3_GetHashCode" data-uid="VRageMath.Matrix3x3.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetOrientation_" data-uid="VRageMath.Matrix3x3.GetOrientation*"></a>
  <h4 id="VRageMath_Matrix3x3_GetOrientation" data-uid="VRageMath.Matrix3x3.GetOrientation">GetOrientation()</h4>
  <div class="markdown level1 summary"><p>Gets the orientation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix3x3 GetOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_GetRow_" data-uid="VRageMath.Matrix3x3.GetRow*"></a>
  <h4 id="VRageMath_Matrix3x3_GetRow_System_Int32_" data-uid="VRageMath.Matrix3x3.GetRow(System.Int32)">GetRow(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 GetRow(int row)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Invert_" data-uid="VRageMath.Matrix3x3.Invert*"></a>
  <h4 id="VRageMath_Matrix3x3_Invert_VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Invert(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Invert(ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Calculates the inverse of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Invert(ref Matrix3x3 matrix, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The inverse of matrix. The same matrix can be used for both arguments.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_IsMirrored_" data-uid="VRageMath.Matrix3x3.IsMirrored*"></a>
  <h4 id="VRageMath_Matrix3x3_IsMirrored" data-uid="VRageMath.Matrix3x3.IsMirrored">IsMirrored()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsMirrored()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_IsNan_" data-uid="VRageMath.Matrix3x3.IsNan*"></a>
  <h4 id="VRageMath_Matrix3x3_IsNan" data-uid="VRageMath.Matrix3x3.IsNan">IsNan()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsNan()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_IsOrthogonal_" data-uid="VRageMath.Matrix3x3.IsOrthogonal*"></a>
  <h4 id="VRageMath_Matrix3x3_IsOrthogonal" data-uid="VRageMath.Matrix3x3.IsOrthogonal">IsOrthogonal()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsOrthogonal()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_IsRotation_" data-uid="VRageMath.Matrix3x3.IsRotation*"></a>
  <h4 id="VRageMath_Matrix3x3_IsRotation" data-uid="VRageMath.Matrix3x3.IsRotation">IsRotation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRotation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_IsValid_" data-uid="VRageMath.Matrix3x3.IsValid*"></a>
  <h4 id="VRageMath_Matrix3x3_IsValid" data-uid="VRageMath.Matrix3x3.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Lerp_" data-uid="VRageMath.Matrix3x3.Lerp*"></a>
  <h4 id="VRageMath_Matrix3x3_Lerp_VRageMath_Matrix3x3__VRageMath_Matrix3x3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Lerp(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,System.Single,VRageMath.Matrix3x3@)">Lerp(ref Matrix3x3, ref Matrix3x3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between the corresponding values of two matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, float amount, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Multiply_" data-uid="VRageMath.Matrix3x3.Multiply*"></a>
  <h4 id="VRageMath_Matrix3x3_Multiply_VRageMath_Matrix3x3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Multiply(VRageMath.Matrix3x3@,System.Single,VRageMath.Matrix3x3@)">Multiply(ref Matrix3x3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Matrix3x3 matrix1, float scaleFactor, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Multiply_" data-uid="VRageMath.Matrix3x3.Multiply*"></a>
  <h4 id="VRageMath_Matrix3x3_Multiply_VRageMath_Matrix3x3__VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Multiply(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Multiply(ref Matrix3x3, ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Negate_" data-uid="VRageMath.Matrix3x3.Negate*"></a>
  <h4 id="VRageMath_Matrix3x3_Negate_VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Negate(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Negate(ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Matrix3x3 matrix, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Negated matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Normalize_" data-uid="VRageMath.Matrix3x3.Normalize*"></a>
  <h4 id="VRageMath_Matrix3x3_Normalize_VRageMath_Matrix3x3_" data-uid="VRageMath.Matrix3x3.Normalize(VRageMath.Matrix3x3)">Normalize(Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Normalize(Matrix3x3 matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Orthogonalize_" data-uid="VRageMath.Matrix3x3.Orthogonalize*"></a>
  <h4 id="VRageMath_Matrix3x3_Orthogonalize_VRageMath_Matrix3x3_" data-uid="VRageMath.Matrix3x3.Orthogonalize(VRageMath.Matrix3x3)">Orthogonalize(Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Orthogonalize(Matrix3x3 rotationMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">rotationMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Rescale_" data-uid="VRageMath.Matrix3x3.Rescale*"></a>
  <h4 id="VRageMath_Matrix3x3_Rescale_VRageMath_Matrix3x3_System_Single_" data-uid="VRageMath.Matrix3x3.Rescale(VRageMath.Matrix3x3,System.Single)">Rescale(Matrix3x3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Rescale(Matrix3x3 matrix, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Rescale_" data-uid="VRageMath.Matrix3x3.Rescale*"></a>
  <h4 id="VRageMath_Matrix3x3_Rescale_VRageMath_Matrix3x3_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.Rescale(VRageMath.Matrix3x3,VRageMath.Vector3)">Rescale(Matrix3x3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Rescale(Matrix3x3 matrix, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Rescale_" data-uid="VRageMath.Matrix3x3.Rescale*"></a>
  <h4 id="VRageMath_Matrix3x3_Rescale_VRageMath_Matrix3x3__System_Single_" data-uid="VRageMath.Matrix3x3.Rescale(VRageMath.Matrix3x3@,System.Single)">Rescale(ref Matrix3x3, Single)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix3x3.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref Matrix3x3 matrix, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Rescale_" data-uid="VRageMath.Matrix3x3.Rescale*"></a>
  <h4 id="VRageMath_Matrix3x3_Rescale_VRageMath_Matrix3x3__VRageMath_Vector3__" data-uid="VRageMath.Matrix3x3.Rescale(VRageMath.Matrix3x3@,VRageMath.Vector3@)">Rescale(ref Matrix3x3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix3x3.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref Matrix3x3 matrix, ref Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Round_" data-uid="VRageMath.Matrix3x3.Round*"></a>
  <h4 id="VRageMath_Matrix3x3_Round_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Round(VRageMath.Matrix3x3@)">Round(ref Matrix3x3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix3x3 Round(ref Matrix3x3 matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_SetDirectionVector_" data-uid="VRageMath.Matrix3x3.SetDirectionVector*"></a>
  <h4 id="VRageMath_Matrix3x3_SetDirectionVector_VRageMath_Base6Directions_Direction_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.SetDirectionVector(VRageMath.Base6Directions.Direction,VRageMath.Vector3)">SetDirectionVector(Base6Directions.Direction, Vector3)</h4>
  <div class="markdown level1 summary"><p>Sets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDirectionVector(Base6Directions.Direction direction, Vector3 newValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">newValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_SetRow_" data-uid="VRageMath.Matrix3x3.SetRow*"></a>
  <h4 id="VRageMath_Matrix3x3_SetRow_System_Int32_VRageMath_Vector3_" data-uid="VRageMath.Matrix3x3.SetRow(System.Int32,VRageMath.Vector3)">SetRow(Int32, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRow(int row, Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Slerp_" data-uid="VRageMath.Matrix3x3.Slerp*"></a>
  <h4 id="VRageMath_Matrix3x3_Slerp_VRageMath_Matrix3x3__VRageMath_Matrix3x3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Slerp(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,System.Single,VRageMath.Matrix3x3@)">Slerp(ref Matrix3x3, ref Matrix3x3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, float amount, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_SlerpScale_" data-uid="VRageMath.Matrix3x3.SlerpScale*"></a>
  <h4 id="VRageMath_Matrix3x3_SlerpScale_VRageMath_Matrix3x3__VRageMath_Matrix3x3__System_Single_VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.SlerpScale(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,System.Single,VRageMath.Matrix3x3@)">SlerpScale(ref Matrix3x3, ref Matrix3x3, Single, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SlerpScale(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, float amount, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Subtract_" data-uid="VRageMath.Matrix3x3.Subtract*"></a>
  <h4 id="VRageMath_Matrix3x3_Subtract_VRageMath_Matrix3x3__VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Subtract(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Subtract(ref Matrix3x3, ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Matrix3x3 matrix1, ref Matrix3x3 matrix2, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_ToString_" data-uid="VRageMath.Matrix3x3.ToString*"></a>
  <h4 id="VRageMath_Matrix3x3_ToString" data-uid="VRageMath.Matrix3x3.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Transform_" data-uid="VRageMath.Matrix3x3.Transform*"></a>
  <h4 id="VRageMath_Matrix3x3_Transform_VRageMath_Matrix3x3__VRageMath_Quaternion__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Transform(VRageMath.Matrix3x3@,VRageMath.Quaternion@,VRageMath.Matrix3x3@)">Transform(ref Matrix3x3, ref Quaternion, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Transforms a Matrix3x3 by applying a Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Matrix3x3 value, ref Quaternion rotation, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Matrix3x3 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply, expressed as a Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Matrix3x3 filled in with the result of the transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix3x3_Transpose_" data-uid="VRageMath.Matrix3x3.Transpose*"></a>
  <h4 id="VRageMath_Matrix3x3_Transpose" data-uid="VRageMath.Matrix3x3.Transpose">Transpose()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Transpose()</code></pre>
  </div>
  
  
  <a id="VRageMath_Matrix3x3_Transpose_" data-uid="VRageMath.Matrix3x3.Transpose*"></a>
  <h4 id="VRageMath_Matrix3x3_Transpose_VRageMath_Matrix3x3__VRageMath_Matrix3x3__" data-uid="VRageMath.Matrix3x3.Transpose(VRageMath.Matrix3x3@,VRageMath.Matrix3x3@)">Transpose(ref Matrix3x3, out Matrix3x3)</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transpose(ref Matrix3x3 matrix, out Matrix3x3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Transposed matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
