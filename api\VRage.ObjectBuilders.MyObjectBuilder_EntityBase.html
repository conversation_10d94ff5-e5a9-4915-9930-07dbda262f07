﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_EntityBase
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_EntityBase
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase">
  
  
  <h1 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase" class="text-break">Class MyObjectBuilder_EntityBase
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><span class="xref">MyObjectBuilder_EntityBase</span></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AutomaticRifle.html">MyObjectBuilder_AutomaticRifle</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_HandDrill.html">MyObjectBuilder_HandDrill</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Meteor.html">MyObjectBuilder_Meteor</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Missile.html">MyObjectBuilder_Missile</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SafeZone.html">MyObjectBuilder_SafeZone</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Waypoint.html">MyObjectBuilder_Waypoint</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_Character.html">MyObjectBuilder_Character</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CubeGrid.html">MyObjectBuilder_CubeGrid</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_CubePlacer.html">MyObjectBuilder_CubePlacer</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_DestroyableItem.html">MyObjectBuilder_DestroyableItem</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EngineerToolBase.html">MyObjectBuilder_EngineerToolBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_EnvironmentItems.html">MyObjectBuilder_EnvironmentItems</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_FloatingObject.html">MyObjectBuilder_FloatingObject</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_GhostCharacter.html">MyObjectBuilder_GhostCharacter</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_HandToolBase.html">MyObjectBuilder_HandToolBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ManipulationTool.html">MyObjectBuilder_ManipulationTool</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ProxyAntenna.html">MyObjectBuilder_ProxyAntenna</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_Tree.html">MyObjectBuilder_Tree</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_VoxelMap.html">MyObjectBuilder_VoxelMap</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_CargoContainerInventoryBagEntity.html">MyObjectBuilder_CargoContainerInventoryBagEntity</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_ForageableEntity.html">MyObjectBuilder_ForageableEntity</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_InventoryBagEntity.html">MyObjectBuilder_InventoryBagEntity</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_ModifiableEntity.html">MyObjectBuilder_ModifiableEntity</a></div>
      <div class="level3"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_ReplicableEntity.html">MyObjectBuilder_ReplicableEntity</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ObjectBuilders.html">VRage.ObjectBuilders</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_EntityBase : MyObjectBuilder_Base</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase__ctor_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.#ctor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase__ctor" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.#ctor">MyObjectBuilder_EntityBase()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_EntityBase()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ComponentContainer" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ComponentContainer">ComponentContainer</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public MyObjectBuilder_ComponentContainer ComponentContainer</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentContainer.html">MyObjectBuilder_ComponentContainer</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_EntityDefinitionId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.EntityDefinitionId">EntityDefinitionId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[NoSerialize]
public Nullable&lt;SerializableDefinitionId&gt; EntityDefinitionId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.ObjectBuilders.SerializableDefinitionId.html">SerializableDefinitionId</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_EntityId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.EntityId">EntityId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long EntityId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_LocalPositionAndOrientation" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.LocalPositionAndOrientation">LocalPositionAndOrientation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;MyPositionAndOrientation&gt; LocalPositionAndOrientation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">VRage.MyPositionAndOrientation</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_Name" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.Name">Name</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public string Name</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_PersistentFlags" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.PersistentFlags">PersistentFlags</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyPersistentEntityFlags2 PersistentFlags</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyPersistentEntityFlags2.html">MyPersistentEntityFlags2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_PositionAndOrientation" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.PositionAndOrientation">PositionAndOrientation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;MyPositionAndOrientation&gt; PositionAndOrientation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">VRage.MyPositionAndOrientation</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_Remap_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.Remap*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_Remap_VRage_ModAPI_IMyRemapHelper_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.Remap(VRage.ModAPI.IMyRemapHelper)">Remap(IMyRemapHelper)</h4>
  <div class="markdown level1 summary"><p>Remaps this entity's entityId to a new value.
If there are cross-referenced between different entities in this object builder, the remapHelper should be used to rememeber these
references and retrieve them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Remap(IMyRemapHelper remapHelper)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyRemapHelper.html">IMyRemapHelper</a></td>
        <td><span class="parametername">remapHelper</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeComponentContainer_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeComponentContainer*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeComponentContainer" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeComponentContainer">ShouldSerializeComponentContainer()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeComponentContainer()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeEntityDefinitionId_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeEntityDefinitionId*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeEntityDefinitionId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeEntityDefinitionId">ShouldSerializeEntityDefinitionId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeEntityDefinitionId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeLocalPositionAndOrientation_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeLocalPositionAndOrientation*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializeLocalPositionAndOrientation" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializeLocalPositionAndOrientation">ShouldSerializeLocalPositionAndOrientation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeLocalPositionAndOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializePositionAndOrientation_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializePositionAndOrientation*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_EntityBase_ShouldSerializePositionAndOrientation" data-uid="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.ShouldSerializePositionAndOrientation">ShouldSerializePositionAndOrientation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializePositionAndOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
