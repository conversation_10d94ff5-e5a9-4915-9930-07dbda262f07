﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyCamera
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyCamera
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyCamera">
  
  
  <h1 id="VRage_ModAPI_IMyCamera" data-uid="VRage.ModAPI.IMyCamera" class="text-break">Interface IMyCamera
  </h1>
  <div class="markdown level0 summary"><p>Describes camera (mods interface)</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyCamera_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyCamera</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ModAPI_IMyCamera_FarPlaneDistance_" data-uid="VRage.ModAPI.IMyCamera.FarPlaneDistance*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_FarPlaneDistance" data-uid="VRage.ModAPI.IMyCamera.FarPlaneDistance">FarPlaneDistance</h4>
  <div class="markdown level1 summary"><p>Gets farplane is set by MyObjectBuilder_SessionSettings.ViewDistance</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float FarPlaneDistance { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_FieldOfViewAngle_" data-uid="VRage.ModAPI.IMyCamera.FieldOfViewAngle*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_FieldOfViewAngle" data-uid="VRage.ModAPI.IMyCamera.FieldOfViewAngle">FieldOfViewAngle</h4>
  <div class="markdown level1 summary"><p>Gets field of view angle in degrees</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float FieldOfViewAngle { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_FieldOfViewAngleForNearObjects_" data-uid="VRage.ModAPI.IMyCamera.FieldOfViewAngleForNearObjects*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_FieldOfViewAngleForNearObjects" data-uid="VRage.ModAPI.IMyCamera.FieldOfViewAngleForNearObjects">FieldOfViewAngleForNearObjects</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float FieldOfViewAngleForNearObjects { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_FovWithZoom_" data-uid="VRage.ModAPI.IMyCamera.FovWithZoom*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_FovWithZoom" data-uid="VRage.ModAPI.IMyCamera.FovWithZoom">FovWithZoom</h4>
  <div class="markdown level1 summary"><p>Gets field of view with zoom</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float FovWithZoom { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_FovWithZoomForNearObjects_" data-uid="VRage.ModAPI.IMyCamera.FovWithZoomForNearObjects*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_FovWithZoomForNearObjects" data-uid="VRage.ModAPI.IMyCamera.FovWithZoomForNearObjects">FovWithZoomForNearObjects</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float FovWithZoomForNearObjects { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_NearPlaneDistance_" data-uid="VRage.ModAPI.IMyCamera.NearPlaneDistance*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_NearPlaneDistance" data-uid="VRage.ModAPI.IMyCamera.NearPlaneDistance">NearPlaneDistance</h4>
  <div class="markdown level1 summary"><p>Gets near plane distance</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float NearPlaneDistance { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_Position_" data-uid="VRage.ModAPI.IMyCamera.Position*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_Position" data-uid="VRage.ModAPI.IMyCamera.Position">Position</h4>
  <div class="markdown level1 summary"><p>Gets camera position</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3D Position { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_PreviousPosition_" data-uid="VRage.ModAPI.IMyCamera.PreviousPosition*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_PreviousPosition" data-uid="VRage.ModAPI.IMyCamera.PreviousPosition">PreviousPosition</h4>
  <div class="markdown level1 summary"><p>Gets camera previous position</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3D PreviousPosition { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_ProjectionMatrix_" data-uid="VRage.ModAPI.IMyCamera.ProjectionMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_ProjectionMatrix" data-uid="VRage.ModAPI.IMyCamera.ProjectionMatrix">ProjectionMatrix</h4>
  <div class="markdown level1 summary"><p>Gets projection matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD ProjectionMatrix { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_ProjectionMatrixForNearObjects_" data-uid="VRage.ModAPI.IMyCamera.ProjectionMatrixForNearObjects*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_ProjectionMatrixForNearObjects" data-uid="VRage.ModAPI.IMyCamera.ProjectionMatrixForNearObjects">ProjectionMatrixForNearObjects</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD ProjectionMatrixForNearObjects { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_ViewMatrix_" data-uid="VRage.ModAPI.IMyCamera.ViewMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_ViewMatrix" data-uid="VRage.ModAPI.IMyCamera.ViewMatrix">ViewMatrix</h4>
  <div class="markdown level1 summary"><p>Gets view matrix when camera in real position</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD ViewMatrix { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_ViewportOffset_" data-uid="VRage.ModAPI.IMyCamera.ViewportOffset*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_ViewportOffset" data-uid="VRage.ModAPI.IMyCamera.ViewportOffset">ViewportOffset</h4>
  <div class="markdown level1 summary"><p>Gets camera viewport offset</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector2 ViewportOffset { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_ViewportSize_" data-uid="VRage.ModAPI.IMyCamera.ViewportSize*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_ViewportSize" data-uid="VRage.ModAPI.IMyCamera.ViewportSize">ViewportSize</h4>
  <div class="markdown level1 summary"><p>Gets camera viewport size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector2 ViewportSize { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_WorldMatrix_" data-uid="VRage.ModAPI.IMyCamera.WorldMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_WorldMatrix" data-uid="VRage.ModAPI.IMyCamera.WorldMatrix">WorldMatrix</h4>
  <div class="markdown level1 summary"><p>Gets camera world matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD WorldMatrix { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyCamera_GetDistanceWithFOV_" data-uid="VRage.ModAPI.IMyCamera.GetDistanceWithFOV*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_GetDistanceWithFOV_VRageMath_Vector3D_" data-uid="VRage.ModAPI.IMyCamera.GetDistanceWithFOV(VRageMath.Vector3D)">GetDistanceWithFOV(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Gets distance from point. Equals to <a class="xref" href="VRageMath.Vector3D.html#VRageMath_Vector3D_Distance_VRageMath_Vector3D_VRageMath_Vector3D_">Distance(Vector3D, Vector3D)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">double GetDistanceWithFOV(Vector3D position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Another point</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><p>Distance in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_IsInFrustum_" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_IsInFrustum_VRageMath_BoundingBoxD_" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum(VRageMath.BoundingBoxD)">IsInFrustum(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks if specified bounding box is in actual bounding frustum
IMPORTANT: If you observe bad result of this test, check how you transform your bounding box.
Don't use BoundingBox.Transform. Instead transform box manualy and then create new box.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInFrustum(BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td><p>Bounding box to check</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>Whether specified bounding box is in actual bounding frustum</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_IsInFrustum_" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_IsInFrustum_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum(VRageMath.BoundingBoxD@)">IsInFrustum(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks if specified bounding box is in actual bounding frustum
IMPORTANT: If you observe bad result of this test, check how you transform your bounding box.
Don't use BoundingBox.Transform. Instead transform box manualy and then create new box.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInFrustum(ref BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td><p>Bounding box to check</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>Whether specified bounding box is in actual bounding frustum</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_IsInFrustum_" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_IsInFrustum_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyCamera.IsInFrustum(VRageMath.BoundingSphereD@)">IsInFrustum(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks if specified bounding sphere is in actual bounding frustum
IMPORTANT: If you observe bad result of this test, check how you transform your bounding sphere.
Don't use BoundingSphere.Transform. Instead transform sphere center manualy and then create new sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInFrustum(ref BoundingSphereD boundingSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">boundingSphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>Whether specified bounding box is in actual bounding frustum</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_WorldLineFromScreen_" data-uid="VRage.ModAPI.IMyCamera.WorldLineFromScreen*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_WorldLineFromScreen_VRageMath_Vector2_" data-uid="VRage.ModAPI.IMyCamera.WorldLineFromScreen(VRageMath.Vector2)">WorldLineFromScreen(Vector2)</h4>
  <div class="markdown level1 summary"><p>Gets normalized world space line from screen space coordinates.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">LineD WorldLineFromScreen(Vector2 screenCoords)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">screenCoords</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><p>Gets normalized world space line</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyCamera_WorldToScreen_" data-uid="VRage.ModAPI.IMyCamera.WorldToScreen*"></a>
  <h4 id="VRage_ModAPI_IMyCamera_WorldToScreen_VRageMath_Vector3D__" data-uid="VRage.ModAPI.IMyCamera.WorldToScreen(VRageMath.Vector3D@)">WorldToScreen(ref Vector3D)</h4>
  <div class="markdown level1 summary"><p>Gets screen coordinates of 3d world pos in 0 - 1 distance where 1.0 is screen width(for X) or height(for Y).
WARNING: Y is from bottom to top.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3D WorldToScreen(ref Vector3D worldPos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPos</span></td>
        <td><p>World position.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><p>Screen coordinate in 0-1 distance.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
