﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyIterableComplementSet&lt;T&gt;
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyIterableComplementSet&lt;T&gt;
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Library.Utils.MyIterableComplementSet`1">
  
  
  <h1 id="VRage_Library_Utils_MyIterableComplementSet_1" data-uid="VRage.Library.Utils.MyIterableComplementSet`1" class="text-break">Class MyIterableComplementSet&lt;T&gt;
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyIterableComplementSet&lt;T&gt;</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Library.Utils.html">VRage.Library.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.Library.dll</h6>
  <h5 id="VRage_Library_Utils_MyIterableComplementSet_1_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyIterableComplementSet&lt;T&gt; : Object, IEnumerable&lt;T&gt;, IEnumerable</code></pre>
  </div>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1__ctor_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.#ctor*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1__ctor" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.#ctor">MyIterableComplementSet()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyIterableComplementSet()</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Add_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Add*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Add__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Add(`0)">Add(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Add(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_AddToComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AddToComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_AddToComplement__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AddToComplement(`0)">AddToComplement(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddToComplement(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_AllToComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AllToComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_AllToComplement" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AllToComplement">AllToComplement()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AllToComplement()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_AllToSet_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AllToSet*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_AllToSet" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.AllToSet">AllToSet()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AllToSet()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Clear_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Clear*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Clear" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_ClearComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.ClearComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_ClearComplement" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.ClearComplement">ClearComplement()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ClearComplement()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_ClearSet_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.ClearSet*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_ClearSet" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.ClearSet">ClearSet()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ClearSet()</code></pre>
  </div>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Complement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Complement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Complement" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Complement">Complement()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IEnumerable&lt;T&gt; Complement()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;T&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Contains_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Contains*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Contains__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Contains(`0)">Contains(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_GetEnumerator_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.GetEnumerator*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_GetEnumerator" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.GetEnumerator">GetEnumerator()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IEnumerator&lt;T&gt; GetEnumerator()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerator</span>&lt;T&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_IsInComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.IsInComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_IsInComplement__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.IsInComplement(`0)">IsInComplement(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsInComplement(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_MoveToComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.MoveToComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_MoveToComplement__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.MoveToComplement(`0)">MoveToComplement(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void MoveToComplement(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_MoveToSet_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.MoveToSet*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_MoveToSet__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.MoveToSet(`0)">MoveToSet(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void MoveToSet(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Remove_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Remove*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Remove__0_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Remove(`0)">Remove(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Remove(T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSet_1_Set_" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Set*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSet_1_Set" data-uid="VRage.Library.Utils.MyIterableComplementSet`1.Set">Set()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IEnumerable&lt;T&gt; Set()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;T&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html#VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnComplement__1_VRage_Library_Utils_MyIterableComplementSet___0____0_">MyIterableComplementSetExtensions.AddOrEnsureOnComplement&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html#VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnSet__1_VRage_Library_Utils_MyIterableComplementSet___0____0_">MyIterableComplementSetExtensions.AddOrEnsureOnSet&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html#VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnComplementIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_">MyIterableComplementSetExtensions.EnsureOnComplementIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html#VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnSetIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_">MyIterableComplementSetExtensions.EnsureOnSetIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</a>
  </div>
  <div>
      <a class="xref" href="VRage.Library.Utils.MyIterableComplementSetExtensions.html#VRage_Library_Utils_MyIterableComplementSetExtensions_RemoveIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_">MyIterableComplementSetExtensions.RemoveIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
