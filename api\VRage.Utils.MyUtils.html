﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyUtils
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyUtils
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils.MyUtils">
  
  
  <h1 id="VRage_Utils_MyUtils" data-uid="VRage.Utils.MyUtils" class="text-break">Class MyUtils
  </h1>
  <div class="markdown level0 summary"><p>MyFileSystemUtils</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyUtils</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Utils.html">VRage.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Utils_MyUtils_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class MyUtils : Object</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Utils_MyUtils_C_CRLF" data-uid="VRage.Utils.MyUtils.C_CRLF">C_CRLF</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const string C_CRLF = &quot;\r\n&quot;</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Utils_MyUtils_DefaultNumberSuffix" data-uid="VRage.Utils.MyUtils.DefaultNumberSuffix">DefaultNumberSuffix</h4>
  <div class="markdown level1 summary"><p>Default number suffix, k = thousand, m = million, g/b = billion</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Tuple&lt;string, float&gt;[] DefaultNumberSuffix</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Tuple</span>&lt;<span class="xref">System.String</span>, <span class="xref">System.Single</span>&gt;[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Utils_MyUtils_EmptyStringBuilder" data-uid="VRage.Utils.MyUtils.EmptyStringBuilder">EmptyStringBuilder</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly StringBuilder EmptyStringBuilder</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Utils_MyUtils_ZeroMatrix" data-uid="VRage.Utils.MyUtils.ZeroMatrix">ZeroMatrix</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Matrix ZeroMatrix</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Utils_MyUtils_MainThread_" data-uid="VRage.Utils.MyUtils.MainThread*"></a>
  <h4 id="VRage_Utils_MyUtils_MainThread" data-uid="VRage.Utils.MyUtils.MainThread">MainThread</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Thread MainThread { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Threading.Thread</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Utils_MyUtils_AlignCoord_" data-uid="VRage.Utils.MyUtils.AlignCoord*"></a>
  <h4 id="VRage_Utils_MyUtils_AlignCoord_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.AlignCoord(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">AlignCoord(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 AlignCoord(Vector2 coordScreen, Vector2 size, MyGuiDrawAlignEnum drawAlignEnum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">coordScreen</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlignEnum</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AlignIntToRight_" data-uid="VRage.Utils.MyUtils.AlignIntToRight*"></a>
  <h4 id="VRage_Utils_MyUtils_AlignIntToRight_System_Int32_System_Int32_System_Char_" data-uid="VRage.Utils.MyUtils.AlignIntToRight(System.Int32,System.Int32,System.Char)">AlignIntToRight(Int32, Int32, Char)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string AlignIntToRight(int value, int charsCount, char ch)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">charsCount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Char</span></td>
        <td><span class="parametername">ch</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_System_Double_" data-uid="VRage.Utils.MyUtils.AssertIsValid(System.Double)">AssertIsValid(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(double f)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">f</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_System_Nullable_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.AssertIsValid(System.Nullable{VRageMath.Vector3})">AssertIsValid(Nullable&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Nullable&lt;Vector3&gt; vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_System_Single_" data-uid="VRage.Utils.MyUtils.AssertIsValid(System.Single)">AssertIsValid(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(float f)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">f</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_Matrix_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.Matrix)">AssertIsValid(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_MatrixD_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.MatrixD)">AssertIsValid(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_Quaternion_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.Quaternion)">AssertIsValid(Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Quaternion q)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">q</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_Vector2_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.Vector2)">AssertIsValid(Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Vector2 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.Vector3)">AssertIsValid(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValid_" data-uid="VRage.Utils.MyUtils.AssertIsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValid_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.AssertIsValid(VRageMath.Vector3D)">AssertIsValid(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValid(Vector3D vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertIsValidOrZero_" data-uid="VRage.Utils.MyUtils.AssertIsValidOrZero*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertIsValidOrZero_VRageMath_Matrix_" data-uid="VRage.Utils.MyUtils.AssertIsValidOrZero(VRageMath.Matrix)">AssertIsValidOrZero(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertIsValidOrZero(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertLengthValid_" data-uid="VRage.Utils.MyUtils.AssertLengthValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertLengthValid_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.AssertLengthValid(VRageMath.Vector3@)">AssertLengthValid(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertLengthValid(ref Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_AssertLengthValid_" data-uid="VRage.Utils.MyUtils.AssertLengthValid*"></a>
  <h4 id="VRage_Utils_MyUtils_AssertLengthValid_VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.AssertLengthValid(VRageMath.Vector3D@)">AssertLengthValid(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertLengthValid(ref Vector3D vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_CheckFloatValues_" data-uid="VRage.Utils.MyUtils.CheckFloatValues*"></a>
  <h4 id="VRage_Utils_MyUtils_CheckFloatValues_System_Object_System_String_System_Nullable_System_Double___System_Nullable_System_Double___" data-uid="VRage.Utils.MyUtils.CheckFloatValues(System.Object,System.String,System.Nullable{System.Double}@,System.Nullable{System.Double}@)">CheckFloatValues(Object, String, ref Nullable&lt;Double&gt;, ref Nullable&lt;Double&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CheckFloatValues(object graph, string name, ref Nullable&lt;double&gt; min, ref Nullable&lt;double&gt; max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">graph</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_CheckMainThread_" data-uid="VRage.Utils.MyUtils.CheckMainThread*"></a>
  <h4 id="VRage_Utils_MyUtils_CheckMainThread_System_String_" data-uid="VRage.Utils.MyUtils.CheckMainThread(System.String)">CheckMainThread(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CheckMainThread(string message = &quot;&quot;)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">message</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_DeserializeValue_" data-uid="VRage.Utils.MyUtils.DeserializeValue*"></a>
  <h4 id="VRage_Utils_MyUtils_DeserializeValue_System_Xml_XmlReader_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.DeserializeValue(System.Xml.XmlReader,VRageMath.Vector3@)">DeserializeValue(XmlReader, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DeserializeValue(XmlReader reader, out Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Xml.XmlReader</span></td>
        <td><span class="parametername">reader</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_DeserializeValue_" data-uid="VRage.Utils.MyUtils.DeserializeValue*"></a>
  <h4 id="VRage_Utils_MyUtils_DeserializeValue_System_Xml_XmlReader_VRageMath_Vector4__" data-uid="VRage.Utils.MyUtils.DeserializeValue(System.Xml.XmlReader,VRageMath.Vector4@)">DeserializeValue(XmlReader, out Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DeserializeValue(XmlReader reader, out Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Xml.XmlReader</span></td>
        <td><span class="parametername">reader</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_FormatByteSizePrefix_" data-uid="VRage.Utils.MyUtils.FormatByteSizePrefix*"></a>
  <h4 id="VRage_Utils_MyUtils_FormatByteSizePrefix_System_Double__" data-uid="VRage.Utils.MyUtils.FormatByteSizePrefix(System.Double@)">FormatByteSizePrefix(ref Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string FormatByteSizePrefix(ref double byteSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">byteSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GenerateBoxColors_" data-uid="VRage.Utils.MyUtils.GenerateBoxColors*"></a>
  <h4 id="VRage_Utils_MyUtils_GenerateBoxColors" data-uid="VRage.Utils.MyUtils.GenerateBoxColors">GenerateBoxColors()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color[] GenerateBoxColors()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GenerateQuad_" data-uid="VRage.Utils.MyUtils.GenerateQuad*"></a>
  <h4 id="VRage_Utils_MyUtils_GenerateQuad_VRageMath_MyQuadD__VRageMath_Vector3D__System_Single_System_Single_VRageMath_MatrixD__" data-uid="VRage.Utils.MyUtils.GenerateQuad(VRageMath.MyQuadD@,VRageMath.Vector3D@,System.Single,System.Single,VRageMath.MatrixD@)">GenerateQuad(out MyQuadD, ref Vector3D, Single, Single, ref MatrixD)</h4>
  <div class="markdown level1 summary"><p>Generate oriented quad by matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GenerateQuad(out MyQuadD quad, ref Vector3D position, float width, float height, ref MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetAngleBetweenVectors_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectors*"></a>
  <h4 id="VRage_Utils_MyUtils_GetAngleBetweenVectors_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectors(VRageMath.Vector3,VRageMath.Vector3)">GetAngleBetweenVectors(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculating the Angle between two Vectors (return in radians).</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetAngleBetweenVectors(Vector3 vectorA, Vector3 vectorB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vectorA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vectorB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetAngleBetweenVectorsAndNormalise_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectorsAndNormalise*"></a>
  <h4 id="VRage_Utils_MyUtils_GetAngleBetweenVectorsAndNormalise_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectorsAndNormalise(VRageMath.Vector3,VRageMath.Vector3)">GetAngleBetweenVectorsAndNormalise(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetAngleBetweenVectorsAndNormalise(Vector3 vectorA, Vector3 vectorB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vectorA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vectorB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetAngleBetweenVectorsForSphereCollision_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectorsForSphereCollision*"></a>
  <h4 id="VRage_Utils_MyUtils_GetAngleBetweenVectorsForSphereCollision_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.GetAngleBetweenVectorsForSphereCollision(VRageMath.Vector3,VRageMath.Vector3)">GetAngleBetweenVectorsForSphereCollision(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetAngleBetweenVectorsForSphereCollision(Vector3 vector1, Vector3 vector2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBillboardQuadAdvancedRotated_" data-uid="VRage.Utils.MyUtils.GetBillboardQuadAdvancedRotated*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBillboardQuadAdvancedRotated_VRageMath_MyQuadD__VRageMath_Vector3D_System_Single_System_Single_System_Single_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.GetBillboardQuadAdvancedRotated(VRageMath.MyQuadD@,VRageMath.Vector3D,System.Single,System.Single,System.Single,VRageMath.Vector3D)">GetBillboardQuadAdvancedRotated(out MyQuadD, Vector3D, Single, Single, Single, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetBillboardQuadAdvancedRotated(out MyQuadD quad, Vector3D position, float radiusX, float radiusY, float angle, Vector3D cameraPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radiusX</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radiusY</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBillboardQuadAdvancedRotated_" data-uid="VRage.Utils.MyUtils.GetBillboardQuadAdvancedRotated*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBillboardQuadAdvancedRotated_VRageMath_MyQuadD__VRageMath_Vector3D_System_Single_System_Single_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.GetBillboardQuadAdvancedRotated(VRageMath.MyQuadD@,VRageMath.Vector3D,System.Single,System.Single,VRageMath.Vector3D)">GetBillboardQuadAdvancedRotated(out MyQuadD, Vector3D, Single, Single, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetBillboardQuadAdvancedRotated(out MyQuadD quad, Vector3D position, float radius, float angle, Vector3D cameraPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBillboardQuadOriented_" data-uid="VRage.Utils.MyUtils.GetBillboardQuadOriented*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBillboardQuadOriented_VRageMath_MyQuadD__VRageMath_Vector3D__System_Single_System_Single_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.GetBillboardQuadOriented(VRageMath.MyQuadD@,VRageMath.Vector3D@,System.Single,System.Single,VRageMath.Vector3@,VRageMath.Vector3@)">GetBillboardQuadOriented(out MyQuadD, ref Vector3D, Single, Single, ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>This billboard isn't facing the camera. It's always oriented in specified direction. May be used as thrusts, or inner light of reflector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetBillboardQuadOriented(out MyQuadD quad, ref Vector3D position, float width, float height, ref Vector3 leftVector, ref Vector3 upVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBoolFromString_" data-uid="VRage.Utils.MyUtils.GetBoolFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBoolFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetBoolFromString(System.String)">GetBoolFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;bool&gt; GetBoolFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Boolean</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBoolFromString_" data-uid="VRage.Utils.MyUtils.GetBoolFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBoolFromString_System_String_System_Boolean_" data-uid="VRage.Utils.MyUtils.GetBoolFromString(System.String,System.Boolean)">GetBoolFromString(String, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetBoolFromString(string s, bool defaultValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetBoundingSphereFromBoundingBox_" data-uid="VRage.Utils.MyUtils.GetBoundingSphereFromBoundingBox*"></a>
  <h4 id="VRage_Utils_MyUtils_GetBoundingSphereFromBoundingBox_VRageMath_BoundingBoxD__" data-uid="VRage.Utils.MyUtils.GetBoundingSphereFromBoundingBox(VRageMath.BoundingBoxD@)">GetBoundingSphereFromBoundingBox(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD GetBoundingSphereFromBoundingBox(ref BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetByteFromString_" data-uid="VRage.Utils.MyUtils.GetByteFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetByteFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetByteFromString(System.String)">GetByteFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;byte&gt; GetByteFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Byte</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCartesianCoordinatesFromSpherical_" data-uid="VRage.Utils.MyUtils.GetCartesianCoordinatesFromSpherical*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCartesianCoordinatesFromSpherical_System_Single_System_Single_System_Single_" data-uid="VRage.Utils.MyUtils.GetCartesianCoordinatesFromSpherical(System.Single,System.Single,System.Single)">GetCartesianCoordinatesFromSpherical(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetCartesianCoordinatesFromSpherical(float angleHorizontal, float angleVertical, float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angleHorizontal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angleVertical</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetClampInt_" data-uid="VRage.Utils.MyUtils.GetClampInt*"></a>
  <h4 id="VRage_Utils_MyUtils_GetClampInt_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Utils.MyUtils.GetClampInt(System.Int32,System.Int32,System.Int32)">GetClampInt(Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetClampInt(int value, int min, int max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetClosestPointOnLine_" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine*"></a>
  <h4 id="VRage_Utils_MyUtils_GetClosestPointOnLine_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">GetClosestPointOnLine(ref Vector3, ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetClosestPointOnLine(ref Vector3 linePointA, ref Vector3 linePointB, ref Vector3 point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">linePointA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">linePointB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetClosestPointOnLine_" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine*"></a>
  <h4 id="VRage_Utils_MyUtils_GetClosestPointOnLine_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Single__" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Single@)">GetClosestPointOnLine(ref Vector3, ref Vector3, ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetClosestPointOnLine(ref Vector3 linePointA, ref Vector3 linePointB, ref Vector3 point, out float dist)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">linePointA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">linePointB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">dist</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetClosestPointOnLine_" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine*"></a>
  <h4 id="VRage_Utils_MyUtils_GetClosestPointOnLine_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@)">GetClosestPointOnLine(ref Vector3D, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetClosestPointOnLine(ref Vector3D linePointA, ref Vector3D linePointB, ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetClosestPointOnLine_" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine*"></a>
  <h4 id="VRage_Utils_MyUtils_GetClosestPointOnLine_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__System_Double__" data-uid="VRage.Utils.MyUtils.GetClosestPointOnLine(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@,System.Double@)">GetClosestPointOnLine(ref Vector3D, ref Vector3D, ref Vector3D, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetClosestPointOnLine(ref Vector3D linePointA, ref Vector3D linePointB, ref Vector3D point, out double dist)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">dist</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordAligned_" data-uid="VRage.Utils.MyUtils.GetCoordAligned*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordAligned_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordAligned(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordAligned(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Aligns rectangle, works in screen/texture/pixel coordinates, not normalized coordinates.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordAligned(Vector2 coordScreen, Vector2 size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">coordScreen</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><p>Pixel coordinates for texture.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordAlignedFromCenter_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromCenter*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordAlignedFromCenter_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromCenter(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordAlignedFromCenter(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Modifies input coordinate (in center) using alignment and
size of the rectangle. Result is at position inside rectangle
specified by alignment.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordAlignedFromCenter(Vector2 coordCenter, Vector2 size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">coordCenter</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordAlignedFromRectangle_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromRectangle*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordAlignedFromRectangle_VRageMath_RectangleF__VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromRectangle(VRageMath.RectangleF@,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordAlignedFromRectangle(ref RectangleF, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Returns coordinate within given rectangle specified by draw align. Rectangle position should be
upper left corner. Conversion assumes that Y coordinates increase downwards.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordAlignedFromRectangle(ref RectangleF rect, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RectangleF.html">RectangleF</a></td>
        <td><span class="parametername">rect</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordAlignedFromTopLeft_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromTopLeft*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordAlignedFromTopLeft_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordAlignedFromTopLeft(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordAlignedFromTopLeft(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordAlignedFromTopLeft(Vector2 topLeft, Vector2 size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">topLeft</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordCenterFromAligned_" data-uid="VRage.Utils.MyUtils.GetCoordCenterFromAligned*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordCenterFromAligned_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordCenterFromAligned(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordCenterFromAligned(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Reverses effect of alignment to compute center coordinate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordCenterFromAligned(Vector2 alignedCoord, Vector2 size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">alignedCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordTopLeftFromAligned_" data-uid="VRage.Utils.MyUtils.GetCoordTopLeftFromAligned*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordTopLeftFromAligned_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordTopLeftFromAligned(VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordTopLeftFromAligned(Vector2, Vector2, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Reverses effect of alignment to compute top-left corner coordinate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 GetCoordTopLeftFromAligned(Vector2 alignedCoord, Vector2 size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">alignedCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetCoordTopLeftFromAligned_" data-uid="VRage.Utils.MyUtils.GetCoordTopLeftFromAligned*"></a>
  <h4 id="VRage_Utils_MyUtils_GetCoordTopLeftFromAligned_VRageMath_Vector2I_VRageMath_Vector2I_VRage_Utils_MyGuiDrawAlignEnum_" data-uid="VRage.Utils.MyUtils.GetCoordTopLeftFromAligned(VRageMath.Vector2I,VRageMath.Vector2I,VRage.Utils.MyGuiDrawAlignEnum)">GetCoordTopLeftFromAligned(Vector2I, Vector2I, MyGuiDrawAlignEnum)</h4>
  <div class="markdown level1 summary"><p>Reverses effect of alignment to compute top-left corner coordinate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2I GetCoordTopLeftFromAligned(Vector2I alignedCoord, Vector2I size, MyGuiDrawAlignEnum drawAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">alignedCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></td>
        <td><span class="parametername">drawAlign</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetEdgeSphereCollision_" data-uid="VRage.Utils.MyUtils.GetEdgeSphereCollision*"></a>
  <h4 id="VRage_Utils_MyUtils_GetEdgeSphereCollision_VRageMath_Vector3__System_Single_VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetEdgeSphereCollision(VRageMath.Vector3@,System.Single,VRage.MyTriangle_Vertices@)">GetEdgeSphereCollision(ref Vector3, Single, ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"><p>Returns intersection point between sphere and its edges. But only if there is intersection between sphere and one of the edges.
If sphere intersects somewhere inside the triangle, this method will not detect it.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;Vector3&gt; GetEdgeSphereCollision(ref Vector3 sphereCenter, float sphereRadius, ref MyTriangle_Vertices triangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">sphereCenter</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">sphereRadius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">triangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetFixedInvalidFileNameChars_" data-uid="VRage.Utils.MyUtils.GetFixedInvalidFileNameChars*"></a>
  <h4 id="VRage_Utils_MyUtils_GetFixedInvalidFileNameChars" data-uid="VRage.Utils.MyUtils.GetFixedInvalidFileNameChars">GetFixedInvalidFileNameChars()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static char[] GetFixedInvalidFileNameChars()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Char</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetFixedInvalidPathChars_" data-uid="VRage.Utils.MyUtils.GetFixedInvalidPathChars*"></a>
  <h4 id="VRage_Utils_MyUtils_GetFixedInvalidPathChars" data-uid="VRage.Utils.MyUtils.GetFixedInvalidPathChars">GetFixedInvalidPathChars()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static char[] GetFixedInvalidPathChars()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Char</span>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetFloatFromString_" data-uid="VRage.Utils.MyUtils.GetFloatFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetFloatFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetFloatFromString(System.String)">GetFloatFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;float&gt; GetFloatFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetFloatFromString_" data-uid="VRage.Utils.MyUtils.GetFloatFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetFloatFromString_System_String_System_Single_" data-uid="VRage.Utils.MyUtils.GetFloatFromString(System.String,System.Single)">GetFloatFromString(String, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetFloatFromString(string s, float defaultValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetHash_" data-uid="VRage.Utils.MyUtils.GetHash*"></a>
  <h4 id="VRage_Utils_MyUtils_GetHash_System_Double_System_Int32_" data-uid="VRage.Utils.MyUtils.GetHash(System.Double,System.Int32)">GetHash(Double, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetHash(double d, int hash = -2128831035)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">d</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">hash</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetHash_" data-uid="VRage.Utils.MyUtils.GetHash*"></a>
  <h4 id="VRage_Utils_MyUtils_GetHash_System_String_System_Int32_" data-uid="VRage.Utils.MyUtils.GetHash(System.String,System.Int32)">GetHash(String, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetHash(string str, int hash = -2128831035)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">str</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">hash</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetHash_" data-uid="VRage.Utils.MyUtils.GetHash*"></a>
  <h4 id="VRage_Utils_MyUtils_GetHash_System_String_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Utils.MyUtils.GetHash(System.String,System.Int32,System.Int32,System.Int32)">GetHash(String, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetHash(string str, int start, int length, int hash = -2128831035)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">str</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">hash</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetHashUpperCase_" data-uid="VRage.Utils.MyUtils.GetHashUpperCase*"></a>
  <h4 id="VRage_Utils_MyUtils_GetHashUpperCase_System_String_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Utils.MyUtils.GetHashUpperCase(System.String,System.Int32,System.Int32,System.Int32)">GetHashUpperCase(String, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetHashUpperCase(string str, int start, int length, int hash = -2128831035)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">str</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">hash</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetInsidePolygonForSphereCollision_" data-uid="VRage.Utils.MyUtils.GetInsidePolygonForSphereCollision*"></a>
  <h4 id="VRage_Utils_MyUtils_GetInsidePolygonForSphereCollision_VRageMath_Vector3__VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetInsidePolygonForSphereCollision(VRageMath.Vector3@,VRage.MyTriangle_Vertices@)">GetInsidePolygonForSphereCollision(ref Vector3, ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"><p>Return true if point is inside the triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetInsidePolygonForSphereCollision(ref Vector3 point, ref MyTriangle_Vertices triangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">triangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetInsidePolygonForSphereCollision_" data-uid="VRage.Utils.MyUtils.GetInsidePolygonForSphereCollision*"></a>
  <h4 id="VRage_Utils_MyUtils_GetInsidePolygonForSphereCollision_VRageMath_Vector3D__VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetInsidePolygonForSphereCollision(VRageMath.Vector3D@,VRage.MyTriangle_Vertices@)">GetInsidePolygonForSphereCollision(ref Vector3D, ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"><p>Return true if point is inside the triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetInsidePolygonForSphereCollision(ref Vector3D point, ref MyTriangle_Vertices triangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">triangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetInt32FromString_" data-uid="VRage.Utils.MyUtils.GetInt32FromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetInt32FromString_System_String_" data-uid="VRage.Utils.MyUtils.GetInt32FromString(System.String)">GetInt32FromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;int&gt; GetInt32FromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int32</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetIntFromString_" data-uid="VRage.Utils.MyUtils.GetIntFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetIntFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetIntFromString(System.String)">GetIntFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;int&gt; GetIntFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int32</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetIntFromString_" data-uid="VRage.Utils.MyUtils.GetIntFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetIntFromString_System_String_System_Int32_" data-uid="VRage.Utils.MyUtils.GetIntFromString(System.String,System.Int32)">GetIntFromString(String, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetIntFromString(string s, int defaultValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetLargestDistanceToSphere_" data-uid="VRage.Utils.MyUtils.GetLargestDistanceToSphere*"></a>
  <h4 id="VRage_Utils_MyUtils_GetLargestDistanceToSphere_VRageMath_Vector3D__VRageMath_BoundingSphereD__" data-uid="VRage.Utils.MyUtils.GetLargestDistanceToSphere(VRageMath.Vector3D@,VRageMath.BoundingSphereD@)">GetLargestDistanceToSphere(ref Vector3D, ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Distance between &quot;from&quot; and opposite side of the &quot;sphere&quot;. Always positive.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetLargestDistanceToSphere(ref Vector3D from, ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">from</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetLineBoundingBoxIntersection_" data-uid="VRage.Utils.MyUtils.GetLineBoundingBoxIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_GetLineBoundingBoxIntersection_VRageMath_Line__VRageMath_BoundingBox__" data-uid="VRage.Utils.MyUtils.GetLineBoundingBoxIntersection(VRageMath.Line@,VRageMath.BoundingBox@)">GetLineBoundingBoxIntersection(ref Line, ref BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Calculates intersection between line and bounding box and if found, distance is returned. Otherwise null is returned.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;float&gt; GetLineBoundingBoxIntersection(ref Line line, ref BoundingBox boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetLineTriangleIntersection_" data-uid="VRage.Utils.MyUtils.GetLineTriangleIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_GetLineTriangleIntersection_VRageMath_Line__VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetLineTriangleIntersection(VRageMath.Line@,VRage.MyTriangle_Vertices@)">GetLineTriangleIntersection(ref Line, ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"><p>Checks whether a ray intersects a triangleVertexes. This uses the algorithm
developed by Tomas Moller and Ben Trumbore, which was published in the
Journal of Graphics Tools, pitch 2, &quot;Fast, Minimum Storage Ray-Triangle
Intersection&quot;.</p>
<p>This method is implemented using the pass-by-reference versions of the
XNA math functions. Using these overloads is generally not recommended,
because they make the code less readable than the normal pass-by-value
versions. This method can be called very frequently in a tight inner loop,
however, so in this particular case the performance benefits from passing
everything by reference outweigh the loss of readability.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;float&gt; GetLineTriangleIntersection(ref Line line, ref MyTriangle_Vertices triangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">triangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetMaxValueFromEnum_" data-uid="VRage.Utils.MyUtils.GetMaxValueFromEnum*"></a>
  <h4 id="VRage_Utils_MyUtils_GetMaxValueFromEnum__1" data-uid="VRage.Utils.MyUtils.GetMaxValueFromEnum``1">GetMaxValueFromEnum&lt;T&gt;()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetMaxValueFromEnum&lt;T&gt;()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetNormalVectorFromTriangle_" data-uid="VRage.Utils.MyUtils.GetNormalVectorFromTriangle*"></a>
  <h4 id="VRage_Utils_MyUtils_GetNormalVectorFromTriangle_VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetNormalVectorFromTriangle(VRage.MyTriangle_Vertices@)">GetNormalVectorFromTriangle(ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetNormalVectorFromTriangle(ref MyTriangle_Vertices inputTriangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">inputTriangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetOpenBoundaries_" data-uid="VRage.Utils.MyUtils.GetOpenBoundaries*"></a>
  <h4 id="VRage_Utils_MyUtils_GetOpenBoundaries_VRageMath_Vector3___System_Int32___System_Collections_Generic_List_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.GetOpenBoundaries(VRageMath.Vector3[],System.Int32[],System.Collections.Generic.List{VRageMath.Vector3})">GetOpenBoundaries(Vector3[], Int32[], List&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetOpenBoundaries(Vector3[] vertices, int[] indices, List&lt;Vector3&gt; openBoundaries)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">vertices</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td><span class="parametername">indices</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">openBoundaries</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetPointLineDistance_" data-uid="VRage.Utils.MyUtils.GetPointLineDistance*"></a>
  <h4 id="VRage_Utils_MyUtils_GetPointLineDistance_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.GetPointLineDistance(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@)">GetPointLineDistance(ref Vector3D, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetPointLineDistance(ref Vector3D linePointA, ref Vector3D linePointB, ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">linePointB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetPolyLineQuad_" data-uid="VRage.Utils.MyUtils.GetPolyLineQuad*"></a>
  <h4 id="VRage_Utils_MyUtils_GetPolyLineQuad_VRageMath_MyQuadD__VRage_Utils_MyPolyLineD__VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.GetPolyLineQuad(VRageMath.MyQuadD@,VRage.Utils.MyPolyLineD@,VRageMath.Vector3D)">GetPolyLineQuad(out MyQuadD, ref MyPolyLineD, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetPolyLineQuad(out MyQuadD retQuad, ref MyPolyLineD polyLine, Vector3D cameraPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">retQuad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyPolyLineD.html">MyPolyLineD</a></td>
        <td><span class="parametername">polyLine</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomBorderPosition_" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomBorderPosition_VRageMath_BoundingBox__" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition(VRageMath.BoundingBox@)">GetRandomBorderPosition(ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomBorderPosition(ref BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomBorderPosition_" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomBorderPosition_VRageMath_BoundingBoxD__" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition(VRageMath.BoundingBoxD@)">GetRandomBorderPosition(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomBorderPosition(ref BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomBorderPosition_" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomBorderPosition_VRageMath_BoundingSphere__" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition(VRageMath.BoundingSphere@)">GetRandomBorderPosition(ref BoundingSphere)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomBorderPosition(ref BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomBorderPosition_" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomBorderPosition_VRageMath_BoundingSphereD__" data-uid="VRage.Utils.MyUtils.GetRandomBorderPosition(VRageMath.BoundingSphereD@)">GetRandomBorderPosition(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomBorderPosition(ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomDiscPosition_" data-uid="VRage.Utils.MyUtils.GetRandomDiscPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomDiscPosition_VRageMath_Vector3D__System_Double_System_Double_VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.GetRandomDiscPosition(VRageMath.Vector3D@,System.Double,System.Double,VRageMath.Vector3D@,VRageMath.Vector3D@)">GetRandomDiscPosition(ref Vector3D, Double, Double, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomDiscPosition(ref Vector3D center, double minRadius, double maxRadius, ref Vector3D tangent, ref Vector3D bitangent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minRadius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">maxRadius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">tangent</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">bitangent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomDiscPosition_" data-uid="VRage.Utils.MyUtils.GetRandomDiscPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomDiscPosition_VRageMath_Vector3D__System_Double_VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.GetRandomDiscPosition(VRageMath.Vector3D@,System.Double,VRageMath.Vector3D@,VRageMath.Vector3D@)">GetRandomDiscPosition(ref Vector3D, Double, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomDiscPosition(ref Vector3D center, double radius, ref Vector3D tangent, ref Vector3D bitangent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">tangent</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">bitangent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomDouble_" data-uid="VRage.Utils.MyUtils.GetRandomDouble*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomDouble_System_Double_System_Double_" data-uid="VRage.Utils.MyUtils.GetRandomDouble(System.Double,System.Double)">GetRandomDouble(Double, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetRandomDouble(double minValue, double maxValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minValue</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">maxValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomFloat_" data-uid="VRage.Utils.MyUtils.GetRandomFloat*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomFloat" data-uid="VRage.Utils.MyUtils.GetRandomFloat">GetRandomFloat()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetRandomFloat()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomFloat_" data-uid="VRage.Utils.MyUtils.GetRandomFloat*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomFloat_System_Single_System_Single_" data-uid="VRage.Utils.MyUtils.GetRandomFloat(System.Single,System.Single)">GetRandomFloat(Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetRandomFloat(float minValue, float maxValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">minValue</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">maxValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomInt_" data-uid="VRage.Utils.MyUtils.GetRandomInt*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomInt_System_Int32_" data-uid="VRage.Utils.MyUtils.GetRandomInt(System.Int32)">GetRandomInt(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetRandomInt(int maxValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">maxValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomInt_" data-uid="VRage.Utils.MyUtils.GetRandomInt*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomInt_System_Int32_System_Int32_" data-uid="VRage.Utils.MyUtils.GetRandomInt(System.Int32,System.Int32)">GetRandomInt(Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Return random int in range of minValue to maxValue, the range of return values includes minValue but not maxValue</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetRandomInt(int minValue, int maxValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">minValue</span></td>
        <td><p>min</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">maxValue</span></td>
        <td><p>max</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomItem_" data-uid="VRage.Utils.MyUtils.GetRandomItem*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomItem__1___0___" data-uid="VRage.Utils.MyUtils.GetRandomItem``1(``0[])">GetRandomItem&lt;T&gt;(T[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T GetRandomItem&lt;T&gt;(this T[] list)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>T[]</td>
        <td><span class="parametername">list</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomItemFromList_" data-uid="VRage.Utils.MyUtils.GetRandomItemFromList*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomItemFromList__1_System_Collections_Generic_List___0__" data-uid="VRage.Utils.MyUtils.GetRandomItemFromList``1(System.Collections.Generic.List{``0})">GetRandomItemFromList&lt;T&gt;(List&lt;T&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T GetRandomItemFromList&lt;T&gt;(this List&lt;T&gt; list)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">list</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomLong_" data-uid="VRage.Utils.MyUtils.GetRandomLong*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomLong" data-uid="VRage.Utils.MyUtils.GetRandomLong">GetRandomLong()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static long GetRandomLong()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomPerpendicularVector_" data-uid="VRage.Utils.MyUtils.GetRandomPerpendicularVector*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomPerpendicularVector_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.GetRandomPerpendicularVector(VRageMath.Vector3@)">GetRandomPerpendicularVector(in Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomPerpendicularVector(in Vector3 axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomPerpendicularVector_" data-uid="VRage.Utils.MyUtils.GetRandomPerpendicularVector*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomPerpendicularVector_VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.GetRandomPerpendicularVector(VRageMath.Vector3D@)">GetRandomPerpendicularVector(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomPerpendicularVector(ref Vector3D axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomPosition_" data-uid="VRage.Utils.MyUtils.GetRandomPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomPosition_VRageMath_BoundingBox__" data-uid="VRage.Utils.MyUtils.GetRandomPosition(VRageMath.BoundingBox@)">GetRandomPosition(ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomPosition(ref BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomPosition_" data-uid="VRage.Utils.MyUtils.GetRandomPosition*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomPosition_VRageMath_BoundingBoxD__" data-uid="VRage.Utils.MyUtils.GetRandomPosition(VRageMath.BoundingBoxD@)">GetRandomPosition(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomPosition(ref BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomRadian_" data-uid="VRage.Utils.MyUtils.GetRandomRadian*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomRadian" data-uid="VRage.Utils.MyUtils.GetRandomRadian">GetRandomRadian()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetRandomRadian()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomSign_" data-uid="VRage.Utils.MyUtils.GetRandomSign*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomSign" data-uid="VRage.Utils.MyUtils.GetRandomSign">GetRandomSign()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetRandomSign()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomTimeSpan_" data-uid="VRage.Utils.MyUtils.GetRandomTimeSpan*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomTimeSpan_System_TimeSpan_System_TimeSpan_" data-uid="VRage.Utils.MyUtils.GetRandomTimeSpan(System.TimeSpan,System.TimeSpan)">GetRandomTimeSpan(TimeSpan, TimeSpan)</h4>
  <div class="markdown level1 summary"><p>Returns a random TimeSpan between begin (inclusive) and end (exclusive).</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static TimeSpan GetRandomTimeSpan(TimeSpan begin, TimeSpan end)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.TimeSpan</span></td>
        <td><span class="parametername">begin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.TimeSpan</span></td>
        <td><span class="parametername">end</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.TimeSpan</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3_" data-uid="VRage.Utils.MyUtils.GetRandomVector3*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3" data-uid="VRage.Utils.MyUtils.GetRandomVector3">GetRandomVector3()</h4>
  <div class="markdown level1 summary"><p>Returns a uniformly-distributed random vector from inside of a box (-1,-1,-1), (1, 1, 1)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomVector3()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3CircleNormalized_" data-uid="VRage.Utils.MyUtils.GetRandomVector3CircleNormalized*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3CircleNormalized" data-uid="VRage.Utils.MyUtils.GetRandomVector3CircleNormalized">GetRandomVector3CircleNormalized()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomVector3CircleNormalized()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3D_" data-uid="VRage.Utils.MyUtils.GetRandomVector3D*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3D" data-uid="VRage.Utils.MyUtils.GetRandomVector3D">GetRandomVector3D()</h4>
  <div class="markdown level1 summary"><p>Returns a uniformly-distributed random vector from inside of a box (-1,-1,-1), (1, 1, 1)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetRandomVector3D()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3HemisphereNormalized_" data-uid="VRage.Utils.MyUtils.GetRandomVector3HemisphereNormalized*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3HemisphereNormalized_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.GetRandomVector3HemisphereNormalized(VRageMath.Vector3)">GetRandomVector3HemisphereNormalized(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomVector3HemisphereNormalized(Vector3 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3MaxAngle_" data-uid="VRage.Utils.MyUtils.GetRandomVector3MaxAngle*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3MaxAngle_System_Single_" data-uid="VRage.Utils.MyUtils.GetRandomVector3MaxAngle(System.Single)">GetRandomVector3MaxAngle(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomVector3MaxAngle(float maxAngle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">maxAngle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetRandomVector3Normalized_" data-uid="VRage.Utils.MyUtils.GetRandomVector3Normalized*"></a>
  <h4 id="VRage_Utils_MyUtils_GetRandomVector3Normalized" data-uid="VRage.Utils.MyUtils.GetRandomVector3Normalized">GetRandomVector3Normalized()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetRandomVector3Normalized()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetSmallestDistanceToSphere_" data-uid="VRage.Utils.MyUtils.GetSmallestDistanceToSphere*"></a>
  <h4 id="VRage_Utils_MyUtils_GetSmallestDistanceToSphere_VRageMath_Vector3D__VRageMath_BoundingSphereD__" data-uid="VRage.Utils.MyUtils.GetSmallestDistanceToSphere(VRageMath.Vector3D@,VRageMath.BoundingSphereD@)">GetSmallestDistanceToSphere(ref Vector3D, ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Calculates distance from point 'from' to boundary of 'sphere'. If point is inside the sphere, distance will be negative.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetSmallestDistanceToSphere(ref Vector3D from, ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">from</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetSmallestDistanceToSphereAlwaysPositive_" data-uid="VRage.Utils.MyUtils.GetSmallestDistanceToSphereAlwaysPositive*"></a>
  <h4 id="VRage_Utils_MyUtils_GetSmallestDistanceToSphereAlwaysPositive_VRageMath_Vector3D__VRageMath_BoundingSphereD__" data-uid="VRage.Utils.MyUtils.GetSmallestDistanceToSphereAlwaysPositive(VRageMath.Vector3D@,VRageMath.BoundingSphereD@)">GetSmallestDistanceToSphereAlwaysPositive(ref Vector3D, ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double GetSmallestDistanceToSphereAlwaysPositive(ref Vector3D from, ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">from</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetSpherePlaneIntersection_" data-uid="VRage.Utils.MyUtils.GetSpherePlaneIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_GetSpherePlaneIntersection_VRageMath_BoundingSphere__VRageMath_Plane__System_Single__" data-uid="VRage.Utils.MyUtils.GetSpherePlaneIntersection(VRageMath.BoundingSphere@,VRageMath.Plane@,System.Single@)">GetSpherePlaneIntersection(ref BoundingSphere, ref Plane, out Single)</h4>
  <div class="markdown level1 summary"><p>This tells if a sphere is BEHIND, in FRONT, or INTERSECTS a plane, also it's distance</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MySpherePlaneIntersectionEnum GetSpherePlaneIntersection(ref BoundingSphere sphere, ref Plane plane, out float distanceFromPlaneToSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">distanceFromPlaneToSphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MySpherePlaneIntersectionEnum.html">MySpherePlaneIntersectionEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetSpherePlaneIntersection_" data-uid="VRage.Utils.MyUtils.GetSpherePlaneIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_GetSpherePlaneIntersection_VRageMath_BoundingSphereD__VRageMath_PlaneD__System_Double__" data-uid="VRage.Utils.MyUtils.GetSpherePlaneIntersection(VRageMath.BoundingSphereD@,VRageMath.PlaneD@,System.Double@)">GetSpherePlaneIntersection(ref BoundingSphereD, ref PlaneD, out Double)</h4>
  <div class="markdown level1 summary"><p>This tells if a sphere is BEHIND, in FRONT, or INTERSECTS a plane, also it's distance</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MySpherePlaneIntersectionEnum GetSpherePlaneIntersection(ref BoundingSphereD sphere, ref PlaneD plane, out double distanceFromPlaneToSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">distanceFromPlaneToSphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MySpherePlaneIntersectionEnum.html">MySpherePlaneIntersectionEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetSphereTriangleIntersection_" data-uid="VRage.Utils.MyUtils.GetSphereTriangleIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_GetSphereTriangleIntersection_VRageMath_BoundingSphere__VRageMath_Plane__VRage_MyTriangle_Vertices__" data-uid="VRage.Utils.MyUtils.GetSphereTriangleIntersection(VRageMath.BoundingSphere@,VRageMath.Plane@,VRage.MyTriangle_Vertices@)">GetSphereTriangleIntersection(ref BoundingSphere, ref Plane, ref MyTriangle_Vertices)</h4>
  <div class="markdown level1 summary"><p>Method returns intersection point between sphere and triangle (which is defined by vertexes and plane).
If no intersection found, method returns null.
See below how intersection point can be calculated, because it's not so easy - for example sphere vs. triangle will
hardly generate just intersection point... more like intersection area or something.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;Vector3&gt; GetSphereTriangleIntersection(ref BoundingSphere sphere, ref Plane trianglePlane, ref MyTriangle_Vertices triangle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">trianglePlane</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyTriangle_Vertices</span></td>
        <td><span class="parametername">triangle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetTransformNormalNormalized_" data-uid="VRage.Utils.MyUtils.GetTransformNormalNormalized*"></a>
  <h4 id="VRage_Utils_MyUtils_GetTransformNormalNormalized_VRageMath_Vector3D_VRageMath_MatrixD__" data-uid="VRage.Utils.MyUtils.GetTransformNormalNormalized(VRageMath.Vector3D,VRageMath.MatrixD@)">GetTransformNormalNormalized(Vector3D, ref MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetTransformNormalNormalized(Vector3D vec, ref MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetUIntFromString_" data-uid="VRage.Utils.MyUtils.GetUIntFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetUIntFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetUIntFromString(System.String)">GetUIntFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;uint&gt; GetUIntFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.UInt32</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetUIntFromString_" data-uid="VRage.Utils.MyUtils.GetUIntFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetUIntFromString_System_String_System_UInt32_" data-uid="VRage.Utils.MyUtils.GetUIntFromString(System.String,System.UInt32)">GetUIntFromString(String, UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static uint GetUIntFromString(string s, uint defaultValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetULongFromString_" data-uid="VRage.Utils.MyUtils.GetULongFromString*"></a>
  <h4 id="VRage_Utils_MyUtils_GetULongFromString_System_String_" data-uid="VRage.Utils.MyUtils.GetULongFromString(System.String)">GetULongFromString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;ulong&gt; GetULongFromString(string s)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">s</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.UInt64</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_GetVector3Scaled_" data-uid="VRage.Utils.MyUtils.GetVector3Scaled*"></a>
  <h4 id="VRage_Utils_MyUtils_GetVector3Scaled_VRageMath_Vector3D_System_Single_" data-uid="VRage.Utils.MyUtils.GetVector3Scaled(VRageMath.Vector3D,System.Single)">GetVector3Scaled(Vector3D, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D GetVector3Scaled(Vector3D originalVector, float newLength)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">originalVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">newLength</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_HasValidLength_" data-uid="VRage.Utils.MyUtils.HasValidLength*"></a>
  <h4 id="VRage_Utils_MyUtils_HasValidLength_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.HasValidLength(VRageMath.Vector3)">HasValidLength(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool HasValidLength(Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_HasValidLength_" data-uid="VRage.Utils.MyUtils.HasValidLength*"></a>
  <h4 id="VRage_Utils_MyUtils_HasValidLength_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.HasValidLength(VRageMath.Vector3D)">HasValidLength(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool HasValidLength(Vector3D vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Init_" data-uid="VRage.Utils.MyUtils.Init*"></a>
  <h4 id="VRage_Utils_MyUtils_Init__1___0__" data-uid="VRage.Utils.MyUtils.Init``1(``0@)">Init&lt;T&gt;(ref T)</h4>
  <div class="markdown level1 summary"><p>When location is null, creates new instance, stores it in location and returns it.
When location is not null, returns it.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Init&lt;T&gt;(ref T location)
    where T : class, new()</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">location</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_InterlockedMax_" data-uid="VRage.Utils.MyUtils.InterlockedMax*"></a>
  <h4 id="VRage_Utils_MyUtils_InterlockedMax_System_Int32__System_Int32_" data-uid="VRage.Utils.MyUtils.InterlockedMax(System.Int32@,System.Int32)">InterlockedMax(ref Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int InterlockedMax(ref int storage, int value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">storage</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><p>Previous value, not max!</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_InterlockedMax_" data-uid="VRage.Utils.MyUtils.InterlockedMax*"></a>
  <h4 id="VRage_Utils_MyUtils_InterlockedMax_System_Int64__System_Int64_" data-uid="VRage.Utils.MyUtils.InterlockedMax(System.Int64@,System.Int64)">InterlockedMax(ref Int64, Int64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void InterlockedMax(ref long storage, long value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">storage</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_System_Single_System_Single_" data-uid="VRage.Utils.MyUtils.IsEqual(System.Single,System.Single)">IsEqual(Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(float value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRage.Utils.MyUtils.IsEqual(VRageMath.Matrix,VRageMath.Matrix)">IsEqual(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(Matrix value1, Matrix value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRage.Utils.MyUtils.IsEqual(VRageMath.Quaternion,VRageMath.Quaternion)">IsEqual(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(Quaternion value1, Quaternion value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRage.Utils.MyUtils.IsEqual(VRageMath.QuaternionD,VRageMath.QuaternionD)">IsEqual(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(QuaternionD value1, QuaternionD value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRage.Utils.MyUtils.IsEqual(VRageMath.Vector2,VRageMath.Vector2)">IsEqual(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsEqual_" data-uid="VRage.Utils.MyUtils.IsEqual*"></a>
  <h4 id="VRage_Utils_MyUtils_IsEqual_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.IsEqual(VRageMath.Vector3,VRageMath.Vector3)">IsEqual(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEqual(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsLineIntersectingBoundingSphere_" data-uid="VRage.Utils.MyUtils.IsLineIntersectingBoundingSphere*"></a>
  <h4 id="VRage_Utils_MyUtils_IsLineIntersectingBoundingSphere_VRageMath_LineD__VRageMath_BoundingSphereD__" data-uid="VRage.Utils.MyUtils.IsLineIntersectingBoundingSphere(VRageMath.LineD@,VRageMath.BoundingSphereD@)">IsLineIntersectingBoundingSphere(ref LineD, ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Check intersection between line and bounding sphere
We don't use BoundingSphere.Contains(Ray ...) because ray doesn't have an end, but line does, so we need
to check if line really intersects the sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsLineIntersectingBoundingSphere(ref LineD line, ref BoundingSphereD boundingSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">boundingSphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_System_Double_" data-uid="VRage.Utils.MyUtils.IsValid(System.Double)">IsValid(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(double f)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">f</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_System_Nullable_VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.IsValid(System.Nullable{VRageMath.Vector3})">IsValid(Nullable&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Nullable&lt;Vector3&gt; vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_System_Single_" data-uid="VRage.Utils.MyUtils.IsValid(System.Single)">IsValid(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(float f)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">f</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_Matrix_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.Matrix)">IsValid(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_MatrixD_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.MatrixD)">IsValid(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_Quaternion_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.Quaternion)">IsValid(Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Quaternion q)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">q</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_Vector2_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.Vector2)">IsValid(Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Vector2 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.Vector3)">IsValid(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValid_" data-uid="VRage.Utils.MyUtils.IsValid*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValid_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.IsValid(VRageMath.Vector3D)">IsValid(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValid(Vector3D vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValidNormal_" data-uid="VRage.Utils.MyUtils.IsValidNormal*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValidNormal_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.IsValidNormal(VRageMath.Vector3)">IsValidNormal(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValidNormal(Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsValidOrZero_" data-uid="VRage.Utils.MyUtils.IsValidOrZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsValidOrZero_VRageMath_Matrix_" data-uid="VRage.Utils.MyUtils.IsValidOrZero(VRageMath.Matrix)">IsValidOrZero(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValidOrZero(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsWrongTriangle_" data-uid="VRage.Utils.MyUtils.IsWrongTriangle*"></a>
  <h4 id="VRage_Utils_MyUtils_IsWrongTriangle_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.IsWrongTriangle(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">IsWrongTriangle(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsWrongTriangle(Vector3 vertex0, Vector3 vertex1, Vector3 vertex2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vertex0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vertex1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vertex2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_System_Double_System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(System.Double,System.Single)">IsZero(Double, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(double value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_System_Single_System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(System.Single,System.Single)">IsZero(Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(float value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Quaternion_System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Quaternion,System.Single)">IsZero(Quaternion, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Quaternion value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Quaternion__System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Quaternion@,System.Single)">IsZero(ref Quaternion, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Quaternion value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Vector3_System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Vector3,System.Single)">IsZero(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector3 value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Vector3__System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Vector3@,System.Single)">IsZero(ref Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Vector3 value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Vector3D_System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Vector3D,System.Single)">IsZero(Vector3D, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector3D value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Vector3D__System_Single_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Vector3D@,System.Single)">IsZero(ref Vector3D, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Vector3D value, float epsilon = 1E-05F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_IsZero_" data-uid="VRage.Utils.MyUtils.IsZero*"></a>
  <h4 id="VRage_Utils_MyUtils_IsZero_VRageMath_Vector4_" data-uid="VRage.Utils.MyUtils.IsZero(VRageMath.Vector4)">IsZero(Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_LinePlaneIntersection_" data-uid="VRage.Utils.MyUtils.LinePlaneIntersection*"></a>
  <h4 id="VRage_Utils_MyUtils_LinePlaneIntersection_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3D_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.LinePlaneIntersection(VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3D,VRageMath.Vector3)">LinePlaneIntersection(Vector3D, Vector3, Vector3D, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D LinePlaneIntersection(Vector3D planePoint, Vector3 planeNormal, Vector3D lineStart, Vector3 lineDir)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">planePoint</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">planeNormal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">lineStart</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">lineDir</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.Matrix@,VRageMath.Matrix@)">Normalize(ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Protected normalize with assert</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Matrix m, out Matrix normalized)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">normalized</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.MatrixD@,VRageMath.MatrixD@)">Normalize(ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref MatrixD m, out MatrixD normalized)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">normalized</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.Vector3)">Normalize(Vector3)</h4>
  <div class="markdown level1 summary"><p>Protected normalize with assert</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Normalize(Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.Vector3@,VRageMath.Vector3@)">Normalize(ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Protected normalize with assert</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector3 vec, out Vector3 normalized)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normalized</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_Vector3D_" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.Vector3D)">Normalize(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D Normalize(Vector3D vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Normalize_" data-uid="VRage.Utils.MyUtils.Normalize*"></a>
  <h4 id="VRage_Utils_MyUtils_Normalize_VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRage.Utils.MyUtils.Normalize(VRageMath.Vector3D@,VRageMath.Vector3D@)">Normalize(ref Vector3D, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector3D vec, out Vector3D normalized)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normalized</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_PrepareCollection_" data-uid="VRage.Utils.MyUtils.PrepareCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_PrepareCollection__2___0__" data-uid="VRage.Utils.MyUtils.PrepareCollection``2(``0@)">PrepareCollection&lt;TCollection, TElement&gt;(ref TCollection)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static TCollection PrepareCollection&lt;TCollection, TElement&gt;(ref TCollection collection)
    where TCollection : class, ICollection&lt;TElement&gt;, new()</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TCollection</span></td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TCollection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TCollection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollection_" data-uid="VRage.Utils.MyUtils.ReuseCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollection__1_System_Collections_Generic_HashSet___0___" data-uid="VRage.Utils.MyUtils.ReuseCollection``1(System.Collections.Generic.HashSet{``0}@)">ReuseCollection&lt;TElement&gt;(ref HashSet&lt;TElement&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearCollectionToken&lt;HashSet&lt;TElement&gt;, TElement&gt; ReuseCollection&lt;TElement&gt;(ref HashSet&lt;TElement&gt; collection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.HashSet</span>&lt;TElement&gt;</td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken</a>&lt;<span class="xref">System.Collections.Generic.HashSet</span>&lt;TElement&gt;, TElement&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollection_" data-uid="VRage.Utils.MyUtils.ReuseCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollection__1_System_Collections_Generic_List___0___" data-uid="VRage.Utils.MyUtils.ReuseCollection``1(System.Collections.Generic.List{``0}@)">ReuseCollection&lt;TElement&gt;(ref List&lt;TElement&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearCollectionToken&lt;List&lt;TElement&gt;, TElement&gt; ReuseCollection&lt;TElement&gt;(ref List&lt;TElement&gt; collection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;TElement&gt;</td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken</a>&lt;<span class="xref">System.Collections.Generic.List</span>&lt;TElement&gt;, TElement&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollection_" data-uid="VRage.Utils.MyUtils.ReuseCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollection__1_VRage_Library_Collections_MyList___0___" data-uid="VRage.Utils.MyUtils.ReuseCollection``1(VRage.Library.Collections.MyList{``0}@)">ReuseCollection&lt;TElement&gt;(ref MyList&lt;TElement&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearCollectionToken&lt;MyList&lt;TElement&gt;, TElement&gt; ReuseCollection&lt;TElement&gt;(ref MyList&lt;TElement&gt; collection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;TElement&gt;</td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken</a>&lt;<span class="xref">VRage.Library.Collections.MyList</span>&lt;TElement&gt;, TElement&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollection_" data-uid="VRage.Utils.MyUtils.ReuseCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollection__2___0__" data-uid="VRage.Utils.MyUtils.ReuseCollection``2(``0@)">ReuseCollection&lt;TCollection, TElement&gt;(ref TCollection)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearCollectionToken&lt;TCollection, TElement&gt; ReuseCollection&lt;TCollection, TElement&gt;(ref TCollection collection)
    where TCollection : class, ICollection&lt;TElement&gt;, new()</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TCollection</span></td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken</a>&lt;TCollection, TElement&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TCollection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollection_" data-uid="VRage.Utils.MyUtils.ReuseCollection*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollection__2_System_Collections_Generic_Dictionary___0___1___" data-uid="VRage.Utils.MyUtils.ReuseCollection``2(System.Collections.Generic.Dictionary{``0,``1}@)">ReuseCollection&lt;TKey, TValue&gt;(ref Dictionary&lt;TKey, TValue&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearCollectionToken&lt;Dictionary&lt;TKey, TValue&gt;, KeyValuePair&lt;TKey, TValue&gt;&gt; ReuseCollection&lt;TKey, TValue&gt;(ref Dictionary&lt;TKey, TValue&gt; collection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.Dictionary</span>&lt;TKey, TValue&gt;</td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken</a>&lt;<span class="xref">System.Collections.Generic.Dictionary</span>&lt;TKey, TValue&gt;, <span class="xref">System.Collections.Generic.KeyValuePair</span>&lt;TKey, TValue&gt;&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TKey</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ReuseCollectionNested_" data-uid="VRage.Utils.MyUtils.ReuseCollectionNested*"></a>
  <h4 id="VRage_Utils_MyUtils_ReuseCollectionNested__1_System_Collections_Generic_List___0___" data-uid="VRage.Utils.MyUtils.ReuseCollectionNested``1(System.Collections.Generic.List{``0}@)">ReuseCollectionNested&lt;TElement&gt;(ref List&lt;TElement&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyUtils.ClearRangeToken&lt;TElement&gt; ReuseCollectionNested&lt;TElement&gt;(ref List&lt;TElement&gt; collection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;TElement&gt;</td>
        <td><span class="parametername">collection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyUtils.ClearRangeToken-1.html">MyUtils.ClearRangeToken</a>&lt;TElement&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TElement</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_RotationMatrixToYawPitchRoll_" data-uid="VRage.Utils.MyUtils.RotationMatrixToYawPitchRoll*"></a>
  <h4 id="VRage_Utils_MyUtils_RotationMatrixToYawPitchRoll_VRageMath_Matrix__System_Single__System_Single__System_Single__" data-uid="VRage.Utils.MyUtils.RotationMatrixToYawPitchRoll(VRageMath.Matrix@,System.Single@,System.Single@,System.Single@)">RotationMatrixToYawPitchRoll(ref Matrix, out Single, out Single, out Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RotationMatrixToYawPitchRoll(ref Matrix mx, out float yaw, out float pitch, out float roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">mx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_RotationMatrixToYawPitchRoll_" data-uid="VRage.Utils.MyUtils.RotationMatrixToYawPitchRoll*"></a>
  <h4 id="VRage_Utils_MyUtils_RotationMatrixToYawPitchRoll_VRageMath_MatrixD__System_Double__System_Double__System_Double__" data-uid="VRage.Utils.MyUtils.RotationMatrixToYawPitchRoll(VRageMath.MatrixD@,System.Double@,System.Double@,System.Double@)">RotationMatrixToYawPitchRoll(ref MatrixD, out Double, out Double, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RotationMatrixToYawPitchRoll(ref MatrixD mx, out double yaw, out double pitch, out double roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">mx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yaw</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">pitch</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">roll</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_SerializeValue_" data-uid="VRage.Utils.MyUtils.SerializeValue*"></a>
  <h4 id="VRage_Utils_MyUtils_SerializeValue_System_Xml_XmlWriter_VRageMath_Vector3_" data-uid="VRage.Utils.MyUtils.SerializeValue(System.Xml.XmlWriter,VRageMath.Vector3)">SerializeValue(XmlWriter, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SerializeValue(XmlWriter writer, Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Xml.XmlWriter</span></td>
        <td><span class="parametername">writer</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_SerializeValue_" data-uid="VRage.Utils.MyUtils.SerializeValue*"></a>
  <h4 id="VRage_Utils_MyUtils_SerializeValue_System_Xml_XmlWriter_VRageMath_Vector4_" data-uid="VRage.Utils.MyUtils.SerializeValue(System.Xml.XmlWriter,VRageMath.Vector4)">SerializeValue(XmlWriter, Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SerializeValue(XmlWriter writer, Vector4 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Xml.XmlWriter</span></td>
        <td><span class="parametername">writer</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_ShuffleList_" data-uid="VRage.Utils.MyUtils.ShuffleList*"></a>
  <h4 id="VRage_Utils_MyUtils_ShuffleList__1_System_Collections_Generic_IList___0__System_Int32_System_Nullable_System_Int32__" data-uid="VRage.Utils.MyUtils.ShuffleList``1(System.Collections.Generic.IList{``0},System.Int32,System.Nullable{System.Int32})">ShuffleList&lt;T&gt;(IList&lt;T&gt;, Int32, Nullable&lt;Int32&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ShuffleList&lt;T&gt;(this IList&lt;T&gt; list, int offset = 0, Nullable&lt;int&gt; count = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IList</span>&lt;T&gt;</td>
        <td><span class="parametername">list</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">offset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int32</span>&gt;</td>
        <td><span class="parametername">count</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_StripInvalidChars_" data-uid="VRage.Utils.MyUtils.StripInvalidChars*"></a>
  <h4 id="VRage_Utils_MyUtils_StripInvalidChars_System_String_" data-uid="VRage.Utils.MyUtils.StripInvalidChars(System.String)">StripInvalidChars(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string StripInvalidChars(string filename)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">filename</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_Swap_" data-uid="VRage.Utils.MyUtils.Swap*"></a>
  <h4 id="VRage_Utils_MyUtils_Swap__1___0____0__" data-uid="VRage.Utils.MyUtils.Swap``1(``0@,``0@)">Swap&lt;T&gt;(ref T, ref T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Swap&lt;T&gt;(ref T lhs, ref T rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_TryParseWithSuffix_" data-uid="VRage.Utils.MyUtils.TryParseWithSuffix*"></a>
  <h4 id="VRage_Utils_MyUtils_TryParseWithSuffix_System_String_System_Globalization_NumberStyles_System_IFormatProvider_System_Single__System_Tuple_System_String_System_Single____" data-uid="VRage.Utils.MyUtils.TryParseWithSuffix(System.String,System.Globalization.NumberStyles,System.IFormatProvider,System.Single@,System.Tuple{System.String,System.Single}[])">TryParseWithSuffix(String, NumberStyles, IFormatProvider, out Single, Tuple&lt;String, Single&gt;[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool TryParseWithSuffix(this string text, NumberStyles numberStyle, IFormatProvider formatProvider, out float value, Tuple&lt;string, float&gt;[] suffix = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">text</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Globalization.NumberStyles</span></td>
        <td><span class="parametername">numberStyle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.IFormatProvider</span></td>
        <td><span class="parametername">formatProvider</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Tuple</span>&lt;<span class="xref">System.String</span>, <span class="xref">System.Single</span>&gt;[]</td>
        <td><span class="parametername">suffix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyUtils_VectorPlaneRotation_" data-uid="VRage.Utils.MyUtils.VectorPlaneRotation*"></a>
  <h4 id="VRage_Utils_MyUtils_VectorPlaneRotation_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D__VRageMath_Vector3D__System_Single_" data-uid="VRage.Utils.MyUtils.VectorPlaneRotation(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D@,VRageMath.Vector3D@,System.Single)">VectorPlaneRotation(Vector3D, Vector3D, out Vector3D, out Vector3D, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VectorPlaneRotation(Vector3D xVector, Vector3D yVector, out Vector3D xOut, out Vector3D yOut, float angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">xVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">yVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">xOut</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">yOut</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
