﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingFrustumD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingFrustumD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingFrustumD">
  
  
  <h1 id="VRageMath_BoundingFrustumD" data-uid="VRageMath.BoundingFrustumD" class="text-break">Class BoundingFrustumD
  </h1>
  <div class="markdown level0 summary"><p>Defines a frustum and helps determine whether forms intersect with it.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingFrustumD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingFrustumD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class BoundingFrustumD : Object, IEquatable&lt;BoundingFrustumD&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingFrustumD__ctor_" data-uid="VRageMath.BoundingFrustumD.#ctor*"></a>
  <h4 id="VRageMath_BoundingFrustumD__ctor" data-uid="VRageMath.BoundingFrustumD.#ctor">BoundingFrustumD()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingFrustumD()</code></pre>
  </div>
  
  
  <a id="VRageMath_BoundingFrustumD__ctor_" data-uid="VRageMath.BoundingFrustumD.#ctor*"></a>
  <h4 id="VRageMath_BoundingFrustumD__ctor_VRageMath_MatrixD_" data-uid="VRageMath.BoundingFrustumD.#ctor(VRageMath.MatrixD)">BoundingFrustumD(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingFrustumD(MatrixD value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Combined matrix that usually takes view × projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingFrustumD_CornerCount" data-uid="VRageMath.BoundingFrustumD.CornerCount">CornerCount</h4>
  <div class="markdown level1 summary"><p>Specifies the total number of corners (8) in the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int CornerCount = 8</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingFrustumD_Bottom_" data-uid="VRageMath.BoundingFrustumD.Bottom*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Bottom" data-uid="VRageMath.BoundingFrustumD.Bottom">Bottom</h4>
  <div class="markdown level1 summary"><p>Gets the bottom plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Bottom { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Far_" data-uid="VRageMath.BoundingFrustumD.Far*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Far" data-uid="VRageMath.BoundingFrustumD.Far">Far</h4>
  <div class="markdown level1 summary"><p>Gets the far plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Far { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Item_" data-uid="VRageMath.BoundingFrustumD.Item*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Item_System_Int32_" data-uid="VRageMath.BoundingFrustumD.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD this[int index] { get; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Left_" data-uid="VRageMath.BoundingFrustumD.Left*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Left" data-uid="VRageMath.BoundingFrustumD.Left">Left</h4>
  <div class="markdown level1 summary"><p>Gets the left plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Left { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Matrix_" data-uid="VRageMath.BoundingFrustumD.Matrix*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Matrix" data-uid="VRageMath.BoundingFrustumD.Matrix">Matrix</h4>
  <div class="markdown level1 summary"><p>Gets or sets the Matrix that describes this bounding frustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD Matrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Near_" data-uid="VRageMath.BoundingFrustumD.Near*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Near" data-uid="VRageMath.BoundingFrustumD.Near">Near</h4>
  <div class="markdown level1 summary"><p>Gets the near plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Near { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Right_" data-uid="VRageMath.BoundingFrustumD.Right*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Right" data-uid="VRageMath.BoundingFrustumD.Right">Right</h4>
  <div class="markdown level1 summary"><p>Gets the right plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Right { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Top_" data-uid="VRageMath.BoundingFrustumD.Top*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Top" data-uid="VRageMath.BoundingFrustumD.Top">Top</h4>
  <div class="markdown level1 summary"><p>Gets the top plane of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD Top { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.BoundingBoxD)">Contains(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check against the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_BoundingBoxD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.BoundingBoxD@,VRageMath.ContainmentType@)">Contains(ref BoundingBoxD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBoxD box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.BoundingFrustumD)">Contains(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustumD to check against the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.BoundingSphereD)">Contains(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check against the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_BoundingSphereD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.BoundingSphereD@,VRageMath.ContainmentType@)">Contains(ref BoundingSphereD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingSphereD sphere, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_Vector3D_" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.Vector3D)">Contains(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to check against the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Contains_" data-uid="VRageMath.BoundingFrustumD.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Contains_VRageMath_Vector3D__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustumD.Contains(VRageMath.Vector3D@,VRageMath.ContainmentType@)">Contains(ref Vector3D, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector3D point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Equals_" data-uid="VRageMath.BoundingFrustumD.Equals*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Equals_System_Object_" data-uid="VRageMath.BoundingFrustumD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Equals_" data-uid="VRageMath.BoundingFrustumD.Equals*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Equals_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingFrustumD.Equals(VRageMath.BoundingFrustumD)">Equals(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified BoundingFrustumD is equal to the current BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingFrustumD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingFrustumD to compare with the current BoundingFrustumD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_GetCorners_" data-uid="VRageMath.BoundingFrustumD.GetCorners*"></a>
  <h4 id="VRageMath_BoundingFrustumD_GetCorners" data-uid="VRageMath.BoundingFrustumD.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingFrustumD. ALLOCATION!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_GetCorners_" data-uid="VRageMath.BoundingFrustumD.GetCorners*"></a>
  <h4 id="VRageMath_BoundingFrustumD_GetCorners_VRageMath_Vector3D___" data-uid="VRageMath.BoundingFrustumD.GetCorners(VRageMath.Vector3D[])">GetCorners(Vector3D[])</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector3D[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3D points where the corners of the BoundingFrustumD are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_GetCornersUnsafe_" data-uid="VRageMath.BoundingFrustumD.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingFrustumD_GetCornersUnsafe_VRageMath_Vector3D__" data-uid="VRageMath.BoundingFrustumD.GetCornersUnsafe(VRageMath.Vector3D*)">GetCornersUnsafe(Vector3D*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector3D*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3 points where the corners of the BoundingBox are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_GetHashCode_" data-uid="VRageMath.BoundingFrustumD.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingFrustumD_GetHashCode" data-uid="VRageMath.BoundingFrustumD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.BoundingBoxD)">Intersects(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects the specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_BoundingBoxD__System_Boolean__" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.BoundingBoxD@,System.Boolean@)">Intersects(ref BoundingBoxD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects a BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBoxD box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingFrustumD and BoundingBoxD intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.BoundingFrustumD)">Intersects(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects the specified BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustumD to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.BoundingSphereD)">Intersects(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_BoundingSphereD__System_Boolean__" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.BoundingSphereD@,System.Boolean@)">Intersects(ref BoundingSphereD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphereD sphere, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingFrustumD and BoundingSphere intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_PlaneD_" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.PlaneD)">Intersects(PlaneD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects the specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(PlaneD plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_PlaneD__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.PlaneD@,VRageMath.PlaneIntersectionType@)">Intersects(ref PlaneD, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref PlaneD plane, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the BoundingFrustumD intersects the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_RayD_" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.RayD)">Intersects(RayD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects the specified Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;double&gt; Intersects(RayD ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_Intersects_" data-uid="VRageMath.BoundingFrustumD.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustumD_Intersects_VRageMath_RayD__System_Nullable_System_Double___" data-uid="VRageMath.BoundingFrustumD.Intersects(VRageMath.RayD@,System.Nullable{System.Double}@)">Intersects(ref RayD, out Nullable&lt;Double&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustumD intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref RayD ray, out Nullable&lt;double&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingFrustumD or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_ToString_" data-uid="VRageMath.BoundingFrustumD.ToString*"></a>
  <h4 id="VRageMath_BoundingFrustumD_ToString" data-uid="VRageMath.BoundingFrustumD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingFrustumD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingFrustumD_op_Equality_" data-uid="VRageMath.BoundingFrustumD.op_Equality*"></a>
  <h4 id="VRageMath_BoundingFrustumD_op_Equality_VRageMath_BoundingFrustumD_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingFrustumD.op_Equality(VRageMath.BoundingFrustumD,VRageMath.BoundingFrustumD)">Equality(BoundingFrustumD, BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingFrustumD are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingFrustumD a, BoundingFrustumD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The BoundingFrustumD to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The BoundingFrustumD to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustumD_op_Inequality_" data-uid="VRageMath.BoundingFrustumD.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingFrustumD_op_Inequality_VRageMath_BoundingFrustumD_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingFrustumD.op_Inequality(VRageMath.BoundingFrustumD,VRageMath.BoundingFrustumD)">Inequality(BoundingFrustumD, BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingFrustumD are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingFrustumD a, BoundingFrustumD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The BoundingFrustumD to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The BoundingFrustumD to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
