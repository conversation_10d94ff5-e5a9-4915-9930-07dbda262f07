﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_HumanoidBotDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_HumanoidBotDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition" class="text-break">Class MyObjectBuilder_HumanoidBotDefinition
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></div>
    <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html">MyObjectBuilder_BotDefinition</a></div>
    <div class="level4"><a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html">MyObjectBuilder_AgentDefinition</a></div>
    <div class="level5"><span class="xref">MyObjectBuilder_HumanoidBotDefinition</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_BotModel">MyObjectBuilder_AgentDefinition.BotModel</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_TargetType">MyObjectBuilder_AgentDefinition.TargetType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_InventoryContentGenerated">MyObjectBuilder_AgentDefinition.InventoryContentGenerated</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_InventoryContainerTypeId">MyObjectBuilder_AgentDefinition.InventoryContainerTypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_RemoveAfterDeath">MyObjectBuilder_AgentDefinition.RemoveAfterDeath</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_RespawnTimeMs">MyObjectBuilder_AgentDefinition.RespawnTimeMs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_RemoveTimeMs">MyObjectBuilder_AgentDefinition.RemoveTimeMs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_FactionTag">MyObjectBuilder_AgentDefinition.FactionTag</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_AttackSound">MyObjectBuilder_AgentDefinition.AttackSound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_AttackLength">MyObjectBuilder_AgentDefinition.AttackLength</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_CharacterDamage">MyObjectBuilder_AgentDefinition.CharacterDamage</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_GridDamage">MyObjectBuilder_AgentDefinition.GridDamage</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_AttackRadius">MyObjectBuilder_AgentDefinition.AttackRadius</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_TargetCharacters">MyObjectBuilder_AgentDefinition.TargetCharacters</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_AgentDefinition.html#VRage_Game_MyObjectBuilder_AgentDefinition_TargetGrids">MyObjectBuilder_AgentDefinition.TargetGrids</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html#VRage_Game_MyObjectBuilder_BotDefinition_BotBehaviorTree">MyObjectBuilder_BotDefinition.BotBehaviorTree</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html#VRage_Game_MyObjectBuilder_BotDefinition_BehaviorType">MyObjectBuilder_BotDefinition.BehaviorType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html#VRage_Game_MyObjectBuilder_BotDefinition_BehaviorSubtype">MyObjectBuilder_BotDefinition.BehaviorSubtype</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_BotDefinition.html#VRage_Game_MyObjectBuilder_BotDefinition_Commandable">MyObjectBuilder_BotDefinition.Commandable</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Id">MyObjectBuilder_DefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DisplayName">MyObjectBuilder_DefinitionBase.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Description">MyObjectBuilder_DefinitionBase.Description</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Icons">MyObjectBuilder_DefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Public">MyObjectBuilder_DefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Enabled">MyObjectBuilder_DefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_AvailableInSurvival">MyObjectBuilder_DefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DescriptionArgs">MyObjectBuilder_DefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DLCs">MyObjectBuilder_DefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_HumanoidBotDefinition : MyObjectBuilder_AgentDefinition</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition__ctor_" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition__ctor" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.#ctor">MyObjectBuilder_HumanoidBotDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_HumanoidBotDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition_InventoryItems" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.InventoryItems">InventoryItems</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_HumanoidBotDefinition.Item[] InventoryItems</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.Item.html">MyObjectBuilder_HumanoidBotDefinition.Item</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_HumanoidBotDefinition_StartingItem" data-uid="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.StartingItem">StartingItem</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_HumanoidBotDefinition.Item StartingItem</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_HumanoidBotDefinition.Item.html">MyObjectBuilder_HumanoidBotDefinition.Item</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
