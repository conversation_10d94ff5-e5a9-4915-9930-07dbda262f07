﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MatrixD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MatrixD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.MatrixD">
  
  
  <h1 id="VRageMath_MatrixD" data-uid="VRageMath.MatrixD" class="text-break">Class MatrixD
  </h1>
  <div class="markdown level0 summary"><p>Defines a matrix.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MatrixD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_MatrixD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MatrixD : ValueType, IEquatable&lt;MatrixD&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_MatrixD__ctor_" data-uid="VRageMath.MatrixD.#ctor*"></a>
  <h4 id="VRageMath_MatrixD__ctor_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">MatrixD(Double, Double, Double, Double, Double, Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Matrix with rotation data</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD(double m11, double m12, double m13, double m21, double m22, double m23, double m31, double m32, double m33)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m11</span></td>
        <td><p>Value to initialize m11 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m12</span></td>
        <td><p>Value to initialize m12 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m13</span></td>
        <td><p>Value to initialize m13 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m21</span></td>
        <td><p>Value to initialize m21 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m22</span></td>
        <td><p>Value to initialize m22 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m23</span></td>
        <td><p>Value to initialize m23 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m31</span></td>
        <td><p>Value to initialize m31 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m32</span></td>
        <td><p>Value to initialize m32 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m33</span></td>
        <td><p>Value to initialize m33 to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD__ctor_" data-uid="VRageMath.MatrixD.#ctor*"></a>
  <h4 id="VRageMath_MatrixD__ctor_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">MatrixD(Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD(double m11, double m12, double m13, double m14, double m21, double m22, double m23, double m24, double m31, double m32, double m33, double m34, double m41, double m42, double m43, double m44)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m11</span></td>
        <td><p>Value to initialize m11 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m12</span></td>
        <td><p>Value to initialize m12 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m13</span></td>
        <td><p>Value to initialize m13 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m14</span></td>
        <td><p>Value to initialize m14 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m21</span></td>
        <td><p>Value to initialize m21 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m22</span></td>
        <td><p>Value to initialize m22 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m23</span></td>
        <td><p>Value to initialize m23 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m24</span></td>
        <td><p>Value to initialize m24 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m31</span></td>
        <td><p>Value to initialize m31 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m32</span></td>
        <td><p>Value to initialize m32 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m33</span></td>
        <td><p>Value to initialize m33 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m34</span></td>
        <td><p>Value to initialize m34 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m41</span></td>
        <td><p>Value to initialize m41 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m42</span></td>
        <td><p>Value to initialize m42 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m43</span></td>
        <td><p>Value to initialize m43 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">m44</span></td>
        <td><p>Value to initialize m44 to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD__ctor_" data-uid="VRageMath.MatrixD.#ctor*"></a>
  <h4 id="VRageMath_MatrixD__ctor_VRageMath_Matrix_" data-uid="VRageMath.MatrixD.#ctor(VRageMath.Matrix)">MatrixD(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD(Matrix m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_MatrixD_Identity" data-uid="VRageMath.MatrixD.Identity">Identity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Identity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M11" data-uid="VRageMath.MatrixD.M11">M11</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M11</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M12" data-uid="VRageMath.MatrixD.M12">M12</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M12</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M13" data-uid="VRageMath.MatrixD.M13">M13</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M13</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M14" data-uid="VRageMath.MatrixD.M14">M14</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M14</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M21" data-uid="VRageMath.MatrixD.M21">M21</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M21</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M22" data-uid="VRageMath.MatrixD.M22">M22</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M22</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M23" data-uid="VRageMath.MatrixD.M23">M23</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M23</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M24" data-uid="VRageMath.MatrixD.M24">M24</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M24</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M31" data-uid="VRageMath.MatrixD.M31">M31</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M31</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M32" data-uid="VRageMath.MatrixD.M32">M32</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M32</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M33" data-uid="VRageMath.MatrixD.M33">M33</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M33</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M34" data-uid="VRageMath.MatrixD.M34">M34</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M34</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M41" data-uid="VRageMath.MatrixD.M41">M41</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M41</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M42" data-uid="VRageMath.MatrixD.M42">M42</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M42</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M43" data-uid="VRageMath.MatrixD.M43">M43</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M43</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_M44" data-uid="VRageMath.MatrixD.M44">M44</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double M44</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixD_Zero" data-uid="VRageMath.MatrixD.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_MatrixD_Backward_" data-uid="VRageMath.MatrixD.Backward*"></a>
  <h4 id="VRageMath_MatrixD_Backward" data-uid="VRageMath.MatrixD.Backward">Backward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the backward vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Backward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Col0_" data-uid="VRageMath.MatrixD.Col0*"></a>
  <h4 id="VRageMath_MatrixD_Col0" data-uid="VRageMath.MatrixD.Col0">Col0</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Col0 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Col1_" data-uid="VRageMath.MatrixD.Col1*"></a>
  <h4 id="VRageMath_MatrixD_Col1" data-uid="VRageMath.MatrixD.Col1">Col1</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Col1 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Col2_" data-uid="VRageMath.MatrixD.Col2*"></a>
  <h4 id="VRageMath_MatrixD_Col2" data-uid="VRageMath.MatrixD.Col2">Col2</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Col2 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Down_" data-uid="VRageMath.MatrixD.Down*"></a>
  <h4 id="VRageMath_MatrixD_Down" data-uid="VRageMath.MatrixD.Down">Down</h4>
  <div class="markdown level1 summary"><p>Gets and sets the down vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Down { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Forward_" data-uid="VRageMath.MatrixD.Forward*"></a>
  <h4 id="VRageMath_MatrixD_Forward" data-uid="VRageMath.MatrixD.Forward">Forward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the forward vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Forward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Item_" data-uid="VRageMath.MatrixD.Item*"></a>
  <h4 id="VRageMath_MatrixD_Item_System_Int32_System_Int32_" data-uid="VRageMath.MatrixD.Item(System.Int32,System.Int32)">Item[Int32, Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double this[int row, int column] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">column</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Left_" data-uid="VRageMath.MatrixD.Left*"></a>
  <h4 id="VRageMath_MatrixD_Left" data-uid="VRageMath.MatrixD.Left">Left</h4>
  <div class="markdown level1 summary"><p>Gets and sets the left vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Left { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Right_" data-uid="VRageMath.MatrixD.Right*"></a>
  <h4 id="VRageMath_MatrixD_Right" data-uid="VRageMath.MatrixD.Right">Right</h4>
  <div class="markdown level1 summary"><p>Gets and sets the right vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Right { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rotation_" data-uid="VRageMath.MatrixD.Rotation*"></a>
  <h4 id="VRageMath_MatrixD_Rotation" data-uid="VRageMath.MatrixD.Rotation">Rotation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix3x3 Rotation { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Scale_" data-uid="VRageMath.MatrixD.Scale*"></a>
  <h4 id="VRageMath_MatrixD_Scale" data-uid="VRageMath.MatrixD.Scale">Scale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Scale { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Translation_" data-uid="VRageMath.MatrixD.Translation*"></a>
  <h4 id="VRageMath_MatrixD_Translation" data-uid="VRageMath.MatrixD.Translation">Translation</h4>
  <div class="markdown level1 summary"><p>Gets and sets the translation vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Translation { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Up_" data-uid="VRageMath.MatrixD.Up*"></a>
  <h4 id="VRageMath_MatrixD_Up" data-uid="VRageMath.MatrixD.Up">Up</h4>
  <div class="markdown level1 summary"><p>Gets and sets the up vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Up { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_MatrixD_Add_" data-uid="VRageMath.MatrixD.Add*"></a>
  <h4 id="VRageMath_MatrixD_Add_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Add(VRageMath.MatrixD,VRageMath.MatrixD)">Add(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Add(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Add_" data-uid="VRageMath.MatrixD.Add*"></a>
  <h4 id="VRageMath_MatrixD_Add_VRageMath_MatrixD__VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Add(VRageMath.MatrixD@,VRageMath.MatrixD@,VRageMath.MatrixD@)">Add(ref MatrixD, ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref MatrixD matrix1, ref MatrixD matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_AlignRotationToAxes_" data-uid="VRageMath.MatrixD.AlignRotationToAxes*"></a>
  <h4 id="VRageMath_MatrixD_AlignRotationToAxes_VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.AlignRotationToAxes(VRageMath.MatrixD@,VRageMath.MatrixD@)">AlignRotationToAxes(ref MatrixD, ref MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD AlignRotationToAxes(ref MatrixD toAlign, ref MatrixD axisDefinitionMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">toAlign</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">axisDefinitionMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_AssertIsValid_" data-uid="VRageMath.MatrixD.AssertIsValid*"></a>
  <h4 id="VRageMath_MatrixD_AssertIsValid_System_String_" data-uid="VRageMath.MatrixD.AssertIsValid(System.String)">AssertIsValid(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid(string message = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">message</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateBillboard_" data-uid="VRageMath.MatrixD.CreateBillboard*"></a>
  <h4 id="VRageMath_MatrixD_CreateBillboard_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_System_Nullable_VRageMath_Vector3D__" data-uid="VRageMath.MatrixD.CreateBillboard(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D,System.Nullable{VRageMath.Vector3D})">CreateBillboard(Vector3D, Vector3D, Vector3D, Nullable&lt;Vector3D&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates a spherical billboard that rotates around a specified object position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateBillboard(Vector3D objectPosition, Vector3D cameraPosition, Vector3D cameraUpVector, Nullable&lt;Vector3D&gt; cameraForwardVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The up vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateBillboard_" data-uid="VRageMath.MatrixD.CreateBillboard*"></a>
  <h4 id="VRageMath_MatrixD_CreateBillboard_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__System_Nullable_VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateBillboard(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@,System.Nullable{VRageMath.Vector3D},VRageMath.MatrixD@)">CreateBillboard(ref Vector3D, ref Vector3D, ref Vector3D, Nullable&lt;Vector3D&gt;, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a spherical billboard that rotates around a specified object position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateBillboard(ref Vector3D objectPosition, ref Vector3D cameraPosition, ref Vector3D cameraUpVector, Nullable&lt;Vector3D&gt; cameraForwardVector, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The up vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created billboard matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateConstrainedBillboard_" data-uid="VRageMath.MatrixD.CreateConstrainedBillboard*"></a>
  <h4 id="VRageMath_MatrixD_CreateConstrainedBillboard_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_System_Nullable_VRageMath_Vector3D__System_Nullable_VRageMath_Vector3D__" data-uid="VRageMath.MatrixD.CreateConstrainedBillboard(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D,System.Nullable{VRageMath.Vector3D},System.Nullable{VRageMath.Vector3D})">CreateConstrainedBillboard(Vector3D, Vector3D, Vector3D, Nullable&lt;Vector3D&gt;, Nullable&lt;Vector3D&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates a cylindrical billboard that rotates around a specified axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateConstrainedBillboard(Vector3D objectPosition, Vector3D cameraPosition, Vector3D rotateAxis, Nullable&lt;Vector3D&gt; cameraForwardVector, Nullable&lt;Vector3D&gt; objectForwardVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">rotateAxis</span></td>
        <td><p>Axis to rotate the billboard around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">objectForwardVector</span></td>
        <td><p>Optional forward vector of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateConstrainedBillboard_" data-uid="VRageMath.MatrixD.CreateConstrainedBillboard*"></a>
  <h4 id="VRageMath_MatrixD_CreateConstrainedBillboard_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__System_Nullable_VRageMath_Vector3D__System_Nullable_VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateConstrainedBillboard(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@,System.Nullable{VRageMath.Vector3D},System.Nullable{VRageMath.Vector3D},VRageMath.MatrixD@)">CreateConstrainedBillboard(ref Vector3D, ref Vector3D, ref Vector3D, Nullable&lt;Vector3D&gt;, Nullable&lt;Vector3D&gt;, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a cylindrical billboard that rotates around a specified axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateConstrainedBillboard(ref Vector3D objectPosition, ref Vector3D cameraPosition, ref Vector3D rotateAxis, Nullable&lt;Vector3D&gt; cameraForwardVector, Nullable&lt;Vector3D&gt; objectForwardVector, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">rotateAxis</span></td>
        <td><p>Axis to rotate the billboard around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">objectForwardVector</span></td>
        <td><p>Optional forward vector of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created billboard matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromAxisAngle_" data-uid="VRageMath.MatrixD.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromAxisAngle_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.MatrixD.CreateFromAxisAngle(VRageMath.Vector3D,System.Double)">CreateFromAxisAngle(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromAxisAngle(Vector3D axis, double angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromAxisAngle_" data-uid="VRageMath.MatrixD.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromAxisAngle_VRageMath_Vector3D__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateFromAxisAngle(VRageMath.Vector3D@,System.Double,VRageMath.MatrixD@)">CreateFromAxisAngle(ref Vector3D, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAxisAngle(ref Vector3D axis, double angle, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromDir_" data-uid="VRageMath.MatrixD.CreateFromDir*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromDir_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateFromDir(VRageMath.Vector3D)">CreateFromDir(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromDir(Vector3D dir)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromDir_" data-uid="VRageMath.MatrixD.CreateFromDir*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromDir_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateFromDir(VRageMath.Vector3D,VRageMath.Vector3D)">CreateFromDir(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromDir(Vector3D dir, Vector3D suggestedUp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">suggestedUp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromQuaternion_" data-uid="VRageMath.MatrixD.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromQuaternion_VRageMath_Quaternion_" data-uid="VRageMath.MatrixD.CreateFromQuaternion(VRageMath.Quaternion)">CreateFromQuaternion(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromQuaternion(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromQuaternion_" data-uid="VRageMath.MatrixD.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromQuaternion_VRageMath_Quaternion__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateFromQuaternion(VRageMath.Quaternion@,VRageMath.MatrixD@)">CreateFromQuaternion(ref Quaternion, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromQuaternion(ref Quaternion quaternion, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromQuaternion_" data-uid="VRageMath.MatrixD.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromQuaternion_VRageMath_QuaternionD_" data-uid="VRageMath.MatrixD.CreateFromQuaternion(VRageMath.QuaternionD)">CreateFromQuaternion(QuaternionD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromQuaternion(QuaternionD quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromTransformScale_" data-uid="VRageMath.MatrixD.CreateFromTransformScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromTransformScale_VRageMath_Quaternion_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateFromTransformScale(VRageMath.Quaternion,VRageMath.Vector3D,VRageMath.Vector3D)">CreateFromTransformScale(Quaternion, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromTransformScale(Quaternion orientation, Vector3D position, Vector3D scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromYawPitchRoll_" data-uid="VRageMath.MatrixD.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromYawPitchRoll_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreateFromYawPitchRoll(System.Double,System.Double,System.Double)">CreateFromYawPitchRoll(Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateFromYawPitchRoll(double yaw, double pitch, double roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateFromYawPitchRoll_" data-uid="VRageMath.MatrixD.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_MatrixD_CreateFromYawPitchRoll_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateFromYawPitchRoll(System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreateFromYawPitchRoll(Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Fills in a rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromYawPitchRoll(double yaw, double pitch, double roll, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing matrix filled in to represent the specified yaw, pitch, and roll.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateLookAt_" data-uid="VRageMath.MatrixD.CreateLookAt*"></a>
  <h4 id="VRageMath_MatrixD_CreateLookAt_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3_" data-uid="VRageMath.MatrixD.CreateLookAt(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3)">CreateLookAt(Vector3D, Vector3D, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateLookAt(Vector3D cameraPosition, Vector3D cameraTarget, Vector3 cameraUpVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateLookAt_" data-uid="VRageMath.MatrixD.CreateLookAt*"></a>
  <h4 id="VRageMath_MatrixD_CreateLookAt_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateLookAt(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">CreateLookAt(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a view matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateLookAt(Vector3D cameraPosition, Vector3D cameraTarget, Vector3D cameraUpVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>The position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td><p>The target towards which the camera is pointing.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The direction that is &quot;up&quot; from the camera's point of view.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateLookAt_" data-uid="VRageMath.MatrixD.CreateLookAt*"></a>
  <h4 id="VRageMath_MatrixD_CreateLookAt_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateLookAt(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.MatrixD@)">CreateLookAt(ref Vector3D, ref Vector3D, ref Vector3D, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a view matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateLookAt(ref Vector3D cameraPosition, ref Vector3D cameraTarget, ref Vector3D cameraUpVector, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>The position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td><p>The target towards which the camera is pointing.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The direction that is &quot;up&quot; from the camera's point of view.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created view matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateLookAtInverse_" data-uid="VRageMath.MatrixD.CreateLookAtInverse*"></a>
  <h4 id="VRageMath_MatrixD_CreateLookAtInverse_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateLookAtInverse(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">CreateLookAtInverse(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateLookAtInverse(Vector3D cameraPosition, Vector3D cameraTarget, Vector3D cameraUpVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateOrthographic_" data-uid="VRageMath.MatrixD.CreateOrthographic*"></a>
  <h4 id="VRageMath_MatrixD_CreateOrthographic_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreateOrthographic(System.Double,System.Double,System.Double,System.Double)">CreateOrthographic(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Builds an orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateOrthographic(double width, double height, double zNearPlane, double zFarPlane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateOrthographic_" data-uid="VRageMath.MatrixD.CreateOrthographic*"></a>
  <h4 id="VRageMath_MatrixD_CreateOrthographic_System_Double_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateOrthographic(System.Double,System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreateOrthographic(Double, Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Builds an orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateOrthographic(double width, double height, double zNearPlane, double zFarPlane, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateOrthographicOffCenter_" data-uid="VRageMath.MatrixD.CreateOrthographicOffCenter*"></a>
  <h4 id="VRageMath_MatrixD_CreateOrthographicOffCenter_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreateOrthographicOffCenter(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">CreateOrthographicOffCenter(Double, Double, Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateOrthographicOffCenter(double left, double right, double bottom, double top, double zNearPlane, double zFarPlane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateOrthographicOffCenter_" data-uid="VRageMath.MatrixD.CreateOrthographicOffCenter*"></a>
  <h4 id="VRageMath_MatrixD_CreateOrthographicOffCenter_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateOrthographicOffCenter(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreateOrthographicOffCenter(Double, Double, Double, Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateOrthographicOffCenter(double left, double right, double bottom, double top, double zNearPlane, double zFarPlane, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspective_" data-uid="VRageMath.MatrixD.CreatePerspective*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspective_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreatePerspective(System.Double,System.Double,System.Double,System.Double)">CreatePerspective(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix and returns the result by value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreatePerspective(double width, double height, double nearPlaneDistance, double farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspective_" data-uid="VRageMath.MatrixD.CreatePerspective*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspective_System_Double_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreatePerspective(System.Double,System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreatePerspective(Double, Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix and returns the result by reference.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspective(double width, double height, double nearPlaneDistance, double farPlaneDistance, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspectiveFieldOfView_" data-uid="VRageMath.MatrixD.CreatePerspectiveFieldOfView*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspectiveFieldOfView_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreatePerspectiveFieldOfView(System.Double,System.Double,System.Double,System.Double)">CreatePerspectiveFieldOfView(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix based on a field of view and returns by value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreatePerspectiveFieldOfView(double fieldOfView, double aspectRatio, double nearPlaneDistance, double farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td><p>Field of view in the y direction, in radians.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td><p>Aspect ratio, defined as view space width divided by height. To match the aspect ratio of the
viewport, the property AspectRatio.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspectiveFieldOfView_" data-uid="VRageMath.MatrixD.CreatePerspectiveFieldOfView*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspectiveFieldOfView_System_Double_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreatePerspectiveFieldOfView(System.Double,System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreatePerspectiveFieldOfView(Double, Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix based on a field of view and returns by reference.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspectiveFieldOfView(double fieldOfView, double aspectRatio, double nearPlaneDistance, double farPlaneDistance, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td><p>Field of view in the y direction, in radians.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td><p>Aspect ratio, defined as view space width divided by height. To match the aspect ratio of the
viewport, the property AspectRatio.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The perspective projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspectiveOffCenter_" data-uid="VRageMath.MatrixD.CreatePerspectiveOffCenter*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspectiveOffCenter_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreatePerspectiveOffCenter(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">CreatePerspectiveOffCenter(Double, Double, Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, perspective projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreatePerspectiveOffCenter(double left, double right, double bottom, double top, double nearPlaneDistance, double farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to of the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreatePerspectiveOffCenter_" data-uid="VRageMath.MatrixD.CreatePerspectiveOffCenter*"></a>
  <h4 id="VRageMath_MatrixD_CreatePerspectiveOffCenter_System_Double_System_Double_System_Double_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreatePerspectiveOffCenter(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreatePerspectiveOffCenter(Double, Double, Double, Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, perspective projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspectiveOffCenter(double left, double right, double bottom, double top, double nearPlaneDistance, double farPlaneDistance, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to of the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateReflection_" data-uid="VRageMath.MatrixD.CreateReflection*"></a>
  <h4 id="VRageMath_MatrixD_CreateReflection_VRageMath_Plane_" data-uid="VRageMath.MatrixD.CreateReflection(VRageMath.Plane)">CreateReflection(Plane)</h4>
  <div class="markdown level1 summary"><p>Creates a Matrix that reflects the coordinate system about a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateReflection(Plane value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane about which to create a reflection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateReflection_" data-uid="VRageMath.MatrixD.CreateReflection*"></a>
  <h4 id="VRageMath_MatrixD_CreateReflection_VRageMath_Plane__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateReflection(VRageMath.Plane@,VRageMath.MatrixD@)">CreateReflection(ref Plane, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Fills in an existing Matrix so that it reflects the coordinate system about a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateReflection(ref Plane value, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane about which to create a reflection.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A Matrix that creates the reflection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationX_" data-uid="VRageMath.MatrixD.CreateRotationX*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationX_System_Double_" data-uid="VRageMath.MatrixD.CreateRotationX(System.Double)">CreateRotationX(Double)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateRotationX(double radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationX_" data-uid="VRageMath.MatrixD.CreateRotationX*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationX_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateRotationX(System.Double,VRageMath.MatrixD@)">CreateRotationX(Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationX(double radians, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationY_" data-uid="VRageMath.MatrixD.CreateRotationY*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationY_System_Double_" data-uid="VRageMath.MatrixD.CreateRotationY(System.Double)">CreateRotationY(Double)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateRotationY(double radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationY_" data-uid="VRageMath.MatrixD.CreateRotationY*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationY_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateRotationY(System.Double,VRageMath.MatrixD@)">CreateRotationY(Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationY(double radians, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationZ_" data-uid="VRageMath.MatrixD.CreateRotationZ*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationZ_System_Double_" data-uid="VRageMath.MatrixD.CreateRotationZ(System.Double)">CreateRotationZ(Double)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateRotationZ(double radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateRotationZ_" data-uid="VRageMath.MatrixD.CreateRotationZ*"></a>
  <h4 id="VRageMath_MatrixD_CreateRotationZ_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateRotationZ(System.Double,VRageMath.MatrixD@)">CreateRotationZ(Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationZ(double radians, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to
convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The rotation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_System_Double_" data-uid="VRageMath.MatrixD.CreateScale(System.Double)">CreateScale(Double)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateScale(double scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Amount to scale by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreateScale(System.Double,System.Double,System.Double)">CreateScale(Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateScale(double xScale, double yScale, double zScale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateScale(System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreateScale(Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(double xScale, double yScale, double zScale, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateScale(System.Double,VRageMath.MatrixD@)">CreateScale(Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(double scale, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Value to scale by.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateScale(VRageMath.Vector3D)">CreateScale(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateScale(Vector3D scales)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateScale_" data-uid="VRageMath.MatrixD.CreateScale*"></a>
  <h4 id="VRageMath_MatrixD_CreateScale_VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateScale(VRageMath.Vector3D@,VRageMath.MatrixD@)">CreateScale(ref Vector3D, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(ref Vector3D scales, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateShadow_" data-uid="VRageMath.MatrixD.CreateShadow*"></a>
  <h4 id="VRageMath_MatrixD_CreateShadow_VRageMath_Vector3D_VRageMath_Plane_" data-uid="VRageMath.MatrixD.CreateShadow(VRageMath.Vector3D,VRageMath.Plane)">CreateShadow(Vector3D, Plane)</h4>
  <div class="markdown level1 summary"><p>Creates a Matrix that flattens geometry into a specified Plane as if casting a shadow from a specified light source.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateShadow(Vector3D lightDirection, Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">lightDirection</span></td>
        <td><p>A Vector3 specifying the direction from which the light that will cast the shadow is
coming.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane onto which the new matrix should flatten geometry so as to cast a shadow.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateShadow_" data-uid="VRageMath.MatrixD.CreateShadow*"></a>
  <h4 id="VRageMath_MatrixD_CreateShadow_VRageMath_Vector3D__VRageMath_Plane__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateShadow(VRageMath.Vector3D@,VRageMath.Plane@,VRageMath.MatrixD@)">CreateShadow(ref Vector3D, ref Plane, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Fills in a Matrix to flatten geometry into a specified Plane as if casting a shadow from a specified light source.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateShadow(ref Vector3D lightDirection, ref Plane plane, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">lightDirection</span></td>
        <td><p>A Vector3 specifying the direction from which the light that will cast the shadow is
coming.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane onto which the new matrix should flatten geometry so as to cast a shadow.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A Matrix that can be used to flatten geometry onto the specified plane from the
specified direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateTranslation_" data-uid="VRageMath.MatrixD.CreateTranslation*"></a>
  <h4 id="VRageMath_MatrixD_CreateTranslation_System_Double_System_Double_System_Double_" data-uid="VRageMath.MatrixD.CreateTranslation(System.Double,System.Double,System.Double)">CreateTranslation(Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateTranslation(double xPosition, double yPosition, double zPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">xPosition</span></td>
        <td><p>Value to translate by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yPosition</span></td>
        <td><p>Value to translate by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zPosition</span></td>
        <td><p>Value to translate by on the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateTranslation_" data-uid="VRageMath.MatrixD.CreateTranslation*"></a>
  <h4 id="VRageMath_MatrixD_CreateTranslation_System_Double_System_Double_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateTranslation(System.Double,System.Double,System.Double,VRageMath.MatrixD@)">CreateTranslation(Double, Double, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateTranslation(double xPosition, double yPosition, double zPosition, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">xPosition</span></td>
        <td><p>Value to translate by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yPosition</span></td>
        <td><p>Value to translate by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">zPosition</span></td>
        <td><p>Value to translate by on the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created translation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateTranslation_" data-uid="VRageMath.MatrixD.CreateTranslation*"></a>
  <h4 id="VRageMath_MatrixD_CreateTranslation_VRageMath_Vector3_" data-uid="VRageMath.MatrixD.CreateTranslation(VRageMath.Vector3)">CreateTranslation(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateTranslation(Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateTranslation_" data-uid="VRageMath.MatrixD.CreateTranslation*"></a>
  <h4 id="VRageMath_MatrixD_CreateTranslation_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateTranslation(VRageMath.Vector3D)">CreateTranslation(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateTranslation(Vector3D position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Amounts to translate by on the x, y, and z axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateTranslation_" data-uid="VRageMath.MatrixD.CreateTranslation*"></a>
  <h4 id="VRageMath_MatrixD_CreateTranslation_VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateTranslation(VRageMath.Vector3D@,VRageMath.MatrixD@)">CreateTranslation(ref Vector3D, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateTranslation(ref Vector3D position, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Amounts to translate by on the x, y, and z axes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created translation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateWorld_" data-uid="VRageMath.MatrixD.CreateWorld*"></a>
  <h4 id="VRageMath_MatrixD_CreateWorld_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateWorld(VRageMath.Vector3D)">CreateWorld(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateWorld(Vector3D position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateWorld_" data-uid="VRageMath.MatrixD.CreateWorld*"></a>
  <h4 id="VRageMath_MatrixD_CreateWorld_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.MatrixD.CreateWorld(VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3)">CreateWorld(Vector3D, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateWorld(Vector3D position, Vector3 forward, Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateWorld_" data-uid="VRageMath.MatrixD.CreateWorld*"></a>
  <h4 id="VRageMath_MatrixD_CreateWorld_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.CreateWorld(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">CreateWorld(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a world matrix with the specified parameters.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CreateWorld(Vector3D position, Vector3D forward, Vector3D up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position of the object. This value is used in translation operations.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">forward</span></td>
        <td><p>Forward direction of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">up</span></td>
        <td><p>Upward direction of the object; usually [0, 1, 0].</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_CreateWorld_" data-uid="VRageMath.MatrixD.CreateWorld*"></a>
  <h4 id="VRageMath_MatrixD_CreateWorld_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.CreateWorld(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.MatrixD@)">CreateWorld(ref Vector3D, ref Vector3D, ref Vector3D, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a world matrix with the specified parameters.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateWorld(ref Vector3D position, ref Vector3D forward, ref Vector3D up, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position of the object. This value is used in translation operations.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">forward</span></td>
        <td><p>Forward direction of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">up</span></td>
        <td><p>Upward direction of the object; usually [0, 1, 0].</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created world matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Determinant_" data-uid="VRageMath.MatrixD.Determinant*"></a>
  <h4 id="VRageMath_MatrixD_Determinant" data-uid="VRageMath.MatrixD.Determinant">Determinant()</h4>
  <div class="markdown level1 summary"><p>Calculates the determinant of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Determinant()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Divide_" data-uid="VRageMath.MatrixD.Divide*"></a>
  <h4 id="VRageMath_MatrixD_Divide_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.Divide(VRageMath.MatrixD,System.Double)">Divide(MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Divide(MatrixD matrix1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Divide_" data-uid="VRageMath.MatrixD.Divide*"></a>
  <h4 id="VRageMath_MatrixD_Divide_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Divide(VRageMath.MatrixD,VRageMath.MatrixD)">Divide(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Divide(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Divide_" data-uid="VRageMath.MatrixD.Divide*"></a>
  <h4 id="VRageMath_MatrixD_Divide_VRageMath_MatrixD__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Divide(VRageMath.MatrixD@,System.Double,VRageMath.MatrixD@)">Divide(ref MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref MatrixD matrix1, double divider, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Divide_" data-uid="VRageMath.MatrixD.Divide*"></a>
  <h4 id="VRageMath_MatrixD_Divide_VRageMath_MatrixD__VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Divide(VRageMath.MatrixD@,VRageMath.MatrixD@,VRageMath.MatrixD@)">Divide(ref MatrixD, ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref MatrixD matrix1, ref MatrixD matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Equals_" data-uid="VRageMath.MatrixD.Equals*"></a>
  <h4 id="VRageMath_MatrixD_Equals_System_Object_" data-uid="VRageMath.MatrixD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Equals_" data-uid="VRageMath.MatrixD.Equals*"></a>
  <h4 id="VRageMath_MatrixD_Equals_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Equals(VRageMath.MatrixD)">Equals(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(MatrixD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_EqualsFast_" data-uid="VRageMath.MatrixD.EqualsFast*"></a>
  <h4 id="VRageMath_MatrixD_EqualsFast_VRageMath_MatrixD__System_Double_" data-uid="VRageMath.MatrixD.EqualsFast(VRageMath.MatrixD@,System.Double)">EqualsFast(ref MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Compares just position, forward and up</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool EqualsFast(ref MatrixD other, double epsilon = 0.0001)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetClosestDirection_" data-uid="VRageMath.MatrixD.GetClosestDirection*"></a>
  <h4 id="VRageMath_MatrixD_GetClosestDirection_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.GetClosestDirection(VRageMath.Vector3D)">GetClosestDirection(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(Vector3D referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetClosestDirection_" data-uid="VRageMath.MatrixD.GetClosestDirection*"></a>
  <h4 id="VRageMath_MatrixD_GetClosestDirection_VRageMath_Vector3D__" data-uid="VRageMath.MatrixD.GetClosestDirection(VRageMath.Vector3D@)">GetClosestDirection(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(ref Vector3D referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetDirectionVector_" data-uid="VRageMath.MatrixD.GetDirectionVector*"></a>
  <h4 id="VRageMath_MatrixD_GetDirectionVector_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixD.GetDirectionVector(VRageMath.Base6Directions.Direction)">GetDirectionVector(Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"><p>Gets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D GetDirectionVector(Base6Directions.Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetEulerAnglesXYZ_" data-uid="VRageMath.MatrixD.GetEulerAnglesXYZ*"></a>
  <h4 id="VRageMath_MatrixD_GetEulerAnglesXYZ_VRageMath_MatrixD__VRageMath_Vector3D__" data-uid="VRageMath.MatrixD.GetEulerAnglesXYZ(VRageMath.MatrixD@,VRageMath.Vector3D@)">GetEulerAnglesXYZ(ref MatrixD, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetEulerAnglesXYZ(ref MatrixD mat, out Vector3D xyz)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">mat</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">xyz</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetHashCode_" data-uid="VRageMath.MatrixD.GetHashCode*"></a>
  <h4 id="VRageMath_MatrixD_GetHashCode" data-uid="VRageMath.MatrixD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetOrientation_" data-uid="VRageMath.MatrixD.GetOrientation*"></a>
  <h4 id="VRageMath_MatrixD_GetOrientation" data-uid="VRageMath.MatrixD.GetOrientation">GetOrientation()</h4>
  <div class="markdown level1 summary"><p>Gets the orientation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD GetOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_GetRow_" data-uid="VRageMath.MatrixD.GetRow*"></a>
  <h4 id="VRageMath_MatrixD_GetRow_System_Int32_" data-uid="VRageMath.MatrixD.GetRow(System.Int32)">GetRow(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D GetRow(int row)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_HasNoTranslationOrPerspective_" data-uid="VRageMath.MatrixD.HasNoTranslationOrPerspective*"></a>
  <h4 id="VRageMath_MatrixD_HasNoTranslationOrPerspective" data-uid="VRageMath.MatrixD.HasNoTranslationOrPerspective">HasNoTranslationOrPerspective()</h4>
  <div class="markdown level1 summary"><p>Returns true if this matrix represents invertible (you can call Invert on it) linear (it does not contain translation
or perspective transformation) transformation.
Such matrix consist solely of rotations, shearing, mirroring and scaling. It can be orthogonalized to create an
orthogonal rotation matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasNoTranslationOrPerspective()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Invert_" data-uid="VRageMath.MatrixD.Invert*"></a>
  <h4 id="VRageMath_MatrixD_Invert_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Invert(VRageMath.MatrixD)">Invert(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Calculates the inverse of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Invert(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Invert_" data-uid="VRageMath.MatrixD.Invert*"></a>
  <h4 id="VRageMath_MatrixD_Invert_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Invert(VRageMath.MatrixD@)">Invert(ref MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Invert(ref MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Invert_" data-uid="VRageMath.MatrixD.Invert*"></a>
  <h4 id="VRageMath_MatrixD_Invert_VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Invert(VRageMath.MatrixD@,VRageMath.MatrixD@)">Invert(ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Calculates the inverse of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Invert(ref MatrixD matrix, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The inverse of matrix. The same matrix can be used for both arguments.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsMirrored_" data-uid="VRageMath.MatrixD.IsMirrored*"></a>
  <h4 id="VRageMath_MatrixD_IsMirrored" data-uid="VRageMath.MatrixD.IsMirrored">IsMirrored()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsMirrored()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsNan_" data-uid="VRageMath.MatrixD.IsNan*"></a>
  <h4 id="VRageMath_MatrixD_IsNan" data-uid="VRageMath.MatrixD.IsNan">IsNan()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsNan()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsOrthogonal_" data-uid="VRageMath.MatrixD.IsOrthogonal*"></a>
  <h4 id="VRageMath_MatrixD_IsOrthogonal" data-uid="VRageMath.MatrixD.IsOrthogonal">IsOrthogonal()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsOrthogonal()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsOrthogonal_" data-uid="VRageMath.MatrixD.IsOrthogonal*"></a>
  <h4 id="VRageMath_MatrixD_IsOrthogonal_System_Double_" data-uid="VRageMath.MatrixD.IsOrthogonal(System.Double)">IsOrthogonal(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsOrthogonal(double epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsRotation_" data-uid="VRageMath.MatrixD.IsRotation*"></a>
  <h4 id="VRageMath_MatrixD_IsRotation" data-uid="VRageMath.MatrixD.IsRotation">IsRotation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRotation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_IsValid_" data-uid="VRageMath.MatrixD.IsValid*"></a>
  <h4 id="VRageMath_MatrixD_IsValid" data-uid="VRageMath.MatrixD.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Lerp_" data-uid="VRageMath.MatrixD.Lerp*"></a>
  <h4 id="VRageMath_MatrixD_Lerp_VRageMath_MatrixD_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.Lerp(VRageMath.MatrixD,VRageMath.MatrixD,System.Double)">Lerp(MatrixD, MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between the corresponding values of two matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Lerp(MatrixD matrix1, MatrixD matrix2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Lerp_" data-uid="VRageMath.MatrixD.Lerp*"></a>
  <h4 id="VRageMath_MatrixD_Lerp_VRageMath_MatrixD__VRageMath_MatrixD__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Lerp(VRageMath.MatrixD@,VRageMath.MatrixD@,System.Double,VRageMath.MatrixD@)">Lerp(ref MatrixD, ref MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between the corresponding values of two matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref MatrixD matrix1, ref MatrixD matrix2, double amount, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_Matrix__VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Multiply(VRageMath.Matrix@,VRageMath.MatrixD@,VRageMath.MatrixD@)">Multiply(ref Matrix, ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Matrix matrix1, ref MatrixD matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD,System.Double)">Multiply(MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Multiply(MatrixD matrix1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD_VRageMath_Matrix_" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD,VRageMath.Matrix)">Multiply(MatrixD, Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Multiply(MatrixD matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD,VRageMath.MatrixD)">Multiply(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Multiply(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD@,System.Double,VRageMath.MatrixD@)">Multiply(ref MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref MatrixD matrix1, double scaleFactor, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD__VRageMath_Matrix__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD@,VRageMath.Matrix@,VRageMath.MatrixD@)">Multiply(ref MatrixD, ref Matrix, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref MatrixD matrix1, ref Matrix matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Multiply_" data-uid="VRageMath.MatrixD.Multiply*"></a>
  <h4 id="VRageMath_MatrixD_Multiply_VRageMath_MatrixD__VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Multiply(VRageMath.MatrixD@,VRageMath.MatrixD@,VRageMath.MatrixD@)">Multiply(ref MatrixD, ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref MatrixD matrix1, ref MatrixD matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Negate_" data-uid="VRageMath.MatrixD.Negate*"></a>
  <h4 id="VRageMath_MatrixD_Negate_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Negate(VRageMath.MatrixD)">Negate(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Negate(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Negate_" data-uid="VRageMath.MatrixD.Negate*"></a>
  <h4 id="VRageMath_MatrixD_Negate_VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Negate(VRageMath.MatrixD@,VRageMath.MatrixD@)">Negate(ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref MatrixD matrix, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Negated matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Normalize_" data-uid="VRageMath.MatrixD.Normalize*"></a>
  <h4 id="VRageMath_MatrixD_Normalize_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Normalize(VRageMath.MatrixD)">Normalize(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Normalize(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Orthogonalize_" data-uid="VRageMath.MatrixD.Orthogonalize*"></a>
  <h4 id="VRageMath_MatrixD_Orthogonalize" data-uid="VRageMath.MatrixD.Orthogonalize">Orthogonalize()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Orthogonalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_MatrixD_Orthogonalize_" data-uid="VRageMath.MatrixD.Orthogonalize*"></a>
  <h4 id="VRageMath_MatrixD_Orthogonalize_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Orthogonalize(VRageMath.MatrixD)">Orthogonalize(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Orthogonalize(MatrixD rotationMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">rotationMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rescale_" data-uid="VRageMath.MatrixD.Rescale*"></a>
  <h4 id="VRageMath_MatrixD_Rescale_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.Rescale(VRageMath.MatrixD,System.Double)">Rescale(MatrixD, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Rescale(MatrixD matrix, double scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rescale_" data-uid="VRageMath.MatrixD.Rescale*"></a>
  <h4 id="VRageMath_MatrixD_Rescale_VRageMath_MatrixD_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.Rescale(VRageMath.MatrixD,VRageMath.Vector3D)">Rescale(MatrixD, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Rescale(MatrixD matrix, Vector3D scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rescale_" data-uid="VRageMath.MatrixD.Rescale*"></a>
  <h4 id="VRageMath_MatrixD_Rescale_VRageMath_MatrixD__System_Double_" data-uid="VRageMath.MatrixD.Rescale(VRageMath.MatrixD@,System.Double)">Rescale(ref MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref MatrixD matrix, double scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rescale_" data-uid="VRageMath.MatrixD.Rescale*"></a>
  <h4 id="VRageMath_MatrixD_Rescale_VRageMath_MatrixD__System_Single_" data-uid="VRageMath.MatrixD.Rescale(VRageMath.MatrixD@,System.Single)">Rescale(ref MatrixD, Single)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref MatrixD matrix, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Rescale_" data-uid="VRageMath.MatrixD.Rescale*"></a>
  <h4 id="VRageMath_MatrixD_Rescale_VRageMath_MatrixD__VRageMath_Vector3D__" data-uid="VRageMath.MatrixD.Rescale(VRageMath.MatrixD@,VRageMath.Vector3D@)">Rescale(ref MatrixD, ref Vector3D)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref MatrixD matrix, ref Vector3D scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SetDirectionVector_" data-uid="VRageMath.MatrixD.SetDirectionVector*"></a>
  <h4 id="VRageMath_MatrixD_SetDirectionVector_VRageMath_Base6Directions_Direction_VRageMath_Vector3D_" data-uid="VRageMath.MatrixD.SetDirectionVector(VRageMath.Base6Directions.Direction,VRageMath.Vector3D)">SetDirectionVector(Base6Directions.Direction, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Sets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDirectionVector(Base6Directions.Direction direction, Vector3D newValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">newValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SetFrom_" data-uid="VRageMath.MatrixD.SetFrom*"></a>
  <h4 id="VRageMath_MatrixD_SetFrom_VRageMath_Matrix__" data-uid="VRageMath.MatrixD.SetFrom(VRageMath.Matrix@)">SetFrom(in Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetFrom(in Matrix m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SetRotationAndScale_" data-uid="VRageMath.MatrixD.SetRotationAndScale*"></a>
  <h4 id="VRageMath_MatrixD_SetRotationAndScale_VRageMath_Matrix__" data-uid="VRageMath.MatrixD.SetRotationAndScale(VRageMath.Matrix@)">SetRotationAndScale(in Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRotationAndScale(in Matrix m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SetRow_" data-uid="VRageMath.MatrixD.SetRow*"></a>
  <h4 id="VRageMath_MatrixD_SetRow_System_Int32_VRageMath_Vector4_" data-uid="VRageMath.MatrixD.SetRow(System.Int32,VRageMath.Vector4)">SetRow(Int32, Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRow(int row, Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Slerp_" data-uid="VRageMath.MatrixD.Slerp*"></a>
  <h4 id="VRageMath_MatrixD_Slerp_VRageMath_MatrixD_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.Slerp(VRageMath.MatrixD,VRageMath.MatrixD,System.Double)">Slerp(MatrixD, MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Slerp(MatrixD matrix1, MatrixD matrix2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Slerp_" data-uid="VRageMath.MatrixD.Slerp*"></a>
  <h4 id="VRageMath_MatrixD_Slerp_VRageMath_MatrixD__VRageMath_MatrixD__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Slerp(VRageMath.MatrixD@,VRageMath.MatrixD@,System.Double,VRageMath.MatrixD@)">Slerp(in MatrixD, in MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(in MatrixD matrix1, in MatrixD matrix2, double amount, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SlerpScale_" data-uid="VRageMath.MatrixD.SlerpScale*"></a>
  <h4 id="VRageMath_MatrixD_SlerpScale_VRageMath_MatrixD_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.SlerpScale(VRageMath.MatrixD,VRageMath.MatrixD,System.Double)">SlerpScale(MatrixD, MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD SlerpScale(MatrixD matrix1, MatrixD matrix2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SlerpScale_" data-uid="VRageMath.MatrixD.SlerpScale*"></a>
  <h4 id="VRageMath_MatrixD_SlerpScale_VRageMath_MatrixD_VRageMath_MatrixD_System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.SlerpScale(VRageMath.MatrixD,VRageMath.MatrixD,System.Double,VRageMath.MatrixD@)">SlerpScale(MatrixD, MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SlerpScale(MatrixD matrix1, MatrixD matrix2, double amount, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SlerpScale_" data-uid="VRageMath.MatrixD.SlerpScale*"></a>
  <h4 id="VRageMath_MatrixD_SlerpScale_VRageMath_MatrixD__VRageMath_MatrixD__System_Double_VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.SlerpScale(VRageMath.MatrixD@,VRageMath.MatrixD@,System.Double,VRageMath.MatrixD@)">SlerpScale(ref MatrixD, ref MatrixD, Double, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SlerpScale(ref MatrixD matrix1, ref MatrixD matrix2, double amount, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Subtract_" data-uid="VRageMath.MatrixD.Subtract*"></a>
  <h4 id="VRageMath_MatrixD_Subtract_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.MatrixD.Subtract(VRageMath.Matrix,VRageMath.Matrix)">Subtract(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Subtract(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Subtract_" data-uid="VRageMath.MatrixD.Subtract*"></a>
  <h4 id="VRageMath_MatrixD_Subtract_VRageMath_MatrixD__VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Subtract(VRageMath.MatrixD@,VRageMath.MatrixD@,VRageMath.MatrixD@)">Subtract(ref MatrixD, ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref MatrixD matrix1, ref MatrixD matrix2, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_SwapYZCoordinates_" data-uid="VRageMath.MatrixD.SwapYZCoordinates*"></a>
  <h4 id="VRageMath_MatrixD_SwapYZCoordinates_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.SwapYZCoordinates(VRageMath.MatrixD)">SwapYZCoordinates(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD SwapYZCoordinates(MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_ToString_" data-uid="VRageMath.MatrixD.ToString*"></a>
  <h4 id="VRageMath_MatrixD_ToString" data-uid="VRageMath.MatrixD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Transform_" data-uid="VRageMath.MatrixD.Transform*"></a>
  <h4 id="VRageMath_MatrixD_Transform_VRageMath_MatrixD_VRageMath_Quaternion_" data-uid="VRageMath.MatrixD.Transform(VRageMath.MatrixD,VRageMath.Quaternion)">Transform(MatrixD, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Matrix by applying a Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Transform(MatrixD value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Matrix to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply, expressed as a Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Transform_" data-uid="VRageMath.MatrixD.Transform*"></a>
  <h4 id="VRageMath_MatrixD_Transform_VRageMath_MatrixD__VRageMath_Quaternion__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Transform(VRageMath.MatrixD@,VRageMath.Quaternion@,VRageMath.MatrixD@)">Transform(ref MatrixD, ref Quaternion, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a Matrix by applying a Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref MatrixD value, ref Quaternion rotation, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Matrix to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply, expressed as a Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Matrix filled in with the result of the transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Transpose_" data-uid="VRageMath.MatrixD.Transpose*"></a>
  <h4 id="VRageMath_MatrixD_Transpose_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.Transpose(VRageMath.MatrixD)">Transpose(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Transpose(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_Transpose_" data-uid="VRageMath.MatrixD.Transpose*"></a>
  <h4 id="VRageMath_MatrixD_Transpose_VRageMath_MatrixD__VRageMath_MatrixD__" data-uid="VRageMath.MatrixD.Transpose(VRageMath.MatrixD@,VRageMath.MatrixD@)">Transpose(ref MatrixD, out MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transpose(ref MatrixD matrix, out MatrixD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Transposed matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_MatrixD_op_Addition_" data-uid="VRageMath.MatrixD.op_Addition*"></a>
  <h4 id="VRageMath_MatrixD_op_Addition_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Addition(VRageMath.MatrixD,VRageMath.MatrixD)">Addition(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator +(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Division_" data-uid="VRageMath.MatrixD.op_Division*"></a>
  <h4 id="VRageMath_MatrixD_op_Division_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.op_Division(VRageMath.MatrixD,System.Double)">Division(MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator /(MatrixD matrix1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Division_" data-uid="VRageMath.MatrixD.op_Division*"></a>
  <h4 id="VRageMath_MatrixD_op_Division_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Division(VRageMath.MatrixD,VRageMath.MatrixD)">Division(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator /(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Equality_" data-uid="VRageMath.MatrixD.op_Equality*"></a>
  <h4 id="VRageMath_MatrixD_op_Equality_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Equality(VRageMath.MatrixD,VRageMath.MatrixD)">Equality(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Compares a matrix for equality with another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Implicit_" data-uid="VRageMath.MatrixD.op_Implicit*"></a>
  <h4 id="VRageMath_MatrixD_op_Implicit_VRageMath_Matrix___VRageMath_MatrixD" data-uid="VRageMath.MatrixD.op_Implicit(VRageMath.Matrix@)~VRageMath.MatrixD">Implicit(in Matrix to MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator MatrixD(in Matrix m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Implicit_" data-uid="VRageMath.MatrixD.op_Implicit*"></a>
  <h4 id="VRageMath_MatrixD_op_Implicit_VRageMath_MatrixD___VRageMath_Matrix" data-uid="VRageMath.MatrixD.op_Implicit(VRageMath.MatrixD@)~VRageMath.Matrix">Implicit(in MatrixD to Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Matrix(in MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Inequality_" data-uid="VRageMath.MatrixD.op_Inequality*"></a>
  <h4 id="VRageMath_MatrixD_op_Inequality_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Inequality(VRageMath.MatrixD,VRageMath.MatrixD)">Inequality(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Tests a matrix for inequality with another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>The matrix on the left of the equal sign.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The matrix on the right of the equal sign.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Multiply_" data-uid="VRageMath.MatrixD.op_Multiply*"></a>
  <h4 id="VRageMath_MatrixD_op_Multiply_System_Double_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Multiply(System.Double,VRageMath.MatrixD)">Multiply(Double, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator *(double scaleFactor, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Multiply_" data-uid="VRageMath.MatrixD.op_Multiply*"></a>
  <h4 id="VRageMath_MatrixD_op_Multiply_VRageMath_Matrix_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Multiply(VRageMath.Matrix,VRageMath.MatrixD)">Multiply(Matrix, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator *(Matrix matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Multiply_" data-uid="VRageMath.MatrixD.op_Multiply*"></a>
  <h4 id="VRageMath_MatrixD_op_Multiply_VRageMath_MatrixD_System_Double_" data-uid="VRageMath.MatrixD.op_Multiply(VRageMath.MatrixD,System.Double)">Multiply(MatrixD, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator *(MatrixD matrix, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Multiply_" data-uid="VRageMath.MatrixD.op_Multiply*"></a>
  <h4 id="VRageMath_MatrixD_op_Multiply_VRageMath_MatrixD_VRageMath_Matrix_" data-uid="VRageMath.MatrixD.op_Multiply(VRageMath.MatrixD,VRageMath.Matrix)">Multiply(MatrixD, Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator *(MatrixD matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Multiply_" data-uid="VRageMath.MatrixD.op_Multiply*"></a>
  <h4 id="VRageMath_MatrixD_op_Multiply_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Multiply(VRageMath.MatrixD,VRageMath.MatrixD)">Multiply(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator *(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_Subtraction_" data-uid="VRageMath.MatrixD.op_Subtraction*"></a>
  <h4 id="VRageMath_MatrixD_op_Subtraction_VRageMath_MatrixD_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_Subtraction(VRageMath.MatrixD,VRageMath.MatrixD)">Subtraction(MatrixD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator -(MatrixD matrix1, MatrixD matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixD_op_UnaryNegation_" data-uid="VRageMath.MatrixD.op_UnaryNegation*"></a>
  <h4 id="VRageMath_MatrixD_op_UnaryNegation_VRageMath_MatrixD_" data-uid="VRageMath.MatrixD.op_UnaryNegation(VRageMath.MatrixD)">UnaryNegation(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD operator -(MatrixD matrix1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
