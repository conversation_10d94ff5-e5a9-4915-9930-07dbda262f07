﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyIterableComplementSetExtensions
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyIterableComplementSetExtensions
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions">
  
  
  <h1 id="VRage_Library_Utils_MyIterableComplementSetExtensions" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions" class="text-break">Class MyIterableComplementSetExtensions
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyIterableComplementSetExtensions</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Library.Utils.html">VRage.Library.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.Library.dll</h6>
  <h5 id="VRage_Library_Utils_MyIterableComplementSetExtensions_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class MyIterableComplementSetExtensions : Object</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnComplement_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.AddOrEnsureOnComplement*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnComplement__1_VRage_Library_Utils_MyIterableComplementSet___0____0_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.AddOrEnsureOnComplement``1(VRage.Library.Utils.MyIterableComplementSet{``0},``0)">AddOrEnsureOnComplement&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddOrEnsureOnComplement&lt;T&gt;(this MyIterableComplementSet&lt;T&gt; self, T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet</a>&lt;T&gt;</td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnSet_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.AddOrEnsureOnSet*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSetExtensions_AddOrEnsureOnSet__1_VRage_Library_Utils_MyIterableComplementSet___0____0_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.AddOrEnsureOnSet``1(VRage.Library.Utils.MyIterableComplementSet{``0},``0)">AddOrEnsureOnSet&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddOrEnsureOnSet&lt;T&gt;(this MyIterableComplementSet&lt;T&gt; self, T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet</a>&lt;T&gt;</td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnComplementIfContained_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.EnsureOnComplementIfContained*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnComplementIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.EnsureOnComplementIfContained``1(VRage.Library.Utils.MyIterableComplementSet{``0},``0)">EnsureOnComplementIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void EnsureOnComplementIfContained&lt;T&gt;(this MyIterableComplementSet&lt;T&gt; self, T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet</a>&lt;T&gt;</td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnSetIfContained_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.EnsureOnSetIfContained*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSetExtensions_EnsureOnSetIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.EnsureOnSetIfContained``1(VRage.Library.Utils.MyIterableComplementSet{``0},``0)">EnsureOnSetIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void EnsureOnSetIfContained&lt;T&gt;(this MyIterableComplementSet&lt;T&gt; self, T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet</a>&lt;T&gt;</td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyIterableComplementSetExtensions_RemoveIfContained_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.RemoveIfContained*"></a>
  <h4 id="VRage_Library_Utils_MyIterableComplementSetExtensions_RemoveIfContained__1_VRage_Library_Utils_MyIterableComplementSet___0____0_" data-uid="VRage.Library.Utils.MyIterableComplementSetExtensions.RemoveIfContained``1(VRage.Library.Utils.MyIterableComplementSet{``0},``0)">RemoveIfContained&lt;T&gt;(MyIterableComplementSet&lt;T&gt;, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RemoveIfContained&lt;T&gt;(this MyIterableComplementSet&lt;T&gt; self, T item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyIterableComplementSet-1.html">MyIterableComplementSet</a>&lt;T&gt;</td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">item</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
