﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.Utils
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.Utils
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils">
  
  <h1 id="VRage_Utils" data-uid="VRage.Utils" class="text-break">Namespace VRage.Utils
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.Utils.ConcurrentNormalAggregator.html">ConcurrentNormalAggregator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.LoggingOptions.html">LoggingOptions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.My5BitEncoding.html">My5BitEncoding</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyAtlasTextureCoordinate.html">MyAtlasTextureCoordinate</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyAverageFiltering.html">MyAverageFiltering</a></h4>
      <section><p>Mean (average) filtering.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyBBSetSampler.html">MyBBSetSampler</a></h4>
      <section><p>This class allows for uniform generation of points from a set of bounding boxes.</p>
<p>You start by constructing a bounding box from where the points will be sampled.
Then you can incrementally subtract bounding boxes and the resulting structure will allow you
to generate uniformly distributed points using the Sample() function.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyBinaryReader.html">MyBinaryReader</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyBuildNumbers.html">MyBuildNumbers</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyClipboardHelper.html">MyClipboardHelper</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyColorAlias.html">MyColorAlias</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyConsolePipeWriter.html">MyConsolePipeWriter</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyCriticalSection_Mutex.html">MyCriticalSection_Mutex</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyCriticalSection_SpinLock.html">MyCriticalSection_SpinLock</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDebugHitCounter.html">MyDebugHitCounter</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDebugHitCounter.Sample.html">MyDebugHitCounter.Sample</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDebugWorkTracker-1.html">MyDebugWorkTracker&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDebugWorkTrackerExtensions.html">MyDebugWorkTrackerExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDiscreteSampler.html">MyDiscreteSampler</a></h4>
      <section><p>Provides a simple and efficient way of sampling a discrete probability distribution as described in <a href="http://www.jstatsoft.org/v11/i03/paper">http://www.jstatsoft.org/v11/i03/paper</a>
Instances can be reused by calling the Prepare method every time you want to change the distribution.
Sampling a value is O(1), while the storage requirements are O(N), where N is number of possible values</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyDiscreteSampler.SamplingBin.html">MyDiscreteSampler.SamplingBin</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyDiscreteSampler-1.html">MyDiscreteSampler&lt;T&gt;</a></h4>
      <section><p>A templated class for sampling from a set of objects with given probabilities. Uses MyDiscreteSampler.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyEncryptionSymmetricRijndael.html">MyEncryptionSymmetricRijndael</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyEnumDuplicitiesTester.html">MyEnumDuplicitiesTester</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyEventArgs.html">MyEventArgs</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyEventSet.html">MyEventSet</a></h4>
      <section><p>From <a href="http://www.wintellect.com/Resources">http://www.wintellect.com/Resources</a> CLR Via C# by Jeffrey Richter</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyFlagEnumAttribute.html">MyFlagEnumAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyGuiDrawAlignEnum.html">MyGuiDrawAlignEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyLimitedDefaultLogPrinter.html">MyLimitedDefaultLogPrinter</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyLog.html">MyLog</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyLog.IndentToken.html">MyLog.IndentToken</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyLogExtensions.html">MyLogExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyLogSeverity.html">MyLogSeverity</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyMathConstants.html">MyMathConstants</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyMaxFiltering.html">MyMaxFiltering</a></h4>
      <section><p>Nonlinear maximum filtering.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyMemoryLogs.html">MyMemoryLogs</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyMemoryLogs.MyMemoryEvent.html">MyMemoryLogs.MyMemoryEvent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyMergeHelper.html">MyMergeHelper</a></h4>
      <section><p>Helper class for merge funcionality. Performs comparison between
source and other values and set on self if value is different</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyMessageBox.html">MyMessageBox</a></h4>
      <section><p>Custom message box</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyMinFiltering.html">MyMinFiltering</a></h4>
      <section><p>Nonlinear minimum filtering.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyMwcConstants.html">MyMwcConstants</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyPlane.html">MyPlane</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyPolyLine.html">MyPolyLine</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyPolyLineD.html">MyPolyLineD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyRectangle2D.html">MyRectangle2D</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MySectorConstants.html">MySectorConstants</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MySingleCrypto.html">MySingleCrypto</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MySpherePlaneIntersectionEnum.html">MySpherePlaneIntersectionEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyStringHash.html">MyStringHash</a></h4>
      <section><p>Generates string hashes deterministically and crashes on collisions. When used as key for hash tables (Dictionary or HashSet)
always pass in MyStringHash.Comparer, otherwise lookups will allocate memory! Can be safely used in network but never serialize to disk!</p>
<p>IDs are computed as hash from string so there is a risk of collisions. Use only when MyStringId is
not sufficient (eg. sending over network). Because the odds of collision get higher the more hashes are in use, do not use this for
generated strings and make sure hashes are computed deterministically (eg. at startup) and don't require lengthy gameplay. This way
we know about any collision early and not from rare and random crash reports.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyStringHash.HashComparerType.html">MyStringHash.HashComparerType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></h4>
      <section><p>Generates unique IDs for strings. When used as key for hash tables (Dictionary or HashSet)
always pass in MyStringId.Comparer, otherwise lookups will allocate memory! Never serialize to network or disk!</p>
<p>IDs are created sequentially as they get requested so two IDs might be different between sessions or clients and
server. You can safely use ToString() as it will not allocate.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyStringId.IdComparerType.html">MyStringId.IdComparerType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyStringUtils.html">MyStringUtils</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyTickTimedItem-1.html">MyTickTimedItem&lt;T&gt;</a></h4>
      <section><p>Item that is accessible only for defined amount of time ticks.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyTickTimedItemF.html">MyTickTimedItemF</a></h4>
      <section><p>Item that is accessible only for defined amount of time ticks.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyTimedItem-1.html">MyTimedItem&lt;T&gt;</a></h4>
      <section><p>Item that is accessible only for defined time span.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyTimedItemCache.html">MyTimedItemCache</a></h4>
      <section><p>Temporaly stores information about item (event/place) existence. This is useful if you want to launch some actions only from time to time.
You ask timed cache whether your last event still takes effect.</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyTriangle_Vertex_Normal.html">MyTriangle_Vertex_Normal</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyTriangle_Vertex_Normals.html">MyTriangle_Vertex_Normals</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyTriangle_Vertex_Normals_Tangents.html">MyTriangle_Vertex_Normals_Tangents</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyUtils.html">MyUtils</a></h4>
      <section><p>MyFileSystemUtils</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyUtils.ClearCollectionToken-2.html">MyUtils.ClearCollectionToken&lt;TCollection, TElement&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyUtils.ClearRangeToken-1.html">MyUtils.ClearRangeToken&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyUtils.ClearRangeToken-1.OffsetEnumerator.html">MyUtils.ClearRangeToken&lt;T&gt;.OffsetEnumerator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyValidationConstants.html">MyValidationConstants</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyValueFormatter.html">MyValueFormatter</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyVector3ISet.html">MyVector3ISet</a></h4>
      <section><p>A data structure for a set of Vector3I coordinates optimized for sets with high spatial coherence (hence the name)</p>
</section>
      <h4><a class="xref" href="VRage.Utils.MyVector3ISet.Enumerator.html">MyVector3ISet.Enumerator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.MyVersion.html">MyVersion</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Utils.NormalAggregator.html">NormalAggregator</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
