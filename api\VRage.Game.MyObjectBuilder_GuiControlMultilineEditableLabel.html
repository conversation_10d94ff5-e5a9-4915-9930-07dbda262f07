﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_GuiControlMultilineEditableLabel
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_GuiControlMultilineEditableLabel
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_GuiControlMultilineEditableLabel">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_GuiControlMultilineEditableLabel" data-uid="VRage.Game.MyObjectBuilder_GuiControlMultilineEditableLabel" class="text-break">Class MyObjectBuilder_GuiControlMultilineEditableLabel
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html">MyObjectBuilder_GuiControlBase</a></div>
    <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html">MyObjectBuilder_GuiControlMultilineLabel</a></div>
    <div class="level4"><span class="xref">MyObjectBuilder_GuiControlMultilineEditableLabel</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_TextScale">MyObjectBuilder_GuiControlMultilineLabel.TextScale</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_TextAlign">MyObjectBuilder_GuiControlMultilineLabel.TextAlign</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_TextColor">MyObjectBuilder_GuiControlMultilineLabel.TextColor</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_Text">MyObjectBuilder_GuiControlMultilineLabel.Text</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_TextBoxAlign">MyObjectBuilder_GuiControlMultilineLabel.TextBoxAlign</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlMultilineLabel.html#VRage_Game_MyObjectBuilder_GuiControlMultilineLabel_Font">MyObjectBuilder_GuiControlMultilineLabel.Font</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_Position">MyObjectBuilder_GuiControlBase.Position</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_Size">MyObjectBuilder_GuiControlBase.Size</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_Name">MyObjectBuilder_GuiControlBase.Name</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_BackgroundColor">MyObjectBuilder_GuiControlBase.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_ControlTexture">MyObjectBuilder_GuiControlBase.ControlTexture</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_OriginAlign">MyObjectBuilder_GuiControlBase.OriginAlign</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_ShouldSerializeControlAlign">MyObjectBuilder_GuiControlBase.ShouldSerializeControlAlign()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html#VRage_Game_MyObjectBuilder_GuiControlBase_ControlAlign">MyObjectBuilder_GuiControlBase.ControlAlign</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_GuiControlMultilineEditableLabel_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_GuiControlMultilineEditableLabel : MyObjectBuilder_GuiControlMultilineLabel</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_GuiControlMultilineEditableLabel__ctor_" data-uid="VRage.Game.MyObjectBuilder_GuiControlMultilineEditableLabel.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_GuiControlMultilineEditableLabel__ctor" data-uid="VRage.Game.MyObjectBuilder_GuiControlMultilineEditableLabel.#ctor">MyObjectBuilder_GuiControlMultilineEditableLabel()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_GuiControlMultilineEditableLabel()</code></pre>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
