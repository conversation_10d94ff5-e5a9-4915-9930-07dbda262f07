﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyVoxelCoordSystems
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyVoxelCoordSystems
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Voxels.MyVoxelCoordSystems">
  
  
  <h1 id="VRage_Voxels_MyVoxelCoordSystems" data-uid="VRage.Voxels.MyVoxelCoordSystems" class="text-break">Class MyVoxelCoordSystems
  </h1>
  <div class="markdown level0 summary"><p>Functions for transforming to and from various coordinate systems in voxel maps and for computing bounding boxes of various types of cells.
Note that local and world positions are (and should be) always in the min-corner!</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyVoxelCoordSystems</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Voxels.html">VRage.Voxels</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Voxels_MyVoxelCoordSystems_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class MyVoxelCoordSystems : Object</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_FindBestOctreeSize_" data-uid="VRage.Voxels.MyVoxelCoordSystems.FindBestOctreeSize*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_FindBestOctreeSize_System_Single_" data-uid="VRage.Voxels.MyVoxelCoordSystems.FindBestOctreeSize(System.Single)">FindBestOctreeSize(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3I FindBestOctreeSize(float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCenterCoordToWorldPos_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCenterCoordToWorldPos*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCenterCoordToWorldPos_VRageMath_Vector3D_VRageMath_Vector3I__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCenterCoordToWorldPos(VRageMath.Vector3D,VRageMath.Vector3I@,VRageMath.Vector3D@)">GeometryCellCenterCoordToWorldPos(Vector3D, ref Vector3I, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCenterCoordToWorldPos(Vector3D referenceVoxelMapPosition, ref Vector3I geometryCellCoord, out Vector3D worldPos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPos</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalAABB_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalAABB*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalAABB_VRageMath_Vector3I__VRageMath_BoundingBox__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalAABB(VRageMath.Vector3I@,VRageMath.BoundingBox@)">GeometryCellCoordToLocalAABB(ref Vector3I, out BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCoordToLocalAABB(ref Vector3I geometryCellCoord, out BoundingBox localAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">localAABB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalPosition_VRage_Voxels_MyCellCoord__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalPosition(VRage.Voxels.MyCellCoord@,VRageMath.Vector3@)">GeometryCellCoordToLocalPosition(ref MyCellCoord, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCoordToLocalPosition(ref MyCellCoord geometryCellCoord, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyCellCoord.html">MyCellCoord</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToLocalPosition_VRageMath_Vector3I__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToLocalPosition(VRageMath.Vector3I@,VRageMath.Vector3@)">GeometryCellCoordToLocalPosition(ref Vector3I, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCoordToLocalPosition(ref Vector3I geometryCellCoord, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToWorldAABB_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToWorldAABB*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToWorldAABB_VRageMath_Vector3D_VRage_Voxels_MyCellCoord__VRageMath_BoundingBoxD__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToWorldAABB(VRageMath.Vector3D,VRage.Voxels.MyCellCoord@,VRageMath.BoundingBoxD@)">GeometryCellCoordToWorldAABB(Vector3D, ref MyCellCoord, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCoordToWorldAABB(Vector3D referenceVoxelMapPosition, ref MyCellCoord geometryCellCoord, out BoundingBoxD worldAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyCellCoord.html">MyCellCoord</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">worldAABB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToWorldAABB_" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToWorldAABB*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_GeometryCellCoordToWorldAABB_VRageMath_Vector3D_VRageMath_Vector3I__VRageMath_BoundingBoxD__" data-uid="VRage.Voxels.MyVoxelCoordSystems.GeometryCellCoordToWorldAABB(VRageMath.Vector3D,VRageMath.Vector3I@,VRageMath.BoundingBoxD@)">GeometryCellCoordToWorldAABB(Vector3D, ref Vector3I, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GeometryCellCoordToWorldAABB(Vector3D referenceVoxelMapPosition, ref Vector3I geometryCellCoord, out BoundingBoxD worldAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">worldAABB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToGeometryCellCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToGeometryCellCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToGeometryCellCoord_VRageMath_Vector3__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToGeometryCellCoord(VRageMath.Vector3@,VRageMath.Vector3I@)">LocalPositionToGeometryCellCoord(ref Vector3, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToGeometryCellCoord(ref Vector3 localPosition, out Vector3I geometryCellCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVertexCell_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVertexCell*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVertexCell_System_Int32_VRageMath_Vector3__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVertexCell(System.Int32,VRageMath.Vector3@,VRageMath.Vector3I@)">LocalPositionToVertexCell(Int32, ref Vector3, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToVertexCell(int lod, ref Vector3 localPosition, out Vector3I vertexCell)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lod</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">vertexCell</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVoxelCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVoxelCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVoxelCoord_VRageMath_Vector3__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVoxelCoord(VRageMath.Vector3@,VRageMath.Vector3D@)">LocalPositionToVoxelCoord(ref Vector3, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToVoxelCoord(ref Vector3 localPosition, out Vector3D voxelCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVoxelCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVoxelCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToVoxelCoord_VRageMath_Vector3__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToVoxelCoord(VRageMath.Vector3@,VRageMath.Vector3I@)">LocalPositionToVoxelCoord(ref Vector3, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToVoxelCoord(ref Vector3 localPosition, out Vector3I voxelCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToWorldPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToWorldPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToWorldPosition_VRageMath_MatrixD_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToWorldPosition(VRageMath.MatrixD,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3@,VRageMath.Vector3D@)">LocalPositionToWorldPosition(MatrixD, Vector3D, Vector3, ref Vector3, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToWorldPosition(MatrixD matrix, Vector3D referenceVoxelMapPosition, Vector3 halfSize, ref Vector3 localPosition, out Vector3D worldPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToWorldPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToWorldPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_LocalPositionToWorldPosition_VRageMath_Vector3D_VRageMath_Vector3__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.LocalPositionToWorldPosition(VRageMath.Vector3D,VRageMath.Vector3@,VRageMath.Vector3D@)">LocalPositionToWorldPosition(Vector3D, ref Vector3, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LocalPositionToWorldPosition(Vector3D referenceVoxelMapPosition, ref Vector3 localPosition, out Vector3D worldPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VertexCellToLocalAABB_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VertexCellToLocalAABB*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VertexCellToLocalAABB_System_Int32_VRageMath_Vector3I__VRageMath_BoundingBoxD__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VertexCellToLocalAABB(System.Int32,VRageMath.Vector3I@,VRageMath.BoundingBoxD@)">VertexCellToLocalAABB(Int32, ref Vector3I, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VertexCellToLocalAABB(int lod, ref Vector3I vertexCell, out BoundingBoxD localAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lod</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">vertexCell</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">localAABB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VertexCellToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VertexCellToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VertexCellToLocalPosition_System_Int32_VRageMath_Vector3I__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VertexCellToLocalPosition(System.Int32,VRageMath.Vector3I@,VRageMath.Vector3@)">VertexCellToLocalPosition(Int32, ref Vector3I, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VertexCellToLocalPosition(int lod, ref Vector3I vertexCell, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lod</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">vertexCell</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToGeometryCellCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToGeometryCellCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToGeometryCellCoord_VRageMath_Vector3I__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToGeometryCellCoord(VRageMath.Vector3I@,VRageMath.Vector3I@)">VoxelCoordToGeometryCellCoord(ref Vector3I, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VoxelCoordToGeometryCellCoord(ref Vector3I voxelCoord, out Vector3I geometryCellCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToLocalPosition_VRageMath_Vector3I__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToLocalPosition(VRageMath.Vector3I@,VRageMath.Vector3@)">VoxelCoordToLocalPosition(ref Vector3I, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VoxelCoordToLocalPosition(ref Vector3I voxelCoord, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldAABB_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldAABB*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldAABB_VRageMath_Vector3D_VRageMath_Vector3I__VRageMath_BoundingBoxD__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldAABB(VRageMath.Vector3D,VRageMath.Vector3I@,VRageMath.BoundingBoxD@)">VoxelCoordToWorldAABB(Vector3D, ref Vector3I, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VoxelCoordToWorldAABB(Vector3D referenceVoxelMapPosition, ref Vector3I voxelCoord, out BoundingBoxD worldAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">worldAABB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldPosition_VRageMath_MatrixD_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3I__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldPosition(VRageMath.MatrixD,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3I@,VRageMath.Vector3D@)">VoxelCoordToWorldPosition(MatrixD, Vector3D, Vector3, ref Vector3I, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VoxelCoordToWorldPosition(MatrixD matrix, Vector3D referenceVoxelMapPosition, Vector3 halfsize, ref Vector3I voxelCoord, out Vector3D worldPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfsize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_VoxelCoordToWorldPosition_VRageMath_Vector3D_VRageMath_Vector3I__VRageMath_Vector3D__" data-uid="VRage.Voxels.MyVoxelCoordSystems.VoxelCoordToWorldPosition(VRageMath.Vector3D,VRageMath.Vector3I@,VRageMath.Vector3D@)">VoxelCoordToWorldPosition(Vector3D, ref Vector3I, out Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void VoxelCoordToWorldPosition(Vector3D referenceVoxelMapPosition, ref Vector3I voxelCoord, out Vector3D worldPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToGeometryCellCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToGeometryCellCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToGeometryCellCoord_VRageMath_Vector3D_VRageMath_Vector3D__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToGeometryCellCoord(VRageMath.Vector3D,VRageMath.Vector3D@,VRageMath.Vector3I@)">WorldPositionToGeometryCellCoord(Vector3D, ref Vector3D, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToGeometryCellCoord(Vector3D referenceVoxelMapPosition, ref Vector3D worldPosition, out Vector3I geometryCellCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">geometryCellCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_VRageMath_MatrixD_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3D__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition(VRageMath.MatrixD,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3D@,VRageMath.Vector3@)">WorldPositionToLocalPosition(MatrixD, Vector3D, Vector3, ref Vector3D, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToLocalPosition(MatrixD matrix, Vector3D referenceVoxelMapPosition, Vector3 halfSize, ref Vector3D worldPosition, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_VRageMath_Vector3D_VRageMath_MatrixD_VRageMath_MatrixD_VRageMath_Vector3_VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition(VRageMath.Vector3D,VRageMath.MatrixD,VRageMath.MatrixD,VRageMath.Vector3,VRageMath.Vector3@)">WorldPositionToLocalPosition(Vector3D, MatrixD, MatrixD, Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToLocalPosition(Vector3D worldPosition, MatrixD worldMatrix, MatrixD worldMatrixInv, Vector3 halfSize, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrixInv</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToLocalPosition_VRageMath_Vector3D_VRageMath_Vector3D__VRageMath_Vector3__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToLocalPosition(VRageMath.Vector3D,VRageMath.Vector3D@,VRageMath.Vector3@)">WorldPositionToLocalPosition(Vector3D, ref Vector3D, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToLocalPosition(Vector3D referenceVoxelMapPosition, ref Vector3D worldPosition, out Vector3 localPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">localPosition</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_VRageMath_MatrixD_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3D__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord(VRageMath.MatrixD,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3D@,VRageMath.Vector3I@)">WorldPositionToVoxelCoord(MatrixD, Vector3D, Vector3, ref Vector3D, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToVoxelCoord(MatrixD matrix, Vector3D referenceVoxelMapPosition, Vector3 halfsize, ref Vector3D worldPosition, out Vector3I voxelCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfsize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_VRageMath_Vector3D_VRageMath_Vector3D__VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord(VRageMath.Vector3D,VRageMath.Vector3D@,VRageMath.Vector3I@)">WorldPositionToVoxelCoord(Vector3D, ref Vector3D, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToVoxelCoord(Vector3D referenceVoxelMapPosition, ref Vector3D worldPosition, out Vector3I voxelCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">referenceVoxelMapPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord*"></a>
  <h4 id="VRage_Voxels_MyVoxelCoordSystems_WorldPositionToVoxelCoord_VRageMath_Vector3D__VRageMath_MatrixD_VRageMath_MatrixD_VRageMath_Vector3_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyVoxelCoordSystems.WorldPositionToVoxelCoord(VRageMath.Vector3D@,VRageMath.MatrixD,VRageMath.MatrixD,VRageMath.Vector3,VRageMath.Vector3I@)">WorldPositionToVoxelCoord(ref Vector3D, MatrixD, MatrixD, Vector3, out Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void WorldPositionToVoxelCoord(ref Vector3D worldPosition, MatrixD worldMatrix, MatrixD worldMatrixInv, Vector3 halfSize, out Vector3I voxelCoord)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrixInv</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">halfSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelCoord</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
