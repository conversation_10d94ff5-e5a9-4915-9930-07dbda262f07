﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRageMath
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRageMath
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath">
  
  <h1 id="VRageMath" data-uid="VRageMath" class="text-break">Namespace VRageMath
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRageMath.Base27Directions.html">Base27Directions</a></h4>
      <section><p>Base 26 directions and Vector3.Zero
Each component is only 0,-1 or 1;</p>
</section>
      <h4><a class="xref" href="VRageMath.Base27Directions.Direction.html">Base27Directions.Direction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Base6Directions.html">Base6Directions</a></h4>
      <section><p>Workaround because .NET XML serializer is stupid and does not like enum inside static class</p>
</section>
      <h4><a class="xref" href="VRageMath.Base6Directions.Axis.html">Base6Directions.Axis</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Base6Directions.DirectionFlags.html">Base6Directions.DirectionFlags</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></h4>
      <section><p>Defines an axis-aligned box-shaped 3D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingBox.ComparerType.html">BoundingBox.ComparerType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.BoundingBox2.html">BoundingBox2</a></h4>
      <section><p>Defines an axis-aligned box-shaped 3D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingBox2D.html">BoundingBox2D</a></h4>
      <section><p>Defines an axis-aligned box-shaped 2D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></h4>
      <section><p>Defines an axis-aligned box-shaped 3D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></h4>
      <section><p>Defines an axis-aligned box-shaped 3D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingBoxD.ComparerType.html">BoundingBoxD.ComparerType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.BoundingBoxExtensions.html">BoundingBoxExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></h4>
      <section><p>Defines an axis-aligned box-shaped 3D volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></h4>
      <section><p>Defines a frustum and helps determine whether forms intersect with it.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></h4>
      <section><p>Defines a frustum and helps determine whether forms intersect with it.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingFrustumExtensions.html">BoundingFrustumExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></h4>
      <section><p>Defines a sphere.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></h4>
      <section><p>Defines a sphere.</p>
</section>
      <h4><a class="xref" href="VRageMath.BoxCornerEnumerator.html">BoxCornerEnumerator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Capsule.html">Capsule</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.CapsuleD.html">CapsuleD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Color.html">Color</a></h4>
      <section><p>Represents a four-component color using red, green, blue, and alpha data.</p>
</section>
      <h4><a class="xref" href="VRageMath.ColorExtensions.html">ColorExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.CompressedPositionOrientation.html">CompressedPositionOrientation</a></h4>
      <section><p>Defines a matrix.</p>
</section>
      <h4><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></h4>
      <section><p>Indicates the extent to which bounding volumes intersect or contain one another.</p>
</section>
      <h4><a class="xref" href="VRageMath.CubeFace.html">CubeFace</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Curve.html">Curve</a></h4>
      <section><p>Stores an arbitrary collection of 2D CurveKey points, and provides methods for evaluating features of the curve they define.</p>
</section>
      <h4><a class="xref" href="VRageMath.CurveContinuity.html">CurveContinuity</a></h4>
      <section><p>Defines the continuity of CurveKeys on a Curve.</p>
</section>
      <h4><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></h4>
      <section><p>Represents a point in a multi-point curve.</p>
</section>
      <h4><a class="xref" href="VRageMath.CurveKeyCollection.html">CurveKeyCollection</a></h4>
      <section><p>Contains the CurveKeys making up a Curve.</p>
</section>
      <h4><a class="xref" href="VRageMath.CurveLoopType.html">CurveLoopType</a></h4>
      <section><p>Defines how the value of a Curve will be determined for positions before the first point on the Curve or after the last point on the Curve.</p>
</section>
      <h4><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></h4>
      <section><p>Specifies different tangent types to be calculated for CurveKey points in a Curve.</p>
</section>
      <h4><a class="xref" href="VRageMath.HyperSphereHelpers.html">HyperSphereHelpers</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Line.html">Line</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.LineD.html">LineD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MathHelper.html">MathHelper</a></h4>
      <section><p>Contains commonly used precalculated values.</p>
</section>
      <h4><a class="xref" href="VRageMath.MathHelperD.html">MathHelperD</a></h4>
      <section><p>Contains commonly used precalculated values.</p>
</section>
      <h4><a class="xref" href="VRageMath.Matrix.html">Matrix</a></h4>
      <section><p>Defines a matrix.</p>
</section>
      <h4><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></h4>
      <section><p>Defines a matrix.</p>
</section>
      <h4><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></h4>
      <section><p>Defines a matrix.</p>
</section>
      <h4><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyBounds.html">MyBounds</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyCuboid.html">MyCuboid</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyCuboidSide.html">MyCuboidSide</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyDynamicAABBTree.html">MyDynamicAABBTree</a></h4>
      <section><p>Dynamic aabb tree implementation as a prunning structure</p>
</section>
      <h4><a class="xref" href="VRageMath.MyDynamicAABBTree.DynamicTreeNode.html">MyDynamicAABBTree.DynamicTreeNode</a></h4>
      <section><p>A node in the dynamic tree. The client does not interact with this directly.</p>
</section>
      <h4><a class="xref" href="VRageMath.MyDynamicAABBTreeD.html">MyDynamicAABBTreeD</a></h4>
      <section><p>Dynamic aabb tree implementation as a prunning structure</p>
</section>
      <h4><a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.html">MyLineSegmentOverlapResult&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.MyLineSegmentOverlapResultComparer.html">MyLineSegmentOverlapResult&lt;T&gt;.MyLineSegmentOverlapResultComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyMath.html">MyMath</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyMortonCode3D.html">MyMortonCode3D</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyMovingAverage.html">MyMovingAverage</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyOrientedBoundingBox.html">MyOrientedBoundingBox</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyOrientedBoundingBoxD.html">MyOrientedBoundingBoxD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyQuad.html">MyQuad</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyShort4.html">MyShort4</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyTransform.html">MyTransform</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyTransformD.html">MyTransformD</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.MyUShort4.html">MyUShort4</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.NullableVector3DExtensions.html">NullableVector3DExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.NullableVector3Extensions.html">NullableVector3Extensions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Plane.html">Plane</a></h4>
      <section><p>Defines a plane.</p>
</section>
      <h4><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></h4>
      <section><p>Defines a PlaneD.</p>
</section>
      <h4><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></h4>
      <section><p>Describes the intersection between a plane and a bounding volume.</p>
</section>
      <h4><a class="xref" href="VRageMath.Point.html">Point</a></h4>
      <section><p>Defines a point in 2D space.</p>
</section>
      <h4><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></h4>
      <section><p>Defines a four-dimensional vector (x,y,z,w), which is used to efficiently rotate an object about the (x, y, z) vector by the angle theta, where w = cos(theta/2).</p>
</section>
      <h4><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></h4>
      <section><p>Defines a four-dimensional vector (x,y,z,w), which is used to efficiently rotate an object about the (x, y, z) vector by the angle theta, where w = cos(theta/2).
Uses double precision floating point numbers for calculation and storage</p>
</section>
      <h4><a class="xref" href="VRageMath.Ray.html">Ray</a></h4>
      <section><p>Defines a ray.</p>
</section>
      <h4><a class="xref" href="VRageMath.RayD.html">RayD</a></h4>
      <section><p>Defines a ray.</p>
</section>
      <h4><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></h4>
      <section><p>Defines a rectangle.</p>
</section>
      <h4><a class="xref" href="VRageMath.RectangleF.html">RectangleF</a></h4>
      <section><p>Structure using the same layout than <span class="xref">System.Drawing.RectangleF</span></p>
</section>
      <h4><a class="xref" href="VRageMath.SerializableRange.html">SerializableRange</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.SymmetricSerializableRange.html">SymmetricSerializableRange</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector2.html">Vector2</a></h4>
      <section><p>Defines a vector with two components.</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector2B.html">Vector2B</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></h4>
      <section><p>Defines a vector with two components.</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector2I.ComparerClass.html">Vector2I.ComparerClass</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3.html">Vector3</a></h4>
      <section><p>Defines a vector with three components.</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3B.html">Vector3B</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></h4>
      <section><p>Defines a vector with three components. Vector3 with double floating point precision</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3Extensions.html">Vector3Extensions</a></h4>
      <section><p>Useful Vector3 extensions</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3I.EqualityComparer.html">Vector3I.EqualityComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3I_RangeIterator.html">Vector3I_RangeIterator</a></h4>
      <section><p>A class for simpler traversal of ranges of integer vectors</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3INormalEqualityComparer.html">Vector3INormalEqualityComparer</a></h4>
      <section><p>This can be used only to compare normal vectors of Vector3I, where X, Y and Z has values -1, 0 or 1</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3L.html">Vector3L</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3L.EqualityComparer.html">Vector3L.EqualityComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3L_RangeIterator.html">Vector3L_RangeIterator</a></h4>
      <section><p>A class for simpler traversal of ranges of long vectors</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3LNormalEqualityComparer.html">Vector3LNormalEqualityComparer</a></h4>
      <section><p>This can be used only to compare normal vectors of Vector3L, where X, Y and Z has values -1, 0 or 1</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector3S.html">Vector3S</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3UByte.html">Vector3UByte</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3UByte.EqualityComparer.html">Vector3UByte.EqualityComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector3Ushort.html">Vector3Ushort</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector4.html">Vector4</a></h4>
      <section><p>Defines a vector with four components.</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></h4>
      <section><p>Defines a vector with four components.</p>
</section>
      <h4><a class="xref" href="VRageMath.Vector4I.html">Vector4I</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector4I.EqualityComparer.html">Vector4I.EqualityComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRageMath.Vector4UByte.html">Vector4UByte</a></h4>
      <section></section>
    <h3 id="interfaces">Interfaces
  </h3>
      <h4><a class="xref" href="VRageMath.IAddOp-1.html">IAddOp&lt;T&gt;</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
