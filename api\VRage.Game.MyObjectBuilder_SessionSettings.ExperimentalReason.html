﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_SessionSettings.ExperimentalReason
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_SessionSettings.ExperimentalReason
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason" class="text-break">Class MyObjectBuilder_SessionSettings.ExperimentalReason
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectBuilder_SessionSettings.ExperimentalReason</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class ExperimentalReason : Enum</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_AdaptiveSimulationQuality" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.AdaptiveSimulationQuality">AdaptiveSimulationQuality</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason AdaptiveSimulationQuality</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_BlockLimitsEnabled" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.BlockLimitsEnabled">BlockLimitsEnabled</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason BlockLimitsEnabled</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_EnableIngameScripts" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.EnableIngameScripts">EnableIngameScripts</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason EnableIngameScripts</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_EnableSpectator" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.EnableSpectator">EnableSpectator</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason EnableSpectator</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_EnableSubgridDamage" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.EnableSubgridDamage">EnableSubgridDamage</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason EnableSubgridDamage</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ExperimentalMode" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ExperimentalMode">ExperimentalMode</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ExperimentalMode</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ExperimentalTurnedOnInConfiguration" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ExperimentalTurnedOnInConfiguration">ExperimentalTurnedOnInConfiguration</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ExperimentalTurnedOnInConfiguration</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_InsufficientHardware" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.InsufficientHardware">InsufficientHardware</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason InsufficientHardware</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_MaxFloatingObjects" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.MaxFloatingObjects">MaxFloatingObjects</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason MaxFloatingObjects</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_MaxPlayers" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.MaxPlayers">MaxPlayers</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason MaxPlayers</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_Mods" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.Mods">Mods</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason Mods</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_PermanentDeath" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.PermanentDeath">PermanentDeath</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason PermanentDeath</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_PhysicsIterations" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.PhysicsIterations">PhysicsIterations</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason PhysicsIterations</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_Plugins" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.Plugins">Plugins</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason Plugins</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ProceduralDensity" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ProceduralDensity">ProceduralDensity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ProceduralDensity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ReasonMax" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ReasonMax">ReasonMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ReasonMax</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ResetOwnership" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ResetOwnership">ResetOwnership</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ResetOwnership</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_ShareInertiaTensor" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.ShareInertiaTensor">ShareInertiaTensor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason ShareInertiaTensor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_StationVoxelSupport" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.StationVoxelSupport">StationVoxelSupport</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason StationVoxelSupport</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_SunRotationIntervalMinutes" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.SunRotationIntervalMinutes">SunRotationIntervalMinutes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason SunRotationIntervalMinutes</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_SupergriddingEnabled" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.SupergriddingEnabled">SupergriddingEnabled</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason SupergriddingEnabled</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_SyncDistance" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.SyncDistance">SyncDistance</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason SyncDistance</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_TotalBotLimit" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.TotalBotLimit">TotalBotLimit</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason TotalBotLimit</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_TotalPCU" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.TotalPCU">TotalPCU</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason TotalPCU</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_UnsafePistonImpulses" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.UnsafePistonImpulses">UnsafePistonImpulses</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason UnsafePistonImpulses</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_UnsafeRotorTorques" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.UnsafeRotorTorques">UnsafeRotorTorques</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyObjectBuilder_SessionSettings.ExperimentalReason UnsafeRotorTorques</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.html">MyObjectBuilder_SessionSettings.ExperimentalReason</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_SessionSettings_ExperimentalReason_value__" data-uid="VRage.Game.MyObjectBuilder_SessionSettings.ExperimentalReason.value__">value__</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long value__</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
