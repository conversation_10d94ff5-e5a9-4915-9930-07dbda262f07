﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_Checkpoint.ModItem
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_Checkpoint.ModItem
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem" class="text-break">Class MyObjectBuilder_Checkpoint.ModItem
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectBuilder_Checkpoint.ModItem</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class ModItem : ValueType</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_System_String_System_UInt64_System_String_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor(System.String,System.UInt64,System.String)">ModItem(String, UInt64, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ModItem(string name, ulong publishedFileId, string publishedServiceName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">publishedFileId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">publishedServiceName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_System_String_System_UInt64_System_String_System_String_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor(System.String,System.UInt64,System.String,System.String)">ModItem(String, UInt64, String, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ModItem(string name, ulong publishedFileId, string publishedServiceName, string friendlyName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">publishedFileId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">publishedServiceName</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">friendlyName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_System_UInt64_System_String_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor(System.UInt64,System.String)">ModItem(UInt64, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ModItem(ulong publishedFileId, string publishedServiceName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">publishedFileId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">publishedServiceName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem__ctor_System_UInt64_System_String_System_Boolean_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.#ctor(System.UInt64,System.String,System.Boolean)">ModItem(UInt64, String, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ModItem(ulong publishedFileId, string publishedServiceName, bool isDependency)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">publishedFileId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">publishedServiceName</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">isDependency</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_FriendlyName" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.FriendlyName">FriendlyName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string FriendlyName</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_IsDependency" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.IsDependency">IsDependency</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsDependency</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_Name" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.Name">Name</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Name</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_PublishedFileId" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.PublishedFileId">PublishedFileId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ulong PublishedFileId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_PublishedServiceName" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.PublishedServiceName">PublishedServiceName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string PublishedServiceName</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_Equals_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.Equals*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_Equals_VRage_Game_MyObjectBuilder_Checkpoint_ModItem_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.Equals(VRage.Game.MyObjectBuilder_Checkpoint.ModItem)">Equals(MyObjectBuilder_Checkpoint.ModItem)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(MyObjectBuilder_Checkpoint.ModItem other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.html">MyObjectBuilder_Checkpoint.ModItem</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetModContext_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetModContext*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetModContext" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetModContext">GetModContext()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IMyModContext GetModContext()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyModContext.html">IMyModContext</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetModData_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetModData*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetModData" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetModData">GetModData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyWorkshopItem GetModData()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.GameServices.MyWorkshopItem</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetPath_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetPath*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetPath" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetPath">GetPath()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string GetPath()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetWorkshopId_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetWorkshopId*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_GetWorkshopId" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.GetWorkshopId">GetWorkshopId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public WorkshopId GetWorkshopId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.WorkshopId.html">WorkshopId</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_IsModData_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.IsModData*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_IsModData" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.IsModData">IsModData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsModData()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_SetModData_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.SetModData*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_SetModData_VRage_GameServices_MyWorkshopItem_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.SetModData(VRage.GameServices.MyWorkshopItem)">SetModData(MyWorkshopItem)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetModData(MyWorkshopItem workshopItem)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.GameServices.MyWorkshopItem</span></td>
        <td><span class="parametername">workshopItem</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeFriendlyName_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeFriendlyName*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeFriendlyName" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeFriendlyName">ShouldSerializeFriendlyName()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeFriendlyName()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeIsDependency_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeIsDependency*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeIsDependency" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeIsDependency">ShouldSerializeIsDependency()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeIsDependency()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeName_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeName*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializeName" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializeName">ShouldSerializeName()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeName()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializePublishedFileId_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializePublishedFileId*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ShouldSerializePublishedFileId" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ShouldSerializePublishedFileId">ShouldSerializePublishedFileId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializePublishedFileId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ToString_" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ToString*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_Checkpoint_ModItem_ToString" data-uid="VRage.Game.MyObjectBuilder_Checkpoint.ModItem.ToString">ToString()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
