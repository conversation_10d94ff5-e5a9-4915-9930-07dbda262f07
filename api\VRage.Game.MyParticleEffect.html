﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyParticleEffect
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyParticleEffect
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyParticleEffect">
  
  
  <h1 id="VRage_Game_MyParticleEffect" data-uid="VRage.Game.MyParticleEffect" class="text-break">Class MyParticleEffect
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyParticleEffect</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyParticleEffect_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyParticleEffect : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyParticleEffect__ctor_" data-uid="VRage.Game.MyParticleEffect.#ctor*"></a>
  <h4 id="VRage_Game_MyParticleEffect__ctor" data-uid="VRage.Game.MyParticleEffect.#ctor">MyParticleEffect()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyParticleEffect()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyParticleEffect_Autodelete_" data-uid="VRage.Game.MyParticleEffect.Autodelete*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Autodelete" data-uid="VRage.Game.MyParticleEffect.Autodelete">Autodelete</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Autodelete { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_CameraSoftRadiusMultiplier_" data-uid="VRage.Game.MyParticleEffect.CameraSoftRadiusMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_CameraSoftRadiusMultiplier" data-uid="VRage.Game.MyParticleEffect.CameraSoftRadiusMultiplier">CameraSoftRadiusMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float CameraSoftRadiusMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Data_" data-uid="VRage.Game.MyParticleEffect.Data*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Data" data-uid="VRage.Game.MyParticleEffect.Data">Data</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyParticleEffectData Data { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Render.Particles.MyParticleEffectData</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_DistanceMax_" data-uid="VRage.Game.MyParticleEffect.DistanceMax*"></a>
  <h4 id="VRage_Game_MyParticleEffect_DistanceMax" data-uid="VRage.Game.MyParticleEffect.DistanceMax">DistanceMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DistanceMax { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_DurationMax_" data-uid="VRage.Game.MyParticleEffect.DurationMax*"></a>
  <h4 id="VRage_Game_MyParticleEffect_DurationMax" data-uid="VRage.Game.MyParticleEffect.DurationMax">DurationMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DurationMax { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Id_" data-uid="VRage.Game.MyParticleEffect.Id*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Id" data-uid="VRage.Game.MyParticleEffect.Id">Id</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public uint Id { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_IsEmittingStopped_" data-uid="VRage.Game.MyParticleEffect.IsEmittingStopped*"></a>
  <h4 id="VRage_Game_MyParticleEffect_IsEmittingStopped" data-uid="VRage.Game.MyParticleEffect.IsEmittingStopped">IsEmittingStopped</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsEmittingStopped { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_IsStopped_" data-uid="VRage.Game.MyParticleEffect.IsStopped*"></a>
  <h4 id="VRage_Game_MyParticleEffect_IsStopped" data-uid="VRage.Game.MyParticleEffect.IsStopped">IsStopped</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsStopped { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Loop_" data-uid="VRage.Game.MyParticleEffect.Loop*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Loop" data-uid="VRage.Game.MyParticleEffect.Loop">Loop</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Loop { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_SoftParticleDistanceScaleMultiplier_" data-uid="VRage.Game.MyParticleEffect.SoftParticleDistanceScaleMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_SoftParticleDistanceScaleMultiplier" data-uid="VRage.Game.MyParticleEffect.SoftParticleDistanceScaleMultiplier">SoftParticleDistanceScaleMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SoftParticleDistanceScaleMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserBirthMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserBirthMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserBirthMultiplier" data-uid="VRage.Game.MyParticleEffect.UserBirthMultiplier">UserBirthMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserBirthMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserColorIntensityMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserColorIntensityMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserColorIntensityMultiplier" data-uid="VRage.Game.MyParticleEffect.UserColorIntensityMultiplier">UserColorIntensityMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserColorIntensityMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserColorMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserColorMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserColorMultiplier" data-uid="VRage.Game.MyParticleEffect.UserColorMultiplier">UserColorMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4 UserColorMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserEmitterScale_" data-uid="VRage.Game.MyParticleEffect.UserEmitterScale*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserEmitterScale" data-uid="VRage.Game.MyParticleEffect.UserEmitterScale">UserEmitterScale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserEmitterScale { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserFadeMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserFadeMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserFadeMultiplier" data-uid="VRage.Game.MyParticleEffect.UserFadeMultiplier">UserFadeMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserFadeMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserLifeMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserLifeMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserLifeMultiplier" data-uid="VRage.Game.MyParticleEffect.UserLifeMultiplier">UserLifeMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserLifeMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserRadiusMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserRadiusMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserRadiusMultiplier" data-uid="VRage.Game.MyParticleEffect.UserRadiusMultiplier">UserRadiusMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserRadiusMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserScale_" data-uid="VRage.Game.MyParticleEffect.UserScale*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserScale" data-uid="VRage.Game.MyParticleEffect.UserScale">UserScale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserScale { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_UserVelocityMultiplier_" data-uid="VRage.Game.MyParticleEffect.UserVelocityMultiplier*"></a>
  <h4 id="VRage_Game_MyParticleEffect_UserVelocityMultiplier" data-uid="VRage.Game.MyParticleEffect.UserVelocityMultiplier">UserVelocityMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float UserVelocityMultiplier { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Velocity_" data-uid="VRage.Game.MyParticleEffect.Velocity*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Velocity" data-uid="VRage.Game.MyParticleEffect.Velocity">Velocity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Velocity { set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_WorldMatrix_" data-uid="VRage.Game.MyParticleEffect.WorldMatrix*"></a>
  <h4 id="VRage_Game_MyParticleEffect_WorldMatrix" data-uid="VRage.Game.MyParticleEffect.WorldMatrix">WorldMatrix</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD WorldMatrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyParticleEffect_AssertUnload_" data-uid="VRage.Game.MyParticleEffect.AssertUnload*"></a>
  <h4 id="VRage_Game_MyParticleEffect_AssertUnload" data-uid="VRage.Game.MyParticleEffect.AssertUnload">AssertUnload()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertUnload()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_Clear_" data-uid="VRage.Game.MyParticleEffect.Clear*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Clear" data-uid="VRage.Game.MyParticleEffect.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_Close_" data-uid="VRage.Game.MyParticleEffect.Close*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Close" data-uid="VRage.Game.MyParticleEffect.Close">Close()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Close()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_GetElapsedTime_" data-uid="VRage.Game.MyParticleEffect.GetElapsedTime*"></a>
  <h4 id="VRage_Game_MyParticleEffect_GetElapsedTime" data-uid="VRage.Game.MyParticleEffect.GetElapsedTime">GetElapsedTime()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float GetElapsedTime()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_GetName_" data-uid="VRage.Game.MyParticleEffect.GetName*"></a>
  <h4 id="VRage_Game_MyParticleEffect_GetName" data-uid="VRage.Game.MyParticleEffect.GetName">GetName()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string GetName()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Init_" data-uid="VRage.Game.MyParticleEffect.Init*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Init_VRage_Render_Particles_MyParticleEffectData_VRageMath_MatrixD__System_UInt32_" data-uid="VRage.Game.MyParticleEffect.Init(VRage.Render.Particles.MyParticleEffectData,VRageMath.MatrixD@,System.UInt32)">Init(MyParticleEffectData, ref MatrixD, UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Init(MyParticleEffectData data, ref MatrixD effectMatrix, uint parentId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Render.Particles.MyParticleEffectData</span></td>
        <td><span class="parametername">data</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">effectMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">parentId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_OnRemoved_" data-uid="VRage.Game.MyParticleEffect.OnRemoved*"></a>
  <h4 id="VRage_Game_MyParticleEffect_OnRemoved" data-uid="VRage.Game.MyParticleEffect.OnRemoved">OnRemoved()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OnRemoved()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_Pause_" data-uid="VRage.Game.MyParticleEffect.Pause*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Pause" data-uid="VRage.Game.MyParticleEffect.Pause">Pause()</h4>
  <div class="markdown level1 summary"><p>This methods freezes effect and particles</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Pause()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_Play_" data-uid="VRage.Game.MyParticleEffect.Play*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Play" data-uid="VRage.Game.MyParticleEffect.Play">Play()</h4>
  <div class="markdown level1 summary"><p>This method restores effect</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Play()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_SetDirty_" data-uid="VRage.Game.MyParticleEffect.SetDirty*"></a>
  <h4 id="VRage_Game_MyParticleEffect_SetDirty" data-uid="VRage.Game.MyParticleEffect.SetDirty">SetDirty()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDirty()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_SetElapsedTime_" data-uid="VRage.Game.MyParticleEffect.SetElapsedTime*"></a>
  <h4 id="VRage_Game_MyParticleEffect_SetElapsedTime_System_Single_" data-uid="VRage.Game.MyParticleEffect.SetElapsedTime(System.Single)">SetElapsedTime(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetElapsedTime(float time)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">time</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_SetTranslation_" data-uid="VRage.Game.MyParticleEffect.SetTranslation*"></a>
  <h4 id="VRage_Game_MyParticleEffect_SetTranslation_VRageMath_Vector3D_" data-uid="VRage.Game.MyParticleEffect.SetTranslation(VRageMath.Vector3D)">SetTranslation(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetTranslation(Vector3D trans)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">trans</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_SetTranslation_" data-uid="VRage.Game.MyParticleEffect.SetTranslation*"></a>
  <h4 id="VRage_Game_MyParticleEffect_SetTranslation_VRageMath_Vector3D__" data-uid="VRage.Game.MyParticleEffect.SetTranslation(VRageMath.Vector3D@)">SetTranslation(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetTranslation(ref Vector3D trans)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">trans</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Stop_" data-uid="VRage.Game.MyParticleEffect.Stop*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Stop_System_Boolean_" data-uid="VRage.Game.MyParticleEffect.Stop(System.Boolean)">Stop(Boolean)</h4>
  <div class="markdown level1 summary"><p>This method stops and deletes effect completely</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Stop(bool instant = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">instant</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_StopEmitting_" data-uid="VRage.Game.MyParticleEffect.StopEmitting*"></a>
  <h4 id="VRage_Game_MyParticleEffect_StopEmitting_System_Single_" data-uid="VRage.Game.MyParticleEffect.StopEmitting(System.Single)">StopEmitting(Single)</h4>
  <div class="markdown level1 summary"><p>This method stops generating any new particles</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void StopEmitting(float timeout = 0F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">timeout</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_StopLights_" data-uid="VRage.Game.MyParticleEffect.StopLights*"></a>
  <h4 id="VRage_Game_MyParticleEffect_StopLights" data-uid="VRage.Game.MyParticleEffect.StopLights">StopLights()</h4>
  <div class="markdown level1 summary"><p>This method stops all lights</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void StopLights()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyParticleEffect_ToString_" data-uid="VRage.Game.MyParticleEffect.ToString*"></a>
  <h4 id="VRage_Game_MyParticleEffect_ToString" data-uid="VRage.Game.MyParticleEffect.ToString">ToString()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyParticleEffect_Update_" data-uid="VRage.Game.MyParticleEffect.Update*"></a>
  <h4 id="VRage_Game_MyParticleEffect_Update" data-uid="VRage.Game.MyParticleEffect.Update">Update()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Update()</code></pre>
  </div>
  <h3 id="events">Events
  </h3>
  
  
  <h4 id="VRage_Game_MyParticleEffect_OnDelete" data-uid="VRage.Game.MyParticleEffect.OnDelete">OnDelete</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event Action&lt;MyParticleEffect&gt; OnDelete</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.Game.MyParticleEffect.html">MyParticleEffect</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
