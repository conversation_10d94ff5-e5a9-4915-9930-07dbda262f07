﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Matrix
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Matrix
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Matrix">
  
  
  <h1 id="VRageMath_Matrix" data-uid="VRageMath.Matrix" class="text-break">Class Matrix
  </h1>
  <div class="markdown level0 summary"><p>Defines a matrix.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Matrix</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Matrix_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Matrix : ValueType, IEquatable&lt;Matrix&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Matrix__ctor_" data-uid="VRageMath.Matrix.#ctor*"></a>
  <h4 id="VRageMath_Matrix__ctor_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">Matrix(Single, Single, Single, Single, Single, Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Matrix with rotation data</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix(float m11, float m12, float m13, float m21, float m22, float m23, float m31, float m32, float m33)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m11</span></td>
        <td><p>Value to initialize m11 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m12</span></td>
        <td><p>Value to initialize m12 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m13</span></td>
        <td><p>Value to initialize m13 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m21</span></td>
        <td><p>Value to initialize m21 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m22</span></td>
        <td><p>Value to initialize m22 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m23</span></td>
        <td><p>Value to initialize m23 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m31</span></td>
        <td><p>Value to initialize m31 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m32</span></td>
        <td><p>Value to initialize m32 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m33</span></td>
        <td><p>Value to initialize m33 to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix__ctor_" data-uid="VRageMath.Matrix.#ctor*"></a>
  <h4 id="VRageMath_Matrix__ctor_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">Matrix(Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix(float m11, float m12, float m13, float m14, float m21, float m22, float m23, float m24, float m31, float m32, float m33, float m34, float m41, float m42, float m43, float m44)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m11</span></td>
        <td><p>Value to initialize m11 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m12</span></td>
        <td><p>Value to initialize m12 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m13</span></td>
        <td><p>Value to initialize m13 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m14</span></td>
        <td><p>Value to initialize m14 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m21</span></td>
        <td><p>Value to initialize m21 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m22</span></td>
        <td><p>Value to initialize m22 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m23</span></td>
        <td><p>Value to initialize m23 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m24</span></td>
        <td><p>Value to initialize m24 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m31</span></td>
        <td><p>Value to initialize m31 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m32</span></td>
        <td><p>Value to initialize m32 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m33</span></td>
        <td><p>Value to initialize m33 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m34</span></td>
        <td><p>Value to initialize m34 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m41</span></td>
        <td><p>Value to initialize m41 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m42</span></td>
        <td><p>Value to initialize m42 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m43</span></td>
        <td><p>Value to initialize m43 to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">m44</span></td>
        <td><p>Value to initialize m44 to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix__ctor_" data-uid="VRageMath.Matrix.#ctor*"></a>
  <h4 id="VRageMath_Matrix__ctor_VRageMath_MatrixD_" data-uid="VRageMath.Matrix.#ctor(VRageMath.MatrixD)">Matrix(MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix(MatrixD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Matrix_Identity" data-uid="VRageMath.Matrix.Identity">Identity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Identity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M11" data-uid="VRageMath.Matrix.M11">M11</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M11</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M12" data-uid="VRageMath.Matrix.M12">M12</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M12</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M13" data-uid="VRageMath.Matrix.M13">M13</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M13</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M14" data-uid="VRageMath.Matrix.M14">M14</h4>
  <div class="markdown level1 summary"><p>Value at row 1 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M14</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M21" data-uid="VRageMath.Matrix.M21">M21</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M21</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M22" data-uid="VRageMath.Matrix.M22">M22</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M22</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M23" data-uid="VRageMath.Matrix.M23">M23</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M23</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M24" data-uid="VRageMath.Matrix.M24">M24</h4>
  <div class="markdown level1 summary"><p>Value at row 2 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M24</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M31" data-uid="VRageMath.Matrix.M31">M31</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M31</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M32" data-uid="VRageMath.Matrix.M32">M32</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M32</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M33" data-uid="VRageMath.Matrix.M33">M33</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M33</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M34" data-uid="VRageMath.Matrix.M34">M34</h4>
  <div class="markdown level1 summary"><p>Value at row 3 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M34</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M41" data-uid="VRageMath.Matrix.M41">M41</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 1 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M41</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M42" data-uid="VRageMath.Matrix.M42">M42</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 2 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M42</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M43" data-uid="VRageMath.Matrix.M43">M43</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 3 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M43</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_M44" data-uid="VRageMath.Matrix.M44">M44</h4>
  <div class="markdown level1 summary"><p>Value at row 4 column 4 of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float M44</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Matrix_Zero" data-uid="VRageMath.Matrix.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Matrix_Backward_" data-uid="VRageMath.Matrix.Backward*"></a>
  <h4 id="VRageMath_Matrix_Backward" data-uid="VRageMath.Matrix.Backward">Backward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the backward vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Backward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Col0_" data-uid="VRageMath.Matrix.Col0*"></a>
  <h4 id="VRageMath_Matrix_Col0" data-uid="VRageMath.Matrix.Col0">Col0</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col0 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Col1_" data-uid="VRageMath.Matrix.Col1*"></a>
  <h4 id="VRageMath_Matrix_Col1" data-uid="VRageMath.Matrix.Col1">Col1</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col1 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Col2_" data-uid="VRageMath.Matrix.Col2*"></a>
  <h4 id="VRageMath_Matrix_Col2" data-uid="VRageMath.Matrix.Col2">Col2</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Col2 { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Down_" data-uid="VRageMath.Matrix.Down*"></a>
  <h4 id="VRageMath_Matrix_Down" data-uid="VRageMath.Matrix.Down">Down</h4>
  <div class="markdown level1 summary"><p>Gets and sets the down vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Down { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Forward_" data-uid="VRageMath.Matrix.Forward*"></a>
  <h4 id="VRageMath_Matrix_Forward" data-uid="VRageMath.Matrix.Forward">Forward</h4>
  <div class="markdown level1 summary"><p>Gets and sets the forward vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Forward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Item_" data-uid="VRageMath.Matrix.Item*"></a>
  <h4 id="VRageMath_Matrix_Item_System_Int32_System_Int32_" data-uid="VRageMath.Matrix.Item(System.Int32,System.Int32)">Item[Int32, Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float this[int row, int column] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">column</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Left_" data-uid="VRageMath.Matrix.Left*"></a>
  <h4 id="VRageMath_Matrix_Left" data-uid="VRageMath.Matrix.Left">Left</h4>
  <div class="markdown level1 summary"><p>Gets and sets the left vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Left { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Right_" data-uid="VRageMath.Matrix.Right*"></a>
  <h4 id="VRageMath_Matrix_Right" data-uid="VRageMath.Matrix.Right">Right</h4>
  <div class="markdown level1 summary"><p>Gets and sets the right vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Right { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Scale_" data-uid="VRageMath.Matrix.Scale*"></a>
  <h4 id="VRageMath_Matrix_Scale" data-uid="VRageMath.Matrix.Scale">Scale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Scale { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Translation_" data-uid="VRageMath.Matrix.Translation*"></a>
  <h4 id="VRageMath_Matrix_Translation" data-uid="VRageMath.Matrix.Translation">Translation</h4>
  <div class="markdown level1 summary"><p>Gets and sets the translation vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Translation { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Up_" data-uid="VRageMath.Matrix.Up*"></a>
  <h4 id="VRageMath_Matrix_Up" data-uid="VRageMath.Matrix.Up">Up</h4>
  <div class="markdown level1 summary"><p>Gets and sets the up vector of the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Up { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Matrix_Add_" data-uid="VRageMath.Matrix.Add*"></a>
  <h4 id="VRageMath_Matrix_Add_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Add(VRageMath.Matrix,VRageMath.Matrix)">Add(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Add(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Add_" data-uid="VRageMath.Matrix.Add*"></a>
  <h4 id="VRageMath_Matrix_Add_VRageMath_Matrix__VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Add(VRageMath.Matrix@,VRageMath.Matrix@,VRageMath.Matrix@)">Add(ref Matrix, ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Matrix matrix1, ref Matrix matrix2, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_AlignRotationToAxes_" data-uid="VRageMath.Matrix.AlignRotationToAxes*"></a>
  <h4 id="VRageMath_Matrix_AlignRotationToAxes_VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.AlignRotationToAxes(VRageMath.Matrix@,VRageMath.Matrix@)">AlignRotationToAxes(ref Matrix, ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix AlignRotationToAxes(ref Matrix toAlign, ref Matrix axisDefinitionMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">toAlign</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">axisDefinitionMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_AssertIsValid_" data-uid="VRageMath.Matrix.AssertIsValid*"></a>
  <h4 id="VRageMath_Matrix_AssertIsValid" data-uid="VRageMath.Matrix.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_Matrix_CreateBillboard_" data-uid="VRageMath.Matrix.CreateBillboard*"></a>
  <h4 id="VRageMath_Matrix_CreateBillboard_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Nullable_VRageMath_Vector3__" data-uid="VRageMath.Matrix.CreateBillboard(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Nullable{VRageMath.Vector3})">CreateBillboard(Vector3, Vector3, Vector3, Nullable&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates a spherical billboard that rotates around a specified object position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateBillboard(Vector3 objectPosition, Vector3 cameraPosition, Vector3 cameraUpVector, Nullable&lt;Vector3&gt; cameraForwardVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The up vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateBillboard_" data-uid="VRageMath.Matrix.CreateBillboard*"></a>
  <h4 id="VRageMath_Matrix_CreateBillboard_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Nullable_VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateBillboard(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Nullable{VRageMath.Vector3},VRageMath.Matrix@)">CreateBillboard(ref Vector3, ref Vector3, ref Vector3, Nullable&lt;Vector3&gt;, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a spherical billboard that rotates around a specified object position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateBillboard(ref Vector3 objectPosition, ref Vector3 cameraPosition, ref Vector3 cameraUpVector, Nullable&lt;Vector3&gt; cameraForwardVector, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The up vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created billboard matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateConstrainedBillboard_" data-uid="VRageMath.Matrix.CreateConstrainedBillboard*"></a>
  <h4 id="VRageMath_Matrix_CreateConstrainedBillboard_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Nullable_VRageMath_Vector3__System_Nullable_VRageMath_Vector3__" data-uid="VRageMath.Matrix.CreateConstrainedBillboard(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Nullable{VRageMath.Vector3},System.Nullable{VRageMath.Vector3})">CreateConstrainedBillboard(Vector3, Vector3, Vector3, Nullable&lt;Vector3&gt;, Nullable&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates a cylindrical billboard that rotates around a specified axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateConstrainedBillboard(Vector3 objectPosition, Vector3 cameraPosition, Vector3 rotateAxis, Nullable&lt;Vector3&gt; cameraForwardVector, Nullable&lt;Vector3&gt; objectForwardVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">rotateAxis</span></td>
        <td><p>Axis to rotate the billboard around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">objectForwardVector</span></td>
        <td><p>Optional forward vector of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateConstrainedBillboard_" data-uid="VRageMath.Matrix.CreateConstrainedBillboard*"></a>
  <h4 id="VRageMath_Matrix_CreateConstrainedBillboard_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Nullable_VRageMath_Vector3__System_Nullable_VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateConstrainedBillboard(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Nullable{VRageMath.Vector3},System.Nullable{VRageMath.Vector3},VRageMath.Matrix@)">CreateConstrainedBillboard(ref Vector3, ref Vector3, ref Vector3, Nullable&lt;Vector3&gt;, Nullable&lt;Vector3&gt;, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a cylindrical billboard that rotates around a specified axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateConstrainedBillboard(ref Vector3 objectPosition, ref Vector3 cameraPosition, ref Vector3 rotateAxis, Nullable&lt;Vector3&gt; cameraForwardVector, Nullable&lt;Vector3&gt; objectForwardVector, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">objectPosition</span></td>
        <td><p>Position of the object the billboard will rotate around.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>Position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">rotateAxis</span></td>
        <td><p>Axis to rotate the billboard around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">cameraForwardVector</span></td>
        <td><p>Optional forward vector of the camera.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">objectForwardVector</span></td>
        <td><p>Optional forward vector of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created billboard matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromAxisAngle_" data-uid="VRageMath.Matrix.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Matrix_CreateFromAxisAngle_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Matrix.CreateFromAxisAngle(VRageMath.Vector3,System.Single)">CreateFromAxisAngle(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromAxisAngle(Vector3 axis, float angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromAxisAngle_" data-uid="VRageMath.Matrix.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Matrix_CreateFromAxisAngle_VRageMath_Vector3__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateFromAxisAngle(VRageMath.Vector3@,System.Single,VRageMath.Matrix@)">CreateFromAxisAngle(ref Vector3, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a new Matrix that rotates around an arbitrary vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAxisAngle(ref Vector3 axis, float angle, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromDir_" data-uid="VRageMath.Matrix.CreateFromDir*"></a>
  <h4 id="VRageMath_Matrix_CreateFromDir_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateFromDir(VRageMath.Vector3)">CreateFromDir(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromDir(Vector3 dir)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromDir_" data-uid="VRageMath.Matrix.CreateFromDir*"></a>
  <h4 id="VRageMath_Matrix_CreateFromDir_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateFromDir(VRageMath.Vector3,VRageMath.Vector3)">CreateFromDir(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromDir(Vector3 dir, Vector3 suggestedUp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">dir</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">suggestedUp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromPerspectiveFieldOfView_" data-uid="VRageMath.Matrix.CreateFromPerspectiveFieldOfView*"></a>
  <h4 id="VRageMath_Matrix_CreateFromPerspectiveFieldOfView_VRageMath_Matrix__System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateFromPerspectiveFieldOfView(VRageMath.Matrix@,System.Single,System.Single)">CreateFromPerspectiveFieldOfView(ref Matrix, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromPerspectiveFieldOfView(ref Matrix proj, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">proj</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromQuaternion_" data-uid="VRageMath.Matrix.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_Matrix_CreateFromQuaternion_VRageMath_Quaternion_" data-uid="VRageMath.Matrix.CreateFromQuaternion(VRageMath.Quaternion)">CreateFromQuaternion(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromQuaternion(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromQuaternion_" data-uid="VRageMath.Matrix.CreateFromQuaternion*"></a>
  <h4 id="VRageMath_Matrix_CreateFromQuaternion_VRageMath_Quaternion__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateFromQuaternion(VRageMath.Quaternion@,VRageMath.Matrix@)">CreateFromQuaternion(ref Quaternion, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a rotation Matrix from a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromQuaternion(ref Quaternion quaternion, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Quaternion to create the Matrix from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromTransformScale_" data-uid="VRageMath.Matrix.CreateFromTransformScale*"></a>
  <h4 id="VRageMath_Matrix_CreateFromTransformScale_VRageMath_Quaternion_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateFromTransformScale(VRageMath.Quaternion,VRageMath.Vector3,VRageMath.Vector3)">CreateFromTransformScale(Quaternion, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromTransformScale(Quaternion orientation, Vector3 position, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromYawPitchRoll_" data-uid="VRageMath.Matrix.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Matrix_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">CreateFromYawPitchRoll(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateFromYawPitchRoll(float yaw, float pitch, float roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateFromYawPitchRoll_" data-uid="VRageMath.Matrix.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Matrix_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateFromYawPitchRoll(System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreateFromYawPitchRoll(Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Fills in a rotation matrix from a specified yaw, pitch, and roll.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromYawPitchRoll(float yaw, float pitch, float roll, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>Angle of rotation, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>Angle of rotation, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>Angle of rotation, in radians, around the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing matrix filled in to represent the specified yaw, pitch, and roll.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateLookAt_" data-uid="VRageMath.Matrix.CreateLookAt*"></a>
  <h4 id="VRageMath_Matrix_CreateLookAt_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateLookAt(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">CreateLookAt(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a view matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateLookAt(Vector3 cameraPosition, Vector3 cameraTarget, Vector3 cameraUpVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>The position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td><p>The target towards which the camera is pointing.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The direction that is &quot;up&quot; from the camera's point of view.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateLookAt_" data-uid="VRageMath.Matrix.CreateLookAt*"></a>
  <h4 id="VRageMath_Matrix_CreateLookAt_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateLookAt(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Matrix@)">CreateLookAt(ref Vector3, ref Vector3, ref Vector3, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a view matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateLookAt(ref Vector3 cameraPosition, ref Vector3 cameraTarget, ref Vector3 cameraUpVector, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td><p>The position of the camera.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td><p>The target towards which the camera is pointing.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td><p>The direction that is &quot;up&quot; from the camera's point of view.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created view matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateLookAtInverse_" data-uid="VRageMath.Matrix.CreateLookAtInverse*"></a>
  <h4 id="VRageMath_Matrix_CreateLookAtInverse_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateLookAtInverse(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">CreateLookAtInverse(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateLookAtInverse(Vector3 cameraPosition, Vector3 cameraTarget, Vector3 cameraUpVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraTarget</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraUpVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateOrthographic_" data-uid="VRageMath.Matrix.CreateOrthographic*"></a>
  <h4 id="VRageMath_Matrix_CreateOrthographic_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateOrthographic(System.Single,System.Single,System.Single,System.Single)">CreateOrthographic(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Builds an orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateOrthographic(float width, float height, float zNearPlane, float zFarPlane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateOrthographic_" data-uid="VRageMath.Matrix.CreateOrthographic*"></a>
  <h4 id="VRageMath_Matrix_CreateOrthographic_System_Single_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateOrthographic(System.Single,System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreateOrthographic(Single, Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Builds an orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateOrthographic(float width, float height, float zNearPlane, float zFarPlane, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateOrthographicOffCenter_" data-uid="VRageMath.Matrix.CreateOrthographicOffCenter*"></a>
  <h4 id="VRageMath_Matrix_CreateOrthographicOffCenter_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateOrthographicOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">CreateOrthographicOffCenter(Single, Single, Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateOrthographicOffCenter(float left, float right, float bottom, float top, float zNearPlane, float zFarPlane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateOrthographicOffCenter_" data-uid="VRageMath.Matrix.CreateOrthographicOffCenter*"></a>
  <h4 id="VRageMath_Matrix_CreateOrthographicOffCenter_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateOrthographicOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreateOrthographicOffCenter(Single, Single, Single, Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, orthogonal projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateOrthographicOffCenter(float left, float right, float bottom, float top, float zNearPlane, float zFarPlane, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zNearPlane</span></td>
        <td><p>Minimum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zFarPlane</span></td>
        <td><p>Maximum z-value of the view volume.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspective_" data-uid="VRageMath.Matrix.CreatePerspective*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspective_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspective(System.Single,System.Single,System.Single,System.Single)">CreatePerspective(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix and returns the result by value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspective(float width, float height, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspective_" data-uid="VRageMath.Matrix.CreatePerspective*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspective_System_Single_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreatePerspective(System.Single,System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreatePerspective(Single, Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix and returns the result by reference.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspective(float width, float height, float nearPlaneDistance, float farPlaneDistance, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFieldOfView_" data-uid="VRageMath.Matrix.CreatePerspectiveFieldOfView*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFieldOfView_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFieldOfView(System.Single,System.Single,System.Single,System.Single)">CreatePerspectiveFieldOfView(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix based on a field of view and returns by value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFieldOfView(float fieldOfView, float aspectRatio, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td><p>Field of view in the y direction, in radians.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td><p>Aspect ratio, defined as view space width divided by height. To match the aspect ratio of the viewport, the property AspectRatio.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFieldOfView_" data-uid="VRageMath.Matrix.CreatePerspectiveFieldOfView*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFieldOfView_System_Single_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreatePerspectiveFieldOfView(System.Single,System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreatePerspectiveFieldOfView(Single, Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Builds a perspective projection matrix based on a field of view and returns by reference.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspectiveFieldOfView(float fieldOfView, float aspectRatio, float nearPlaneDistance, float farPlaneDistance, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td><p>Field of view in the y direction, in radians.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td><p>Aspect ratio, defined as view space width divided by height. To match the aspect ratio of the viewport, the property AspectRatio.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The perspective projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhComplementary_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhComplementary*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhComplementary_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhComplementary(System.Single,System.Single,System.Single,System.Single)">CreatePerspectiveFovRhComplementary(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhComplementary(float fieldOfView, float aspectRatio, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhInfinite_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfinite*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhInfinite_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfinite(System.Single,System.Single,System.Single)">CreatePerspectiveFovRhInfinite(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhInfinite(float fieldOfView, float aspectRatio, float nearPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteComplementary_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteComplementary*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteComplementary_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteComplementary(System.Single,System.Single,System.Single)">CreatePerspectiveFovRhInfiniteComplementary(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhInfiniteComplementary(float fieldOfView, float aspectRatio, float nearPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteComplementaryInverse_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteComplementaryInverse*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteComplementaryInverse_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteComplementaryInverse(System.Single,System.Single,System.Single)">CreatePerspectiveFovRhInfiniteComplementaryInverse(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhInfiniteComplementaryInverse(float fieldOfView, float aspectRatio, float nearPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteInverse_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteInverse*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhInfiniteInverse_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInfiniteInverse(System.Single,System.Single,System.Single)">CreatePerspectiveFovRhInfiniteInverse(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhInfiniteInverse(float fieldOfView, float aspectRatio, float nearPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveFovRhInverse_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInverse*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveFovRhInverse_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveFovRhInverse(System.Single,System.Single,System.Single,System.Single)">CreatePerspectiveFovRhInverse(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveFovRhInverse(float fieldOfView, float aspectRatio, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">fieldOfView</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aspectRatio</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveOffCenter_" data-uid="VRageMath.Matrix.CreatePerspectiveOffCenter*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveOffCenter_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreatePerspectiveOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">CreatePerspectiveOffCenter(Single, Single, Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, perspective projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreatePerspectiveOffCenter(float left, float right, float bottom, float top, float nearPlaneDistance, float farPlaneDistance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to of the far view plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreatePerspectiveOffCenter_" data-uid="VRageMath.Matrix.CreatePerspectiveOffCenter*"></a>
  <h4 id="VRageMath_Matrix_CreatePerspectiveOffCenter_System_Single_System_Single_System_Single_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreatePerspectiveOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreatePerspectiveOffCenter(Single, Single, Single, Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Builds a customized, perspective projection matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreatePerspectiveOffCenter(float left, float right, float bottom, float top, float nearPlaneDistance, float farPlaneDistance, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">left</span></td>
        <td><p>Minimum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">right</span></td>
        <td><p>Maximum x-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">bottom</span></td>
        <td><p>Minimum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">top</span></td>
        <td><p>Maximum y-value of the view volume at the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">nearPlaneDistance</span></td>
        <td><p>Distance to the near view plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">farPlaneDistance</span></td>
        <td><p>Distance to of the far view plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateReflection_" data-uid="VRageMath.Matrix.CreateReflection*"></a>
  <h4 id="VRageMath_Matrix_CreateReflection_VRageMath_Plane_" data-uid="VRageMath.Matrix.CreateReflection(VRageMath.Plane)">CreateReflection(Plane)</h4>
  <div class="markdown level1 summary"><p>Creates a Matrix that reflects the coordinate system about a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateReflection(Plane value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane about which to create a reflection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateReflection_" data-uid="VRageMath.Matrix.CreateReflection*"></a>
  <h4 id="VRageMath_Matrix_CreateReflection_VRageMath_Plane__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateReflection(VRageMath.Plane@,VRageMath.Matrix@)">CreateReflection(ref Plane, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Fills in an existing Matrix so that it reflects the coordinate system about a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateReflection(ref Plane value, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane about which to create a reflection.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A Matrix that creates the reflection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationFromTwoVectors_" data-uid="VRageMath.Matrix.CreateRotationFromTwoVectors*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationFromTwoVectors_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateRotationFromTwoVectors(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Matrix@)">CreateRotationFromTwoVectors(ref Vector3, ref Vector3, out Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationFromTwoVectors(ref Vector3 fromVector, ref Vector3 toVector, out Matrix resultMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">fromVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">toVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">resultMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationX_" data-uid="VRageMath.Matrix.CreateRotationX*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationX_System_Single_" data-uid="VRageMath.Matrix.CreateRotationX(System.Single)">CreateRotationX(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateRotationX(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationX_" data-uid="VRageMath.Matrix.CreateRotationX*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationX_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateRotationX(System.Single,VRageMath.Matrix@)">CreateRotationX(Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the x-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationX(float radians, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the x-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationY_" data-uid="VRageMath.Matrix.CreateRotationY*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationY_System_Single_" data-uid="VRageMath.Matrix.CreateRotationY(System.Single)">CreateRotationY(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateRotationY(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationY_" data-uid="VRageMath.Matrix.CreateRotationY*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationY_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateRotationY(System.Single,VRageMath.Matrix@)">CreateRotationY(Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the y-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationY(float radians, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the y-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The matrix in which to place the calculated data.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationZ_" data-uid="VRageMath.Matrix.CreateRotationZ*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationZ_System_Single_" data-uid="VRageMath.Matrix.CreateRotationZ(System.Single)">CreateRotationZ(Single)</h4>
  <div class="markdown level1 summary"><p>Returns a matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateRotationZ(float radians)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateRotationZ_" data-uid="VRageMath.Matrix.CreateRotationZ*"></a>
  <h4 id="VRageMath_Matrix_CreateRotationZ_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateRotationZ(System.Single,VRageMath.Matrix@)">CreateRotationZ(Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Populates data into a user-specified matrix that can be used to rotate a set of vertices around the z-axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateRotationZ(float radians, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radians</span></td>
        <td><p>The amount, in radians, in which to rotate around the z-axis. Note that you can use ToRadians to convert degrees to radians.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The rotation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_System_Single_" data-uid="VRageMath.Matrix.CreateScale(System.Single)">CreateScale(Single)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateScale(float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Amount to scale by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateScale(System.Single,System.Single,System.Single)">CreateScale(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateScale(float xScale, float yScale, float zScale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateScale(System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreateScale(Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(float xScale, float yScale, float zScale, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xScale</span></td>
        <td><p>Value to scale by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yScale</span></td>
        <td><p>Value to scale by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zScale</span></td>
        <td><p>Value to scale by on the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateScale(System.Single,VRageMath.Matrix@)">CreateScale(Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(float scale, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Value to scale by.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateScale(VRageMath.Vector3)">CreateScale(Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateScale(Vector3 scales)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateScale_" data-uid="VRageMath.Matrix.CreateScale*"></a>
  <h4 id="VRageMath_Matrix_CreateScale_VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateScale(VRageMath.Vector3@,VRageMath.Matrix@)">CreateScale(ref Vector3, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a scaling Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateScale(ref Vector3 scales, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scales</span></td>
        <td><p>Amounts to scale by on the x, y, and z axes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created scaling Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateShadow_" data-uid="VRageMath.Matrix.CreateShadow*"></a>
  <h4 id="VRageMath_Matrix_CreateShadow_VRageMath_Vector3_VRageMath_Plane_" data-uid="VRageMath.Matrix.CreateShadow(VRageMath.Vector3,VRageMath.Plane)">CreateShadow(Vector3, Plane)</h4>
  <div class="markdown level1 summary"><p>Creates a Matrix that flattens geometry into a specified Plane as if casting a shadow from a specified light source.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateShadow(Vector3 lightDirection, Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">lightDirection</span></td>
        <td><p>A Vector3 specifying the direction from which the light that will cast the shadow is coming.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane onto which the new matrix should flatten geometry so as to cast a shadow.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateShadow_" data-uid="VRageMath.Matrix.CreateShadow*"></a>
  <h4 id="VRageMath_Matrix_CreateShadow_VRageMath_Vector3__VRageMath_Plane__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateShadow(VRageMath.Vector3@,VRageMath.Plane@,VRageMath.Matrix@)">CreateShadow(ref Vector3, ref Plane, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Fills in a Matrix to flatten geometry into a specified Plane as if casting a shadow from a specified light source.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateShadow(ref Vector3 lightDirection, ref Plane plane, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">lightDirection</span></td>
        <td><p>A Vector3 specifying the direction from which the light that will cast the shadow is coming.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane onto which the new matrix should flatten geometry so as to cast a shadow.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A Matrix that can be used to flatten geometry onto the specified plane from the specified direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateTranslation_" data-uid="VRageMath.Matrix.CreateTranslation*"></a>
  <h4 id="VRageMath_Matrix_CreateTranslation_System_Single_System_Single_System_Single_" data-uid="VRageMath.Matrix.CreateTranslation(System.Single,System.Single,System.Single)">CreateTranslation(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateTranslation(float xPosition, float yPosition, float zPosition)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xPosition</span></td>
        <td><p>Value to translate by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yPosition</span></td>
        <td><p>Value to translate by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zPosition</span></td>
        <td><p>Value to translate by on the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateTranslation_" data-uid="VRageMath.Matrix.CreateTranslation*"></a>
  <h4 id="VRageMath_Matrix_CreateTranslation_System_Single_System_Single_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateTranslation(System.Single,System.Single,System.Single,VRageMath.Matrix@)">CreateTranslation(Single, Single, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateTranslation(float xPosition, float yPosition, float zPosition, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">xPosition</span></td>
        <td><p>Value to translate by on the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yPosition</span></td>
        <td><p>Value to translate by on the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">zPosition</span></td>
        <td><p>Value to translate by on the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created translation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateTranslation_" data-uid="VRageMath.Matrix.CreateTranslation*"></a>
  <h4 id="VRageMath_Matrix_CreateTranslation_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateTranslation(VRageMath.Vector3)">CreateTranslation(Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateTranslation(Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Amounts to translate by on the x, y, and z axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateTranslation_" data-uid="VRageMath.Matrix.CreateTranslation*"></a>
  <h4 id="VRageMath_Matrix_CreateTranslation_VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateTranslation(VRageMath.Vector3@,VRageMath.Matrix@)">CreateTranslation(ref Vector3, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a translation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateTranslation(ref Vector3 position, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Amounts to translate by on the x, y, and z axes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created translation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateWorld_" data-uid="VRageMath.Matrix.CreateWorld*"></a>
  <h4 id="VRageMath_Matrix_CreateWorld_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateWorld(VRageMath.Vector3)">CreateWorld(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateWorld(Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateWorld_" data-uid="VRageMath.Matrix.CreateWorld*"></a>
  <h4 id="VRageMath_Matrix_CreateWorld_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Matrix.CreateWorld(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">CreateWorld(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a world matrix with the specified parameters.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix CreateWorld(Vector3 position, Vector3 forward, Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position of the object. This value is used in translation operations.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td><p>Forward direction of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td><p>Upward direction of the object; usually [0, 1, 0].</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_CreateWorld_" data-uid="VRageMath.Matrix.CreateWorld*"></a>
  <h4 id="VRageMath_Matrix_CreateWorld_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__VRageMath_Matrix__" data-uid="VRageMath.Matrix.CreateWorld(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Matrix@)">CreateWorld(ref Vector3, ref Vector3, ref Vector3, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a world matrix with the specified parameters.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateWorld(ref Vector3 position, ref Vector3 forward, ref Vector3 up, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position of the object. This value is used in translation operations.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td><p>Forward direction of the object.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td><p>Upward direction of the object; usually [0, 1, 0].</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created world matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Determinant_" data-uid="VRageMath.Matrix.Determinant*"></a>
  <h4 id="VRageMath_Matrix_Determinant" data-uid="VRageMath.Matrix.Determinant">Determinant()</h4>
  <div class="markdown level1 summary"><p>Calculates the determinant of the matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Determinant()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Divide_" data-uid="VRageMath.Matrix.Divide*"></a>
  <h4 id="VRageMath_Matrix_Divide_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.Divide(VRageMath.Matrix,System.Single)">Divide(Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Divide(Matrix matrix1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Divide_" data-uid="VRageMath.Matrix.Divide*"></a>
  <h4 id="VRageMath_Matrix_Divide_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Divide(VRageMath.Matrix,VRageMath.Matrix)">Divide(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Divide(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Divide_" data-uid="VRageMath.Matrix.Divide*"></a>
  <h4 id="VRageMath_Matrix_Divide_VRageMath_Matrix__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Divide(VRageMath.Matrix@,System.Single,VRageMath.Matrix@)">Divide(ref Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Matrix matrix1, float divider, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Divide_" data-uid="VRageMath.Matrix.Divide*"></a>
  <h4 id="VRageMath_Matrix_Divide_VRageMath_Matrix__VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Divide(VRageMath.Matrix@,VRageMath.Matrix@,VRageMath.Matrix@)">Divide(ref Matrix, ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Matrix matrix1, ref Matrix matrix2, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Equals_" data-uid="VRageMath.Matrix.Equals*"></a>
  <h4 id="VRageMath_Matrix_Equals_System_Object_" data-uid="VRageMath.Matrix.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Equals_" data-uid="VRageMath.Matrix.Equals*"></a>
  <h4 id="VRageMath_Matrix_Equals_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Equals(VRageMath.Matrix)">Equals(Matrix)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Matrix other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_EqualsFast_" data-uid="VRageMath.Matrix.EqualsFast*"></a>
  <h4 id="VRageMath_Matrix_EqualsFast_VRageMath_Matrix__System_Single_" data-uid="VRageMath.Matrix.EqualsFast(VRageMath.Matrix@,System.Single)">EqualsFast(ref Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Compares just position, forward and up</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool EqualsFast(ref Matrix other, float epsilon = 0.0001F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetClosestDirection_" data-uid="VRageMath.Matrix.GetClosestDirection*"></a>
  <h4 id="VRageMath_Matrix_GetClosestDirection_VRageMath_Vector3_" data-uid="VRageMath.Matrix.GetClosestDirection(VRageMath.Vector3)">GetClosestDirection(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(Vector3 referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetClosestDirection_" data-uid="VRageMath.Matrix.GetClosestDirection*"></a>
  <h4 id="VRageMath_Matrix_GetClosestDirection_VRageMath_Vector3__" data-uid="VRageMath.Matrix.GetClosestDirection(VRageMath.Vector3@)">GetClosestDirection(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetClosestDirection(ref Vector3 referenceVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">referenceVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetDirectionVector_" data-uid="VRageMath.Matrix.GetDirectionVector*"></a>
  <h4 id="VRageMath_Matrix_GetDirectionVector_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.Matrix.GetDirectionVector(VRageMath.Base6Directions.Direction)">GetDirectionVector(Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"><p>Gets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 GetDirectionVector(Base6Directions.Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetEulerAnglesXYZ_" data-uid="VRageMath.Matrix.GetEulerAnglesXYZ*"></a>
  <h4 id="VRageMath_Matrix_GetEulerAnglesXYZ_VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Matrix.GetEulerAnglesXYZ(VRageMath.Matrix@,VRageMath.Vector3@)">GetEulerAnglesXYZ(ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetEulerAnglesXYZ(ref Matrix mat, out Vector3 xyz)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">mat</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">xyz</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetHashCode_" data-uid="VRageMath.Matrix.GetHashCode*"></a>
  <h4 id="VRageMath_Matrix_GetHashCode" data-uid="VRageMath.Matrix.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetOrientation_" data-uid="VRageMath.Matrix.GetOrientation*"></a>
  <h4 id="VRageMath_Matrix_GetOrientation" data-uid="VRageMath.Matrix.GetOrientation">GetOrientation()</h4>
  <div class="markdown level1 summary"><p>Gets the orientation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix GetOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_GetRow_" data-uid="VRageMath.Matrix.GetRow*"></a>
  <h4 id="VRageMath_Matrix_GetRow_System_Int32_" data-uid="VRageMath.Matrix.GetRow(System.Int32)">GetRow(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4 GetRow(int row)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_HasNoTranslationOrPerspective_" data-uid="VRageMath.Matrix.HasNoTranslationOrPerspective*"></a>
  <h4 id="VRageMath_Matrix_HasNoTranslationOrPerspective" data-uid="VRageMath.Matrix.HasNoTranslationOrPerspective">HasNoTranslationOrPerspective()</h4>
  <div class="markdown level1 summary"><p>Returns true if this matrix represents invertible (you can call Invert on it) linear (it does not contain translation or perspective transformation) transformation.
Such matrix consist solely of rotations, shearing, mirroring and scaling. It can be orthogonalized to create an orthogonal rotation matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasNoTranslationOrPerspective()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Invert_" data-uid="VRageMath.Matrix.Invert*"></a>
  <h4 id="VRageMath_Matrix_Invert_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Invert(VRageMath.Matrix)">Invert(Matrix)</h4>
  <div class="markdown level1 summary"><p>Calculates the inverse of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Invert(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Invert_" data-uid="VRageMath.Matrix.Invert*"></a>
  <h4 id="VRageMath_Matrix_Invert_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Invert(VRageMath.Matrix@)">Invert(ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Invert(ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Invert_" data-uid="VRageMath.Matrix.Invert*"></a>
  <h4 id="VRageMath_Matrix_Invert_VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Invert(VRageMath.Matrix@,VRageMath.Matrix@)">Invert(ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Calculates the inverse of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Invert(ref Matrix matrix, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The inverse of matrix. The same matrix can be used for both arguments.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_IsMirrored_" data-uid="VRageMath.Matrix.IsMirrored*"></a>
  <h4 id="VRageMath_Matrix_IsMirrored" data-uid="VRageMath.Matrix.IsMirrored">IsMirrored()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsMirrored()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_IsNan_" data-uid="VRageMath.Matrix.IsNan*"></a>
  <h4 id="VRageMath_Matrix_IsNan" data-uid="VRageMath.Matrix.IsNan">IsNan()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsNan()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_IsOrthogonal_" data-uid="VRageMath.Matrix.IsOrthogonal*"></a>
  <h4 id="VRageMath_Matrix_IsOrthogonal" data-uid="VRageMath.Matrix.IsOrthogonal">IsOrthogonal()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsOrthogonal()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_IsRotation_" data-uid="VRageMath.Matrix.IsRotation*"></a>
  <h4 id="VRageMath_Matrix_IsRotation" data-uid="VRageMath.Matrix.IsRotation">IsRotation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRotation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_IsValid_" data-uid="VRageMath.Matrix.IsValid*"></a>
  <h4 id="VRageMath_Matrix_IsValid" data-uid="VRageMath.Matrix.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Lerp_" data-uid="VRageMath.Matrix.Lerp*"></a>
  <h4 id="VRageMath_Matrix_Lerp_VRageMath_Matrix_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.Lerp(VRageMath.Matrix,VRageMath.Matrix,System.Single)">Lerp(Matrix, Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between the corresponding values of two matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Lerp(Matrix matrix1, Matrix matrix2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Lerp_" data-uid="VRageMath.Matrix.Lerp*"></a>
  <h4 id="VRageMath_Matrix_Lerp_VRageMath_Matrix__VRageMath_Matrix__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Lerp(VRageMath.Matrix@,VRageMath.Matrix@,System.Single,VRageMath.Matrix@)">Lerp(ref Matrix, ref Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between the corresponding values of two matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Matrix matrix1, ref Matrix matrix2, float amount, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Resulting matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Multiply_" data-uid="VRageMath.Matrix.Multiply*"></a>
  <h4 id="VRageMath_Matrix_Multiply_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.Multiply(VRageMath.Matrix,System.Single)">Multiply(Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Multiply(Matrix matrix1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Multiply_" data-uid="VRageMath.Matrix.Multiply*"></a>
  <h4 id="VRageMath_Matrix_Multiply_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Multiply(VRageMath.Matrix,VRageMath.Matrix)">Multiply(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Multiply(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Multiply_" data-uid="VRageMath.Matrix.Multiply*"></a>
  <h4 id="VRageMath_Matrix_Multiply_VRageMath_Matrix__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Multiply(VRageMath.Matrix@,System.Single,VRageMath.Matrix@)">Multiply(ref Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Matrix matrix1, float scaleFactor, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Multiply_" data-uid="VRageMath.Matrix.Multiply*"></a>
  <h4 id="VRageMath_Matrix_Multiply_VRageMath_Matrix__VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Multiply(VRageMath.Matrix@,VRageMath.Matrix@,VRageMath.Matrix@)">Multiply(ref Matrix, ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Matrix matrix1, ref Matrix matrix2, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_MultiplyRotation_" data-uid="VRageMath.Matrix.MultiplyRotation*"></a>
  <h4 id="VRageMath_Matrix_MultiplyRotation_VRageMath_Matrix__VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.MultiplyRotation(VRageMath.Matrix@,VRageMath.Matrix@,VRageMath.Matrix@)">MultiplyRotation(ref Matrix, ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix, only rotation parts.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void MultiplyRotation(ref Matrix matrix1, ref Matrix matrix2, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Negate_" data-uid="VRageMath.Matrix.Negate*"></a>
  <h4 id="VRageMath_Matrix_Negate_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Negate(VRageMath.Matrix)">Negate(Matrix)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Negate(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Negate_" data-uid="VRageMath.Matrix.Negate*"></a>
  <h4 id="VRageMath_Matrix_Negate_VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Negate(VRageMath.Matrix@,VRageMath.Matrix@)">Negate(ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Matrix matrix, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Negated matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Normalize_" data-uid="VRageMath.Matrix.Normalize*"></a>
  <h4 id="VRageMath_Matrix_Normalize_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Normalize(VRageMath.Matrix)">Normalize(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Normalize(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Orthogonalize_" data-uid="VRageMath.Matrix.Orthogonalize*"></a>
  <h4 id="VRageMath_Matrix_Orthogonalize_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Orthogonalize(VRageMath.Matrix)">Orthogonalize(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Orthogonalize(Matrix rotationMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">rotationMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Rescale_" data-uid="VRageMath.Matrix.Rescale*"></a>
  <h4 id="VRageMath_Matrix_Rescale_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.Rescale(VRageMath.Matrix,System.Single)">Rescale(Matrix, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Rescale(Matrix matrix, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Rescale_" data-uid="VRageMath.Matrix.Rescale*"></a>
  <h4 id="VRageMath_Matrix_Rescale_VRageMath_Matrix_VRageMath_Vector3_" data-uid="VRageMath.Matrix.Rescale(VRageMath.Matrix,VRageMath.Vector3)">Rescale(Matrix, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Rescale(Matrix matrix, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Rescale_" data-uid="VRageMath.Matrix.Rescale*"></a>
  <h4 id="VRageMath_Matrix_Rescale_VRageMath_Matrix__System_Single_" data-uid="VRageMath.Matrix.Rescale(VRageMath.Matrix@,System.Single)">Rescale(ref Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref Matrix matrix, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Rescale_" data-uid="VRageMath.Matrix.Rescale*"></a>
  <h4 id="VRageMath_Matrix_Rescale_VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Matrix.Rescale(VRageMath.Matrix@,VRageMath.Vector3@)">Rescale(ref Matrix, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Same result as Matrix.CreateScale(scale) * matrix, but much faster</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Rescale(ref Matrix matrix, ref Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Round_" data-uid="VRageMath.Matrix.Round*"></a>
  <h4 id="VRageMath_Matrix_Round_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Round(VRageMath.Matrix@)">Round(ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Round(ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SetDirectionVector_" data-uid="VRageMath.Matrix.SetDirectionVector*"></a>
  <h4 id="VRageMath_Matrix_SetDirectionVector_VRageMath_Base6Directions_Direction_VRageMath_Vector3_" data-uid="VRageMath.Matrix.SetDirectionVector(VRageMath.Base6Directions.Direction,VRageMath.Vector3)">SetDirectionVector(Base6Directions.Direction, Vector3)</h4>
  <div class="markdown level1 summary"><p>Sets the base vector of the matrix, corresponding to the given direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDirectionVector(Base6Directions.Direction direction, Vector3 newValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">newValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SetFrom_" data-uid="VRageMath.Matrix.SetFrom*"></a>
  <h4 id="VRageMath_Matrix_SetFrom_VRageMath_MatrixD__" data-uid="VRageMath.Matrix.SetFrom(VRageMath.MatrixD@)">SetFrom(in MatrixD)</h4>
  <div class="markdown level1 summary"><p>Set this ma</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetFrom(in MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SetRotationAndScale_" data-uid="VRageMath.Matrix.SetRotationAndScale*"></a>
  <h4 id="VRageMath_Matrix_SetRotationAndScale_VRageMath_MatrixD__" data-uid="VRageMath.Matrix.SetRotationAndScale(VRageMath.MatrixD@)">SetRotationAndScale(in MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRotationAndScale(in MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SetRow_" data-uid="VRageMath.Matrix.SetRow*"></a>
  <h4 id="VRageMath_Matrix_SetRow_System_Int32_VRageMath_Vector4_" data-uid="VRageMath.Matrix.SetRow(System.Int32,VRageMath.Vector4)">SetRow(Int32, Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetRow(int row, Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">row</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Slerp_" data-uid="VRageMath.Matrix.Slerp*"></a>
  <h4 id="VRageMath_Matrix_Slerp_VRageMath_Matrix_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.Slerp(VRageMath.Matrix,VRageMath.Matrix,System.Single)">Slerp(Matrix, Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Slerp(Matrix matrix1, Matrix matrix2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Slerp_" data-uid="VRageMath.Matrix.Slerp*"></a>
  <h4 id="VRageMath_Matrix_Slerp_VRageMath_Matrix_VRageMath_Matrix_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Slerp(VRageMath.Matrix,VRageMath.Matrix,System.Single,VRageMath.Matrix@)">Slerp(Matrix, Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(Matrix matrix1, Matrix matrix2, float amount, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Slerp_" data-uid="VRageMath.Matrix.Slerp*"></a>
  <h4 id="VRageMath_Matrix_Slerp_VRageMath_Matrix__VRageMath_Matrix__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.Slerp(VRageMath.Matrix@,VRageMath.Matrix@,System.Single,VRageMath.Matrix@)">Slerp(ref Matrix, ref Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(ref Matrix matrix1, ref Matrix matrix2, float amount, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SlerpScale_" data-uid="VRageMath.Matrix.SlerpScale*"></a>
  <h4 id="VRageMath_Matrix_SlerpScale_VRageMath_Matrix_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.SlerpScale(VRageMath.Matrix,VRageMath.Matrix,System.Single)">SlerpScale(Matrix, Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix SlerpScale(Matrix matrix1, Matrix matrix2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SlerpScale_" data-uid="VRageMath.Matrix.SlerpScale*"></a>
  <h4 id="VRageMath_Matrix_SlerpScale_VRageMath_Matrix_VRageMath_Matrix_System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.SlerpScale(VRageMath.Matrix,VRageMath.Matrix,System.Single,VRageMath.Matrix@)">SlerpScale(Matrix, Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SlerpScale(Matrix matrix1, Matrix matrix2, float amount, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SlerpScale_" data-uid="VRageMath.Matrix.SlerpScale*"></a>
  <h4 id="VRageMath_Matrix_SlerpScale_VRageMath_Matrix__VRageMath_Matrix__System_Single_VRageMath_Matrix__" data-uid="VRageMath.Matrix.SlerpScale(VRageMath.Matrix@,VRageMath.Matrix@,System.Single,VRageMath.Matrix@)">SlerpScale(ref Matrix, ref Matrix, Single, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Performs spherical linear interpolation of position and rotation and scale.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SlerpScale(ref Matrix matrix1, ref Matrix matrix2, float amount, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Subtract_" data-uid="VRageMath.Matrix.Subtract*"></a>
  <h4 id="VRageMath_Matrix_Subtract_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Subtract(VRageMath.Matrix,VRageMath.Matrix)">Subtract(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Subtract(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Subtract_" data-uid="VRageMath.Matrix.Subtract*"></a>
  <h4 id="VRageMath_Matrix_Subtract_VRageMath_Matrix__VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Subtract(VRageMath.Matrix@,VRageMath.Matrix@,VRageMath.Matrix@)">Subtract(ref Matrix, ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Matrix matrix1, ref Matrix matrix2, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_SwapYZCoordinates_" data-uid="VRageMath.Matrix.SwapYZCoordinates*"></a>
  <h4 id="VRageMath_Matrix_SwapYZCoordinates_VRageMath_Matrix_" data-uid="VRageMath.Matrix.SwapYZCoordinates(VRageMath.Matrix)">SwapYZCoordinates(Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix SwapYZCoordinates(Matrix m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">m</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_ToString_" data-uid="VRageMath.Matrix.ToString*"></a>
  <h4 id="VRageMath_Matrix_ToString" data-uid="VRageMath.Matrix.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Transform_" data-uid="VRageMath.Matrix.Transform*"></a>
  <h4 id="VRageMath_Matrix_Transform_VRageMath_Matrix_VRageMath_Quaternion_" data-uid="VRageMath.Matrix.Transform(VRageMath.Matrix,VRageMath.Quaternion)">Transform(Matrix, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Matrix by applying a Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Transform(Matrix value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Matrix to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply, expressed as a Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Transform_" data-uid="VRageMath.Matrix.Transform*"></a>
  <h4 id="VRageMath_Matrix_Transform_VRageMath_Matrix__VRageMath_Quaternion__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Transform(VRageMath.Matrix@,VRageMath.Quaternion@,VRageMath.Matrix@)">Transform(ref Matrix, ref Quaternion, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a Matrix by applying a Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Matrix value, ref Quaternion rotation, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Matrix to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply, expressed as a Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Matrix filled in with the result of the transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Transpose_" data-uid="VRageMath.Matrix.Transpose*"></a>
  <h4 id="VRageMath_Matrix_Transpose_VRageMath_Matrix_" data-uid="VRageMath.Matrix.Transpose(VRageMath.Matrix)">Transpose(Matrix)</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix Transpose(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_Transpose_" data-uid="VRageMath.Matrix.Transpose*"></a>
  <h4 id="VRageMath_Matrix_Transpose_VRageMath_Matrix__VRageMath_Matrix__" data-uid="VRageMath.Matrix.Transpose(VRageMath.Matrix@,VRageMath.Matrix@)">Transpose(ref Matrix, out Matrix)</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transpose(ref Matrix matrix, out Matrix result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Transposed matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_TransposeRotationInPlace_" data-uid="VRageMath.Matrix.TransposeRotationInPlace*"></a>
  <h4 id="VRageMath_Matrix_TransposeRotationInPlace" data-uid="VRageMath.Matrix.TransposeRotationInPlace">TransposeRotationInPlace()</h4>
  <div class="markdown level1 summary"><p>Transposes the rows and columns of a matrix that is assumed to be rotation only in place.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TransposeRotationInPlace()</code></pre>
  </div>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Matrix_op_Addition_" data-uid="VRageMath.Matrix.op_Addition*"></a>
  <h4 id="VRageMath_Matrix_op_Addition_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Addition(VRageMath.Matrix,VRageMath.Matrix)">Addition(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Adds a matrix to another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator +(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Division_" data-uid="VRageMath.Matrix.op_Division*"></a>
  <h4 id="VRageMath_Matrix_op_Division_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.op_Division(VRageMath.Matrix,System.Single)">Division(Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator /(Matrix matrix1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Division_" data-uid="VRageMath.Matrix.op_Division*"></a>
  <h4 id="VRageMath_Matrix_op_Division_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Division(VRageMath.Matrix,VRageMath.Matrix)">Division(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a matrix by the corresponding components of another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator /(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Equality_" data-uid="VRageMath.Matrix.op_Equality*"></a>
  <h4 id="VRageMath_Matrix_op_Equality_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Equality(VRageMath.Matrix,VRageMath.Matrix)">Equality(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Compares a matrix for equality with another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Inequality_" data-uid="VRageMath.Matrix.op_Inequality*"></a>
  <h4 id="VRageMath_Matrix_op_Inequality_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Inequality(VRageMath.Matrix,VRageMath.Matrix)">Inequality(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Tests a matrix for inequality with another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>The matrix on the left of the equal sign.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>The matrix on the right of the equal sign.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Multiply_" data-uid="VRageMath.Matrix.op_Multiply*"></a>
  <h4 id="VRageMath_Matrix_op_Multiply_System_Single_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Multiply(System.Single,VRageMath.Matrix)">Multiply(Single, Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator *(float scaleFactor, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Multiply_" data-uid="VRageMath.Matrix.op_Multiply*"></a>
  <h4 id="VRageMath_Matrix_op_Multiply_VRageMath_Matrix_System_Single_" data-uid="VRageMath.Matrix.op_Multiply(VRageMath.Matrix,System.Single)">Multiply(Matrix, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator *(Matrix matrix, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Multiply_" data-uid="VRageMath.Matrix.op_Multiply*"></a>
  <h4 id="VRageMath_Matrix_op_Multiply_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Multiply(VRageMath.Matrix,VRageMath.Matrix)">Multiply(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Multiplies a matrix by another matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator *(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_Subtraction_" data-uid="VRageMath.Matrix.op_Subtraction*"></a>
  <h4 id="VRageMath_Matrix_op_Subtraction_VRageMath_Matrix_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_Subtraction(VRageMath.Matrix,VRageMath.Matrix)">Subtraction(Matrix, Matrix)</h4>
  <div class="markdown level1 summary"><p>Subtracts matrices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator -(Matrix matrix1, Matrix matrix2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix2</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Matrix_op_UnaryNegation_" data-uid="VRageMath.Matrix.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Matrix_op_UnaryNegation_VRageMath_Matrix_" data-uid="VRageMath.Matrix.op_UnaryNegation(VRageMath.Matrix)">UnaryNegation(Matrix)</h4>
  <div class="markdown level1 summary"><p>Negates individual elements of a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Matrix operator -(Matrix matrix1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix1</span></td>
        <td><p>Source matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
