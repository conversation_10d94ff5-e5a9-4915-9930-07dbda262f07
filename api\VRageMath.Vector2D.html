﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Vector2D
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Vector2D
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Vector2D">
  
  
  <h1 id="VRageMath_Vector2D" data-uid="VRageMath.Vector2D" class="text-break">Class Vector2D
  </h1>
  <div class="markdown level0 summary"><p>Defines a vector with two components.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Vector2D</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Vector2D_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Vector2D : ValueType, IEquatable&lt;Vector2D&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Vector2D__ctor_" data-uid="VRageMath.Vector2D.#ctor*"></a>
  <h4 id="VRageMath_Vector2D__ctor_System_Double_" data-uid="VRageMath.Vector2D.#ctor(System.Double)">Vector2D(Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Vector2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D(double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value to initialize both components to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D__ctor_" data-uid="VRageMath.Vector2D.#ctor*"></a>
  <h4 id="VRageMath_Vector2D__ctor_System_Double_System_Double_" data-uid="VRageMath.Vector2D.#ctor(System.Double,System.Double)">Vector2D(Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2D(double x, double y)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>Initial value for the x-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>Initial value for the y-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Vector2D_One" data-uid="VRageMath.Vector2D.One">One</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D One</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_PositiveInfinity" data-uid="VRageMath.Vector2D.PositiveInfinity">PositiveInfinity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D PositiveInfinity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_UnitX" data-uid="VRageMath.Vector2D.UnitX">UnitX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D UnitX</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_UnitY" data-uid="VRageMath.Vector2D.UnitY">UnitY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D UnitY</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_X" data-uid="VRageMath.Vector2D.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the x-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_Y" data-uid="VRageMath.Vector2D.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the y-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2D_Zero" data-uid="VRageMath.Vector2D.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Vector2D_Item_" data-uid="VRageMath.Vector2D.Item*"></a>
  <h4 id="VRageMath_Vector2D_Item_System_Int32_" data-uid="VRageMath.Vector2D.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double this[int index] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Vector2D_Add_" data-uid="VRageMath.Vector2D.Add*"></a>
  <h4 id="VRageMath_Vector2D_Add_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Add(VRageMath.Vector2D,VRageMath.Vector2D)">Add(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Add(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Add_" data-uid="VRageMath.Vector2D.Add*"></a>
  <h4 id="VRageMath_Vector2D_Add_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Add(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Add(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Sum of the source vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_AssertIsValid_" data-uid="VRageMath.Vector2D.AssertIsValid*"></a>
  <h4 id="VRageMath_Vector2D_AssertIsValid" data-uid="VRageMath.Vector2D.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector2D_Barycentric_" data-uid="VRageMath.Vector2D.Barycentric*"></a>
  <h4 id="VRageMath_Vector2D_Barycentric_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_System_Double_System_Double_" data-uid="VRageMath.Vector2D.Barycentric(VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D,System.Double,System.Double)">Barycentric(Vector2D, Vector2D, Vector2D, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector2D containing the 2D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 2D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Barycentric(Vector2D value1, Vector2D value2, Vector2D value3, double amount1, double amount2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Barycentric_" data-uid="VRageMath.Vector2D.Barycentric*"></a>
  <h4 id="VRageMath_Vector2D_Barycentric_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__System_Double_System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Barycentric(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double,System.Double,VRageMath.Vector2D@)">Barycentric(ref Vector2D, ref Vector2D, ref Vector2D, Double, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector2D containing the 2D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 2D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(ref Vector2D value1, ref Vector2D value2, ref Vector2D value3, double amount1, double amount2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector2D containing the 2D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The 2D Cartesian coordinates of the specified point are placed in this Vector2D on exit.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Between_" data-uid="VRageMath.Vector2D.Between*"></a>
  <h4 id="VRageMath_Vector2D_Between_VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Between(VRageMath.Vector2D@,VRageMath.Vector2D@)">Between(ref Vector2D, ref Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Between(ref Vector2D start, ref Vector2D end)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">end</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_CatmullRom_" data-uid="VRageMath.Vector2D.CatmullRom*"></a>
  <h4 id="VRageMath_Vector2D_CatmullRom_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.CatmullRom(VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D,System.Double)">CatmullRom(Vector2D, Vector2D, Vector2D, Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D CatmullRom(Vector2D value1, Vector2D value2, Vector2D value3, Vector2D value4, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_CatmullRom_" data-uid="VRageMath.Vector2D.CatmullRom*"></a>
  <h4 id="VRageMath_Vector2D_CatmullRom_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.CatmullRom(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">CatmullRom(ref Vector2D, ref Vector2D, ref Vector2D, ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CatmullRom(ref Vector2D value1, ref Vector2D value2, ref Vector2D value3, ref Vector2D value4, double amount, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A vector that is the result of the Catmull-Rom interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Clamp_" data-uid="VRageMath.Vector2D.Clamp*"></a>
  <h4 id="VRageMath_Vector2D_Clamp_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Clamp(VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D)">Clamp(Vector2D, Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Clamp(Vector2D value1, Vector2D min, Vector2D max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Clamp_" data-uid="VRageMath.Vector2D.Clamp*"></a>
  <h4 id="VRageMath_Vector2D_Clamp_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Clamp(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Clamp(ref Vector2D, ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Clamp(ref Vector2D value1, ref Vector2D min, ref Vector2D max, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The clamped value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_ClampToSphere_" data-uid="VRageMath.Vector2D.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector2D_ClampToSphere_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.ClampToSphere(VRageMath.Vector2D,System.Double)">ClampToSphere(Vector2D, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D ClampToSphere(Vector2D vector, double radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_ClampToSphere_" data-uid="VRageMath.Vector2D.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector2D_ClampToSphere_VRageMath_Vector2D__System_Double_" data-uid="VRageMath.Vector2D.ClampToSphere(VRageMath.Vector2D@,System.Double)">ClampToSphere(ref Vector2D, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ClampToSphere(ref Vector2D vector, double radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Distance_" data-uid="VRageMath.Vector2D.Distance*"></a>
  <h4 id="VRageMath_Vector2D_Distance_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Distance(VRageMath.Vector2D,VRageMath.Vector2D)">Distance(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Distance(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Distance_" data-uid="VRageMath.Vector2D.Distance*"></a>
  <h4 id="VRageMath_Vector2D_Distance_VRageMath_Vector2D__VRageMath_Vector2D__System_Double__" data-uid="VRageMath.Vector2D.Distance(VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double@)">Distance(ref Vector2D, ref Vector2D, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Distance(ref Vector2D value1, ref Vector2D value2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_DistanceSquared_" data-uid="VRageMath.Vector2D.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector2D_DistanceSquared_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.DistanceSquared(VRageMath.Vector2D,VRageMath.Vector2D)">DistanceSquared(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double DistanceSquared(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_DistanceSquared_" data-uid="VRageMath.Vector2D.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector2D_DistanceSquared_VRageMath_Vector2D__VRageMath_Vector2D__System_Double__" data-uid="VRageMath.Vector2D.DistanceSquared(VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double@)">DistanceSquared(ref Vector2D, ref Vector2D, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DistanceSquared(ref Vector2D value1, ref Vector2D value2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors squared.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Divide_" data-uid="VRageMath.Vector2D.Divide*"></a>
  <h4 id="VRageMath_Vector2D_Divide_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.Divide(VRageMath.Vector2D,System.Double)">Divide(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Divide(Vector2D value1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Divide_" data-uid="VRageMath.Vector2D.Divide*"></a>
  <h4 id="VRageMath_Vector2D_Divide_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Divide(VRageMath.Vector2D,VRageMath.Vector2D)">Divide(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Divide(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Divide_" data-uid="VRageMath.Vector2D.Divide*"></a>
  <h4 id="VRageMath_Vector2D_Divide_VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Divide(VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">Divide(ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector2D value1, double divider, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Divide_" data-uid="VRageMath.Vector2D.Divide*"></a>
  <h4 id="VRageMath_Vector2D_Divide_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Divide(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Divide(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Dot_" data-uid="VRageMath.Vector2D.Dot*"></a>
  <h4 id="VRageMath_Vector2D_Dot_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Dot(VRageMath.Vector2D,VRageMath.Vector2D)">Dot(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors. If the two vectors are unit vectors, the dot product returns a doubleing point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Dot(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Dot_" data-uid="VRageMath.Vector2D.Dot*"></a>
  <h4 id="VRageMath_Vector2D_Dot_VRageMath_Vector2D__VRageMath_Vector2D__System_Double__" data-uid="VRageMath.Vector2D.Dot(VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double@)">Dot(ref Vector2D, ref Vector2D, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors and writes the result to a user-specified variable. If the two vectors are unit vectors, the dot product returns a doubleing point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Vector2D value1, ref Vector2D value2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the two vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Equals_" data-uid="VRageMath.Vector2D.Equals*"></a>
  <h4 id="VRageMath_Vector2D_Equals_System_Object_" data-uid="VRageMath.Vector2D.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Equals_" data-uid="VRageMath.Vector2D.Equals*"></a>
  <h4 id="VRageMath_Vector2D_Equals_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Equals(VRageMath.Vector2D)">Equals(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Vector2D.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector2D other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Vector2D.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Floor_" data-uid="VRageMath.Vector2D.Floor*"></a>
  <h4 id="VRageMath_Vector2D_Floor_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Floor(VRageMath.Vector2D)">Floor(Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Floor(Vector2D position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_GetHashCode_" data-uid="VRageMath.Vector2D.GetHashCode*"></a>
  <h4 id="VRageMath_Vector2D_GetHashCode" data-uid="VRageMath.Vector2D.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of the vector object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Hermite_" data-uid="VRageMath.Vector2D.Hermite*"></a>
  <h4 id="VRageMath_Vector2D_Hermite_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.Hermite(VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D,VRageMath.Vector2D,System.Double)">Hermite(Vector2D, Vector2D, Vector2D, Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Hermite(Vector2D value1, Vector2D tangent1, Vector2D value2, Vector2D tangent2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Hermite_" data-uid="VRageMath.Vector2D.Hermite*"></a>
  <h4 id="VRageMath_Vector2D_Hermite_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Hermite(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">Hermite(ref Vector2D, ref Vector2D, ref Vector2D, ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Hermite(ref Vector2D value1, ref Vector2D tangent1, ref Vector2D value2, ref Vector2D tangent2, double amount, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the Hermite spline interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_IsValid_" data-uid="VRageMath.Vector2D.IsValid*"></a>
  <h4 id="VRageMath_Vector2D_IsValid" data-uid="VRageMath.Vector2D.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Length_" data-uid="VRageMath.Vector2D.Length*"></a>
  <h4 id="VRageMath_Vector2D_Length" data-uid="VRageMath.Vector2D.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_LengthSquared_" data-uid="VRageMath.Vector2D.LengthSquared*"></a>
  <h4 id="VRageMath_Vector2D_LengthSquared" data-uid="VRageMath.Vector2D.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Lerp_" data-uid="VRageMath.Vector2D.Lerp*"></a>
  <h4 id="VRageMath_Vector2D_Lerp_VRageMath_Vector2D_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.Lerp(VRageMath.Vector2D,VRageMath.Vector2D,System.Double)">Lerp(Vector2D, Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Lerp(Vector2D value1, Vector2D value2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Lerp_" data-uid="VRageMath.Vector2D.Lerp*"></a>
  <h4 id="VRageMath_Vector2D_Lerp_VRageMath_Vector2D__VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Lerp(VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">Lerp(ref Vector2D, ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Vector2D value1, ref Vector2D value2, double amount, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Max_" data-uid="VRageMath.Vector2D.Max*"></a>
  <h4 id="VRageMath_Vector2D_Max_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Max(VRageMath.Vector2D,VRageMath.Vector2D)">Max(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Max(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Max_" data-uid="VRageMath.Vector2D.Max*"></a>
  <h4 id="VRageMath_Vector2D_Max_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Max(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Max(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Max(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The maximized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Min_" data-uid="VRageMath.Vector2D.Min*"></a>
  <h4 id="VRageMath_Vector2D_Min_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Min(VRageMath.Vector2D,VRageMath.Vector2D)">Min(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Min(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Min_" data-uid="VRageMath.Vector2D.Min*"></a>
  <h4 id="VRageMath_Vector2D_Min_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Min(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Min(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Min(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The minimized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Multiply_" data-uid="VRageMath.Vector2D.Multiply*"></a>
  <h4 id="VRageMath_Vector2D_Multiply_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.Multiply(VRageMath.Vector2D,System.Double)">Multiply(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Multiply(Vector2D value1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Multiply_" data-uid="VRageMath.Vector2D.Multiply*"></a>
  <h4 id="VRageMath_Vector2D_Multiply_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Multiply(VRageMath.Vector2D,VRageMath.Vector2D)">Multiply(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Multiply(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Multiply_" data-uid="VRageMath.Vector2D.Multiply*"></a>
  <h4 id="VRageMath_Vector2D_Multiply_VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Multiply(VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">Multiply(ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector2D value1, double scaleFactor, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Multiply_" data-uid="VRageMath.Vector2D.Multiply*"></a>
  <h4 id="VRageMath_Vector2D_Multiply_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Multiply(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Multiply(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Negate_" data-uid="VRageMath.Vector2D.Negate*"></a>
  <h4 id="VRageMath_Vector2D_Negate_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Negate(VRageMath.Vector2D)">Negate(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Negate(Vector2D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Negate_" data-uid="VRageMath.Vector2D.Negate*"></a>
  <h4 id="VRageMath_Vector2D_Negate_VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Negate(VRageMath.Vector2D@,VRageMath.Vector2D@)">Negate(ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Vector2D value, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Vector pointing in the opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Normalize_" data-uid="VRageMath.Vector2D.Normalize*"></a>
  <h4 id="VRageMath_Vector2D_Normalize" data-uid="VRageMath.Vector2D.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Turns the current vector into a unit vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector2D_Normalize_" data-uid="VRageMath.Vector2D.Normalize*"></a>
  <h4 id="VRageMath_Vector2D_Normalize_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Normalize(VRageMath.Vector2D)">Normalize(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Normalize(Vector2D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source Vector2D.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Normalize_" data-uid="VRageMath.Vector2D.Normalize*"></a>
  <h4 id="VRageMath_Vector2D_Normalize_VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Normalize(VRageMath.Vector2D@,VRageMath.Vector2D@)">Normalize(ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector, writing the result to a user-specified variable. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector2D value, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Normalized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Reflect_" data-uid="VRageMath.Vector2D.Reflect*"></a>
  <h4 id="VRageMath_Vector2D_Reflect_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Reflect(VRageMath.Vector2D,VRageMath.Vector2D)">Reflect(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Determines the reflect vector of the given vector and normal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Reflect(Vector2D vector, Vector2D normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Reflect_" data-uid="VRageMath.Vector2D.Reflect*"></a>
  <h4 id="VRageMath_Vector2D_Reflect_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Reflect(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Reflect(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Determines the reflect vector of the given vector and normal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Reflect(ref Vector2D vector, ref Vector2D normal, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created reflect vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Rotate_" data-uid="VRageMath.Vector2D.Rotate*"></a>
  <h4 id="VRageMath_Vector2D_Rotate_System_Double_" data-uid="VRageMath.Vector2D.Rotate(System.Double)">Rotate(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Rotate(double angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_SmoothStep_" data-uid="VRageMath.Vector2D.SmoothStep*"></a>
  <h4 id="VRageMath_Vector2D_SmoothStep_VRageMath_Vector2D_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.SmoothStep(VRageMath.Vector2D,VRageMath.Vector2D,System.Double)">SmoothStep(Vector2D, Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D SmoothStep(Vector2D value1, Vector2D value2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_SmoothStep_" data-uid="VRageMath.Vector2D.SmoothStep*"></a>
  <h4 id="VRageMath_Vector2D_SmoothStep_VRageMath_Vector2D__VRageMath_Vector2D__System_Double_VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.SmoothStep(VRageMath.Vector2D@,VRageMath.Vector2D@,System.Double,VRageMath.Vector2D@)">SmoothStep(ref Vector2D, ref Vector2D, Double, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SmoothStep(ref Vector2D value1, ref Vector2D value2, double amount, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The interpolated value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Subtract_" data-uid="VRageMath.Vector2D.Subtract*"></a>
  <h4 id="VRageMath_Vector2D_Subtract_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.Subtract(VRageMath.Vector2D,VRageMath.Vector2D)">Subtract(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Subtract(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Subtract_" data-uid="VRageMath.Vector2D.Subtract*"></a>
  <h4 id="VRageMath_Vector2D_Subtract_VRageMath_Vector2D__VRageMath_Vector2D__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Subtract(VRageMath.Vector2D@,VRageMath.Vector2D@,VRageMath.Vector2D@)">Subtract(ref Vector2D, ref Vector2D, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Vector2D value1, ref Vector2D value2, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_ToString_" data-uid="VRageMath.Vector2D.ToString*"></a>
  <h4 id="VRageMath_Vector2D_ToString" data-uid="VRageMath.Vector2D.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D_VRageMath_Matrix_" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D,VRageMath.Matrix)">Transform(Vector2D, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms the vector (x, y, 0, 1) by the specified matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Transform(Vector2D position, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D_VRageMath_Quaternion_" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D,VRageMath.Quaternion)">Transform(Vector2D, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a single Vector2D, or the vector normal (x, y, 0, 0), by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D Transform(Vector2D value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The vector to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D__VRageMath_Matrix__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D@,VRageMath.Matrix@,VRageMath.Vector2D@)">Transform(ref Vector2D, ref Matrix, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2D by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2D position, ref Matrix matrix, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2D.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector2D resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D__VRageMath_Quaternion__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D@,VRageMath.Quaternion@,VRageMath.Vector2D@)">Transform(ref Vector2D, ref Quaternion, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2D, or the vector normal (x, y, 0, 0), by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2D value, ref Quaternion rotation, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The vector to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Vector2D filled in with the result of the rotation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D___System_Int32_VRageMath_Matrix__VRageMath_Vector2D___System_Int32_System_Int32_" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D[],System.Int32,VRageMath.Matrix@,VRageMath.Vector2D[],System.Int32,System.Int32)">Transform(Vector2D[], Int32, ref Matrix, Vector2D[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2s by a specified Matrix and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2D[] sourceArray, int sourceIndex, ref Matrix matrix, Vector2D[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2D to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The Matrix to transform by.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s will be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2D should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector2s to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D___System_Int32_VRageMath_Quaternion__VRageMath_Vector2D___System_Int32_System_Int32_" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D[],System.Int32,VRageMath.Quaternion@,VRageMath.Vector2D[],System.Int32,System.Int32)">Transform(Vector2D[], Int32, ref Quaternion, Vector2D[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2s by a specified Quaternion and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2D[] sourceArray, int sourceIndex, ref Quaternion rotation, Vector2D[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2D to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s are written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2D should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector2s to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D___VRageMath_Matrix__VRageMath_Vector2D___" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D[],VRageMath.Matrix@,VRageMath.Vector2D[])">Transform(Vector2D[], ref Matrix, Vector2D[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2s by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2D[] sourceArray, ref Matrix matrix, Vector2D[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector2s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed Vector2s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_Transform_" data-uid="VRageMath.Vector2D.Transform*"></a>
  <h4 id="VRageMath_Vector2D_Transform_VRageMath_Vector2D___VRageMath_Quaternion__VRageMath_Vector2D___" data-uid="VRageMath.Vector2D.Transform(VRageMath.Vector2D[],VRageMath.Quaternion@,VRageMath.Vector2D[])">Transform(Vector2D[], ref Quaternion, Vector2D[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2s by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2D[] sourceArray, ref Quaternion rotation, Vector2D[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector2s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The transform Matrix to use.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed Vector2s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_TransformNormal_" data-uid="VRageMath.Vector2D.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2D_TransformNormal_VRageMath_Vector2D_VRageMath_Matrix_" data-uid="VRageMath.Vector2D.TransformNormal(VRageMath.Vector2D,VRageMath.Matrix)">TransformNormal(Vector2D, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a 2D vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D TransformNormal(Vector2D normal, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_TransformNormal_" data-uid="VRageMath.Vector2D.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2D_TransformNormal_VRageMath_Vector2D__VRageMath_Matrix__VRageMath_Vector2D__" data-uid="VRageMath.Vector2D.TransformNormal(VRageMath.Vector2D@,VRageMath.Matrix@,VRageMath.Vector2D@)">TransformNormal(ref Vector2D, ref Matrix, out Vector2D)</h4>
  <div class="markdown level1 summary"><p>Transforms a vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector2D normal, ref Matrix matrix, out Vector2D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector2D resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_TransformNormal_" data-uid="VRageMath.Vector2D.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2D_TransformNormal_VRageMath_Vector2D___System_Int32_VRageMath_Matrix__VRageMath_Vector2D___System_Int32_System_Int32_" data-uid="VRageMath.Vector2D.TransformNormal(VRageMath.Vector2D[],System.Int32,VRageMath.Matrix@,VRageMath.Vector2D[],System.Int32,System.Int32)">TransformNormal(Vector2D[], Int32, ref Matrix, Vector2D[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2D vector normals by a specified Matrix and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector2D[] sourceArray, int sourceIndex, ref Matrix matrix, Vector2D[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2D to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s are written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2D should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of vector normals to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_TransformNormal_" data-uid="VRageMath.Vector2D.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2D_TransformNormal_VRageMath_Vector2D___VRageMath_Matrix__VRageMath_Vector2D___" data-uid="VRageMath.Vector2D.TransformNormal(VRageMath.Vector2D[],VRageMath.Matrix@,VRageMath.Vector2D[])">TransformNormal(Vector2D[], ref Matrix, Vector2D[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2D vector normals by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector2D[] sourceArray, ref Matrix matrix, Vector2D[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of vector normals to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed vector normals are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Vector2D_op_Addition_" data-uid="VRageMath.Vector2D.op_Addition*"></a>
  <h4 id="VRageMath_Vector2D_op_Addition_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.op_Addition(VRageMath.Vector2D,System.Double)">Addition(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Adds double to each component of a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator +(Vector2D value1, double value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source double.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Addition_" data-uid="VRageMath.Vector2D.op_Addition*"></a>
  <h4 id="VRageMath_Vector2D_op_Addition_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Addition(VRageMath.Vector2D,VRageMath.Vector2D)">Addition(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator +(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Division_" data-uid="VRageMath.Vector2D.op_Division*"></a>
  <h4 id="VRageMath_Vector2D_op_Division_System_Double_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Division(System.Double,VRageMath.Vector2D)">Division(Double, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Divides a scalar value by a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator /(double value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Division_" data-uid="VRageMath.Vector2D.op_Division*"></a>
  <h4 id="VRageMath_Vector2D_op_Division_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.op_Division(VRageMath.Vector2D,System.Double)">Division(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator /(Vector2D value1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Division_" data-uid="VRageMath.Vector2D.op_Division*"></a>
  <h4 id="VRageMath_Vector2D_op_Division_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Division(VRageMath.Vector2D,VRageMath.Vector2D)">Division(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator /(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Equality_" data-uid="VRageMath.Vector2D.op_Equality*"></a>
  <h4 id="VRageMath_Vector2D_op_Equality_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Equality(VRageMath.Vector2D,VRageMath.Vector2D)">Equality(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Explicit_" data-uid="VRageMath.Vector2D.op_Explicit*"></a>
  <h4 id="VRageMath_Vector2D_op_Explicit_VRageMath_Vector2D__VRageMath_Vector2" data-uid="VRageMath.Vector2D.op_Explicit(VRageMath.Vector2D)~VRageMath.Vector2">Explicit(Vector2D to Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator Vector2(Vector2D vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Explicit_" data-uid="VRageMath.Vector2D.op_Explicit*"></a>
  <h4 id="VRageMath_Vector2D_op_Explicit_VRageMath_Vector2D__VRageMath_Vector2I" data-uid="VRageMath.Vector2D.op_Explicit(VRageMath.Vector2D)~VRageMath.Vector2I">Explicit(Vector2D to Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator Vector2I(Vector2D vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Implicit_" data-uid="VRageMath.Vector2D.op_Implicit*"></a>
  <h4 id="VRageMath_Vector2D_op_Implicit_VRageMath_Vector2__VRageMath_Vector2D" data-uid="VRageMath.Vector2D.op_Implicit(VRageMath.Vector2)~VRageMath.Vector2D">Implicit(Vector2 to Vector2D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Vector2D(Vector2 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Inequality_" data-uid="VRageMath.Vector2D.op_Inequality*"></a>
  <h4 id="VRageMath_Vector2D_op_Inequality_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Inequality(VRageMath.Vector2D,VRageMath.Vector2D)">Inequality(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Multiply_" data-uid="VRageMath.Vector2D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2D_op_Multiply_System_Double_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Multiply(System.Double,VRageMath.Vector2D)">Multiply(Double, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator *(double scaleFactor, Vector2D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Multiply_" data-uid="VRageMath.Vector2D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2D_op_Multiply_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.op_Multiply(VRageMath.Vector2D,System.Double)">Multiply(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator *(Vector2D value, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Multiply_" data-uid="VRageMath.Vector2D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2D_op_Multiply_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Multiply(VRageMath.Vector2D,VRageMath.Vector2D)">Multiply(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator *(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Subtraction_" data-uid="VRageMath.Vector2D.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector2D_op_Subtraction_VRageMath_Vector2D_System_Double_" data-uid="VRageMath.Vector2D.op_Subtraction(VRageMath.Vector2D,System.Double)">Subtraction(Vector2D, Double)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator -(Vector2D value1, double value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_Subtraction_" data-uid="VRageMath.Vector2D.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector2D_op_Subtraction_VRageMath_Vector2D_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_Subtraction(VRageMath.Vector2D,VRageMath.Vector2D)">Subtraction(Vector2D, Vector2D)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator -(Vector2D value1, Vector2D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2D_op_UnaryNegation_" data-uid="VRageMath.Vector2D.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Vector2D_op_UnaryNegation_VRageMath_Vector2D_" data-uid="VRageMath.Vector2D.op_UnaryNegation(VRageMath.Vector2D)">UnaryNegation(Vector2D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2D operator -(Vector2D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2D.html">Vector2D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
