﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyDynamicAABBTree
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyDynamicAABBTree
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.MyDynamicAABBTree">
  
  
  <h1 id="VRageMath_MyDynamicAABBTree" data-uid="VRageMath.MyDynamicAABBTree" class="text-break">Class MyDynamicAABBTree
  </h1>
  <div class="markdown level0 summary"><p>Dynamic aabb tree implementation as a prunning structure</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyDynamicAABBTree</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_MyDynamicAABBTree_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyDynamicAABBTree : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTree__ctor_" data-uid="VRageMath.MyDynamicAABBTree.#ctor*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree__ctor" data-uid="VRageMath.MyDynamicAABBTree.#ctor">MyDynamicAABBTree()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyDynamicAABBTree()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTree__ctor_" data-uid="VRageMath.MyDynamicAABBTree.#ctor*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree__ctor_VRageMath_Vector3_System_Single_" data-uid="VRageMath.MyDynamicAABBTree.#ctor(VRageMath.Vector3,System.Single)">MyDynamicAABBTree(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyDynamicAABBTree(Vector3 extension, float aabbMultiplier = 1F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">extension</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">aabbMultiplier</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_MyDynamicAABBTree_NullNode" data-uid="VRageMath.MyDynamicAABBTree.NullNode">NullNode</h4>
  <div class="markdown level1 summary"><p>A dynamic tree arranges data in a binary tree to accelerate
queries such as volume queries and ray casts. Leafs are proxies
with an BoundingBox. In the tree we expand the proxy BoundingBox by Settings.b2_fatAABBFactor
so that the proxy BoundingBox is bigger than the client object. This allows the client
object to move by small amounts without triggering a tree update.
Nodes are pooled and relocatable, so we use node indices rather than pointers.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int NullNode = -1</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Leaves_" data-uid="VRageMath.MyDynamicAABBTree.Leaves*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Leaves" data-uid="VRageMath.MyDynamicAABBTree.Leaves">Leaves</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DictionaryValuesReader&lt;int, MyDynamicAABBTree.DynamicTreeNode&gt; Leaves { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Collections.DictionaryValuesReader-2.html">DictionaryValuesReader</a>&lt;<span class="xref">System.Int32</span>, <a class="xref" href="VRageMath.MyDynamicAABBTree.DynamicTreeNode.html">MyDynamicAABBTree.DynamicTreeNode</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Nodes_" data-uid="VRageMath.MyDynamicAABBTree.Nodes*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Nodes" data-uid="VRageMath.MyDynamicAABBTree.Nodes">Nodes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ReadOnlySpan&lt;MyDynamicAABBTree.DynamicTreeNode&gt; Nodes { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.ReadOnlySpan</span>&lt;<a class="xref" href="VRageMath.MyDynamicAABBTree.DynamicTreeNode.html">MyDynamicAABBTree.DynamicTreeNode</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTree_AddProxy_" data-uid="VRageMath.MyDynamicAABBTree.AddProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_AddProxy_VRageMath_BoundingBox__System_Object_System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.AddProxy(VRageMath.BoundingBox@,System.Object,System.UInt32,System.Boolean)">AddProxy(ref BoundingBox, Object, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"><p>Create a proxy. Provide a tight fitting BoundingBox and a userData pointer.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int AddProxy(ref BoundingBox aabb, object userData, uint userFlags, bool rebalance = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">aabb</span></td>
        <td><p>The aabb.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">userData</span></td>
        <td><p>The user data.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">userFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">rebalance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Balance_" data-uid="VRageMath.MyDynamicAABBTree.Balance*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Balance_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.Balance(System.Int32)">Balance(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Balance(int iA)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">iA</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Clear_" data-uid="VRageMath.MyDynamicAABBTree.Clear*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Clear" data-uid="VRageMath.MyDynamicAABBTree.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTree_CountLeaves_" data-uid="VRageMath.MyDynamicAABBTree.CountLeaves*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_CountLeaves_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.CountLeaves(System.Int32)">CountLeaves(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int CountLeaves(int nodeId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">nodeId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Dispose_" data-uid="VRageMath.MyDynamicAABBTree.Dispose*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Dispose" data-uid="VRageMath.MyDynamicAABBTree.Dispose">Dispose()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dispose()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetAabb_" data-uid="VRageMath.MyDynamicAABBTree.GetAabb*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetAabb_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.GetAabb(System.Int32)">GetAabb(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox GetAabb(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetAll_" data-uid="VRageMath.MyDynamicAABBTree.GetAll*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetAll__1_System_Collections_Generic_List___0__System_Boolean_System_Collections_Generic_List_VRageMath_BoundingBox__" data-uid="VRageMath.MyDynamicAABBTree.GetAll``1(System.Collections.Generic.List{``0},System.Boolean,System.Collections.Generic.List{VRageMath.BoundingBox})">GetAll&lt;T&gt;(List&lt;T&gt;, Boolean, List&lt;BoundingBox&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAll&lt;T&gt;(List&lt;T&gt; elementsList, bool clear, List&lt;BoundingBox&gt; boxsList = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a>&gt;</td>
        <td><span class="parametername">boxsList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetAllNodeBounds_" data-uid="VRageMath.MyDynamicAABBTree.GetAllNodeBounds*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetAllNodeBounds_System_Collections_Generic_List_VRageMath_BoundingBox__" data-uid="VRageMath.MyDynamicAABBTree.GetAllNodeBounds(System.Collections.Generic.List{VRageMath.BoundingBox})">GetAllNodeBounds(List&lt;BoundingBox&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAllNodeBounds(List&lt;BoundingBox&gt; boxsList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a>&gt;</td>
        <td><span class="parametername">boxsList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetChildren_" data-uid="VRageMath.MyDynamicAABBTree.GetChildren*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetChildren_System_Int32_System_Int32__System_Int32__" data-uid="VRageMath.MyDynamicAABBTree.GetChildren(System.Int32,System.Int32@,System.Int32@)">GetChildren(Int32, out Int32, out Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetChildren(int proxyId, out int left, out int right)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">left</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">right</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetFatAABB_" data-uid="VRageMath.MyDynamicAABBTree.GetFatAABB*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetFatAABB_System_Int32_VRageMath_BoundingBox__" data-uid="VRageMath.MyDynamicAABBTree.GetFatAABB(System.Int32,VRageMath.BoundingBox@)">GetFatAABB(Int32, out BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Get the fat BoundingBox for a proxy.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetFatAABB(int proxyId, out BoundingBox fatAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">fatAABB</span></td>
        <td><p>The fat BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetHeight_" data-uid="VRageMath.MyDynamicAABBTree.GetHeight*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetHeight" data-uid="VRageMath.MyDynamicAABBTree.GetHeight">GetHeight()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetHeight()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetLeafCount_" data-uid="VRageMath.MyDynamicAABBTree.GetLeafCount*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetLeafCount" data-uid="VRageMath.MyDynamicAABBTree.GetLeafCount">GetLeafCount()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetLeafCount()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetLeafCount_" data-uid="VRageMath.MyDynamicAABBTree.GetLeafCount*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetLeafCount_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.GetLeafCount(System.Int32)">GetLeafCount(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetLeafCount(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetNodeLeaves_" data-uid="VRageMath.MyDynamicAABBTree.GetNodeLeaves*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetNodeLeaves_System_Int32_System_Collections_Generic_List_System_Int32__" data-uid="VRageMath.MyDynamicAABBTree.GetNodeLeaves(System.Int32,System.Collections.Generic.List{System.Int32})">GetNodeLeaves(Int32, List&lt;Int32&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetNodeLeaves(int proxyId, List&lt;int&gt; children)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Int32</span>&gt;</td>
        <td><span class="parametername">children</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetRoot_" data-uid="VRageMath.MyDynamicAABBTree.GetRoot*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetRoot" data-uid="VRageMath.MyDynamicAABBTree.GetRoot">GetRoot()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetRoot()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_GetUserData_" data-uid="VRageMath.MyDynamicAABBTree.GetUserData*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_GetUserData__1_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.GetUserData``1(System.Int32)">GetUserData&lt;T&gt;(Int32)</h4>
  <div class="markdown level1 summary"><p>Get proxy user data.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public T GetUserData&lt;T&gt;(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p>the proxy user data or 0 if the id is invalid.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_MoveProxy_" data-uid="VRageMath.MyDynamicAABBTree.MoveProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_MoveProxy_System_Int32_VRageMath_BoundingBox__VRageMath_Vector3_" data-uid="VRageMath.MyDynamicAABBTree.MoveProxy(System.Int32,VRageMath.BoundingBox@,VRageMath.Vector3)">MoveProxy(Int32, ref BoundingBox, Vector3)</h4>
  <div class="markdown level1 summary"><p>Move a proxy with a swepted BoundingBox. If the proxy has moved outside of its fattened BoundingBox,
then the proxy is removed from the tree and re-inserted. Otherwise
the function returns immediately.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool MoveProxy(int proxyId, ref BoundingBox aabb, Vector3 displacement)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">aabb</span></td>
        <td><p>The aabb.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">displacement</span></td>
        <td><p>The displacement.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>true if the proxy was re-inserted.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllBoundingBox_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllBoundingBox*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllBoundingBox__1_VRageMath_BoundingBox__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllBoundingBox``1(VRageMath.BoundingBox@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllBoundingBox&lt;T&gt;(ref BoundingBox, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingBox&lt;T&gt;(ref BoundingBox bbox, List&lt;T&gt; elementsList, uint requiredFlags = 0U, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllBoundingSphere_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllBoundingSphere*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllBoundingSphere__1_VRageMath_BoundingSphere__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllBoundingSphere``1(VRageMath.BoundingSphere@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphere, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphere sphere, List&lt;T&gt; overlapElementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">overlapElementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Action___0_System_Boolean__" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Action{``0,System.Boolean})">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, Action&lt;T, Boolean&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, Action&lt;T, bool&gt; add)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Action___0_System_Boolean__System_Single_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Action{``0,System.Boolean},System.Single)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, Action&lt;T, Boolean&gt;, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, Action&lt;T, bool&gt; add, float tSqr)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_Collections_Generic_List_System_Boolean__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.Collections.Generic.List{System.Boolean},System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, List&lt;Boolean&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, List&lt;bool&gt; isInsideList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">isInsideList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_Collections_Generic_List_System_Boolean__System_Single_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.Collections.Generic.List{System.Boolean},System.Single,System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, List&lt;Boolean&gt;, Single, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, List&lt;bool&gt; isInsideList, float tSqr, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">isInsideList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, uint requiredFlags, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__2_VRageMath_BoundingFrustum____1__" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``2(VRageMath.BoundingFrustum@,``1@)">OverlapAllFrustum&lt;T, Op&gt;(ref BoundingFrustum, ref Op)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T, Op&gt;(ref BoundingFrustum frustum, ref Op add)
    where Op : struct, IAddOp&lt;T&gt;, ValueType</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">Op</span></td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">Op</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustum__2_VRageMath_BoundingFrustum__System_Single___1__" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustum``2(VRageMath.BoundingFrustum@,System.Single,``1@)">OverlapAllFrustum&lt;T, Op&gt;(ref BoundingFrustum, Single, ref Op)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T, Op&gt;(ref BoundingFrustum frustum, float tSqr, ref Op add)
    where Op : struct, IAddOp&lt;T&gt;, ValueType</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">Op</span></td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">Op</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustumAny_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustumAny*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustumAny__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustumAny``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllFrustumAny&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustumAny&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllFrustumConservative_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustumConservative*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllFrustumConservative__1_VRageMath_BoundingFrustum__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllFrustumConservative``1(VRageMath.BoundingFrustum@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllFrustumConservative&lt;T&gt;(ref BoundingFrustum, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustumConservative&lt;T&gt;(ref BoundingFrustum frustum, List&lt;T&gt; elementsList, uint requiredFlags, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllLineSegment_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllLineSegment*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllLineSegment__1_VRageMath_Line__System_Collections_Generic_List_VRageMath_MyLineSegmentOverlapResult___0___" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllLineSegment``1(VRageMath.Line@,System.Collections.Generic.List{VRageMath.MyLineSegmentOverlapResult{``0}})">OverlapAllLineSegment&lt;T&gt;(ref Line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllLineSegment&lt;T&gt;(ref Line line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt; elementsList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.html">MyLineSegmentOverlapResult</a>&lt;T&gt;&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapAllLineSegment_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllLineSegment*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapAllLineSegment__1_VRageMath_Line__System_Collections_Generic_List_VRageMath_MyLineSegmentOverlapResult___0___System_UInt32_" data-uid="VRageMath.MyDynamicAABBTree.OverlapAllLineSegment``1(VRageMath.Line@,System.Collections.Generic.List{VRageMath.MyLineSegmentOverlapResult{``0}},System.UInt32)">OverlapAllLineSegment&lt;T&gt;(ref Line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt;, UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllLineSegment&lt;T&gt;(ref Line line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt; elementsList, uint requiredFlags)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.html">MyLineSegmentOverlapResult</a>&lt;T&gt;&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapsAnyLeafBoundingBox_" data-uid="VRageMath.MyDynamicAABBTree.OverlapsAnyLeafBoundingBox*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapsAnyLeafBoundingBox_VRageMath_BoundingBox__" data-uid="VRageMath.MyDynamicAABBTree.OverlapsAnyLeafBoundingBox(VRageMath.BoundingBox@)">OverlapsAnyLeafBoundingBox(ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool OverlapsAnyLeafBoundingBox(ref BoundingBox bbox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_OverlapSizeableClusters_" data-uid="VRageMath.MyDynamicAABBTree.OverlapSizeableClusters*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_OverlapSizeableClusters_VRageMath_BoundingBox__System_Collections_Generic_List_VRageMath_BoundingBox__System_Double_" data-uid="VRageMath.MyDynamicAABBTree.OverlapSizeableClusters(VRageMath.BoundingBox@,System.Collections.Generic.List{VRageMath.BoundingBox},System.Double)">OverlapSizeableClusters(ref BoundingBox, List&lt;BoundingBox&gt;, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapSizeableClusters(ref BoundingBox bbox, List&lt;BoundingBox&gt; boundList, double minSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a>&gt;</td>
        <td><span class="parametername">boundList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_Query_" data-uid="VRageMath.MyDynamicAABBTree.Query*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_Query_System_Func_System_Int32_System_Boolean__VRageMath_BoundingBox__" data-uid="VRageMath.MyDynamicAABBTree.Query(System.Func{System.Int32,System.Boolean},VRageMath.BoundingBox@)">Query(Func&lt;Int32, Boolean&gt;, ref BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Query(Func&lt;int, bool&gt; callback, ref BoundingBox aabb)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<span class="xref">System.Int32</span>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">aabb</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTree_RemoveProxy_" data-uid="VRageMath.MyDynamicAABBTree.RemoveProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTree_RemoveProxy_System_Int32_" data-uid="VRageMath.MyDynamicAABBTree.RemoveProxy(System.Int32)">RemoveProxy(Int32)</h4>
  <div class="markdown level1 summary"><p>Destroy a proxy. This asserts if the id is invalid.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveProxy(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
