﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyPrecalcComponent
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyPrecalcComponent
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Voxels.MyPrecalcComponent">
  
  
  <h1 id="VRage_Voxels_MyPrecalcComponent" data-uid="VRage.Voxels.MyPrecalcComponent" class="text-break">Class MyPrecalcComponent
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.Game.Components.MySessionComponentBase.html">MySessionComponentBase</a></div>
    <div class="level2"><span class="xref">MyPrecalcComponent</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_DebugName">MySessionComponentBase.DebugName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Priority">MySessionComponentBase.Priority</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_ComponentType">MySessionComponentBase.ComponentType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_SetUpdateOrder_VRage_Game_Components_MyUpdateOrder_">MySessionComponentBase.SetUpdateOrder(MyUpdateOrder)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_InitFromDefinition_VRage_Game_Components_Session_MySessionComponentDefinition_">MySessionComponentBase.InitFromDefinition(MySessionComponentDefinition)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Init_VRage_Game_MyObjectBuilder_SessionComponent_">MySessionComponentBase.Init(MyObjectBuilder_SessionComponent)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_GetObjectBuilder">MySessionComponentBase.GetObjectBuilder()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_AfterLoadData">MySessionComponentBase.AfterLoadData()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UnloadDataConditional">MySessionComponentBase.UnloadDataConditional()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_SaveData">MySessionComponentBase.SaveData()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_BeforeStart">MySessionComponentBase.BeforeStart()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdateBeforeSimulation">MySessionComponentBase.UpdateBeforeSimulation()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Simulate">MySessionComponentBase.Simulate()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdatingStopped">MySessionComponentBase.UpdatingStopped()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Draw">MySessionComponentBase.Draw()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_HandleInput">MySessionComponentBase.HandleInput()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_ToString">MySessionComponentBase.ToString()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdateOrder">MySessionComponentBase.UpdateOrder</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_ObjectBuilderType">MySessionComponentBase.ObjectBuilderType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_ModContext">MySessionComponentBase.ModContext</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Session">MySessionComponentBase.Session</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Loaded">MySessionComponentBase.Loaded</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Initialized">MySessionComponentBase.Initialized</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdateOnPause">MySessionComponentBase.UpdateOnPause</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_IsServerOnly">MySessionComponentBase.IsServerOnly</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Definition">MySessionComponentBase.Definition</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_Dependencies">MySessionComponentBase.Dependencies</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_IsRequiredByGame">MySessionComponentBase.IsRequiredByGame</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Voxels.html">VRage.Voxels</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Voxels_MyPrecalcComponent_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MySessionComponentDescriptor]
public class MyPrecalcComponent : MySessionComponentBase, IMyUserInputComponent</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent__ctor_" data-uid="VRage.Voxels.MyPrecalcComponent.#ctor*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent__ctor" data-uid="VRage.Voxels.MyPrecalcComponent.#ctor">MyPrecalcComponent()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyPrecalcComponent()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Voxels_MyPrecalcComponent_DebugDrawSorted" data-uid="VRage.Voxels.MyPrecalcComponent.DebugDrawSorted">DebugDrawSorted</h4>
  <div class="markdown level1 summary"><p>Enable debug draw.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DebugDrawSorted</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyPrecalcComponent_MaxPrecalcTime" data-uid="VRage.Voxels.MyPrecalcComponent.MaxPrecalcTime">MaxPrecalcTime</h4>
  <div class="markdown level1 summary"><p>Maximum calculation time in milliseconds</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static long MaxPrecalcTime</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyPrecalcComponent_UpdateThreadManagedId" data-uid="VRage.Voxels.MyPrecalcComponent.UpdateThreadManagedId">UpdateThreadManagedId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int UpdateThreadManagedId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_InvalidatedRangeInflate_" data-uid="VRage.Voxels.MyPrecalcComponent.InvalidatedRangeInflate*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_InvalidatedRangeInflate" data-uid="VRage.Voxels.MyPrecalcComponent.InvalidatedRangeInflate">InvalidatedRangeInflate</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int InvalidatedRangeInflate { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_IsoMesher_" data-uid="VRage.Voxels.MyPrecalcComponent.IsoMesher*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_IsoMesher" data-uid="VRage.Voxels.MyPrecalcComponent.IsoMesher">IsoMesher</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IMyIsoMesher IsoMesher { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.IMyIsoMesher.html">IMyIsoMesher</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_IsoMesherType_" data-uid="VRage.Voxels.MyPrecalcComponent.IsoMesherType*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_IsoMesherType" data-uid="VRage.Voxels.MyPrecalcComponent.IsoMesherType">IsoMesherType</h4>
  <div class="markdown level1 summary"><p>The IsoMesher type used by precalc jobs.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Type IsoMesherType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_AssertUpdateThread_" data-uid="VRage.Voxels.MyPrecalcComponent.AssertUpdateThread*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_AssertUpdateThread" data-uid="VRage.Voxels.MyPrecalcComponent.AssertUpdateThread">AssertUpdateThread()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AssertUpdateThread()</code></pre>
  </div>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_EnqueueBack_" data-uid="VRage.Voxels.MyPrecalcComponent.EnqueueBack*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_EnqueueBack_VRage_Game_Voxels_MyPrecalcJob_" data-uid="VRage.Voxels.MyPrecalcComponent.EnqueueBack(VRage.Game.Voxels.MyPrecalcJob)">EnqueueBack(MyPrecalcJob)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool EnqueueBack(MyPrecalcJob job)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Game.Voxels.MyPrecalcJob</span></td>
        <td><span class="parametername">job</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_LoadData_" data-uid="VRage.Voxels.MyPrecalcComponent.LoadData*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_LoadData" data-uid="VRage.Voxels.MyPrecalcComponent.LoadData">LoadData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void LoadData()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_LoadData">MySessionComponentBase.LoadData()</a></div>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_UnloadData_" data-uid="VRage.Voxels.MyPrecalcComponent.UnloadData*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_UnloadData" data-uid="VRage.Voxels.MyPrecalcComponent.UnloadData">UnloadData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void UnloadData()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UnloadData">MySessionComponentBase.UnloadData()</a></div>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_UpdateAfterSimulation_" data-uid="VRage.Voxels.MyPrecalcComponent.UpdateAfterSimulation*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_UpdateAfterSimulation" data-uid="VRage.Voxels.MyPrecalcComponent.UpdateAfterSimulation">UpdateAfterSimulation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void UpdateAfterSimulation()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdateAfterSimulation">MySessionComponentBase.UpdateAfterSimulation()</a></div>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_UpdatedBeforeInit_" data-uid="VRage.Voxels.MyPrecalcComponent.UpdatedBeforeInit*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_UpdatedBeforeInit" data-uid="VRage.Voxels.MyPrecalcComponent.UpdatedBeforeInit">UpdatedBeforeInit()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool UpdatedBeforeInit()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.Components.MySessionComponentBase.html#VRage_Game_Components_MySessionComponentBase_UpdatedBeforeInit">MySessionComponentBase.UpdatedBeforeInit()</a></div>
  
  
  <a id="VRage_Voxels_MyPrecalcComponent_UpdateQueue_" data-uid="VRage.Voxels.MyPrecalcComponent.UpdateQueue*"></a>
  <h4 id="VRage_Voxels_MyPrecalcComponent_UpdateQueue" data-uid="VRage.Voxels.MyPrecalcComponent.UpdateQueue">UpdateQueue()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateQueue()</code></pre>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
