﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilderType
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilderType
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders.MyObjectBuilderType">
  
  
  <h1 id="VRage_ObjectBuilders_MyObjectBuilderType" data-uid="VRage.ObjectBuilders.MyObjectBuilderType" class="text-break">Class MyObjectBuilderType
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectBuilderType</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ObjectBuilders.html">VRage.ObjectBuilders</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ObjectBuilders_MyObjectBuilderType_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MyObjectBuilderType : ValueType</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType__ctor_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.#ctor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType__ctor_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.#ctor(System.Type)">MyObjectBuilderType(Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilderType(Type type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_Comparer" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Comparer">Comparer</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly MyObjectBuilderType.ComparerType Comparer</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.ComparerType.html">MyObjectBuilderType.ComparerType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_Invalid" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Invalid">Invalid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly MyObjectBuilderType Invalid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_LEGACY_TYPE_PREFIX" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.LEGACY_TYPE_PREFIX">LEGACY_TYPE_PREFIX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const string LEGACY_TYPE_PREFIX = &quot;MyObjectBuilder_&quot;</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_IsNull_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsNull*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_IsNull" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsNull">IsNull</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsNull { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_Equals_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Equals*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_Equals_System_Object_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_Equals_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Equals*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_Equals_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Equals(VRage.ObjectBuilders.MyObjectBuilderType)">Equals(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(MyObjectBuilderType type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_GetHashCode_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.GetHashCode*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_GetHashCode" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_IsReady_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsReady*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_IsReady" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsReady">IsReady()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsReady()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_IsValidTypeName_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsValidTypeName*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_IsValidTypeName_System_String_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.IsValidTypeName(System.String)">IsValidTypeName(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsValidTypeName(string value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_Parse_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Parse*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_Parse_System_String_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.Parse(System.String)">Parse(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilderType Parse(string value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_ParseBackwardsCompatible_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.ParseBackwardsCompatible*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_ParseBackwardsCompatible_System_String_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.ParseBackwardsCompatible(System.String)">ParseBackwardsCompatible(String)</h4>
  <div class="markdown level1 summary"><p>Can handle old values as well.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilderType ParseBackwardsCompatible(string value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_RegisterFromAssembly_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.RegisterFromAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_RegisterFromAssembly_System_Reflection_Assembly_System_Boolean_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.RegisterFromAssembly(System.Reflection.Assembly,System.Boolean)">RegisterFromAssembly(Assembly, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RegisterFromAssembly(Assembly assembly, bool registerLegacyNames = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span></td>
        <td><span class="parametername">assembly</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">registerLegacyNames</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_ToString_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.ToString*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_ToString" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.ToString">ToString()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_TryParse_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.TryParse*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_TryParse_System_String_VRage_ObjectBuilders_MyObjectBuilderType__" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.TryParse(System.String,VRage.ObjectBuilders.MyObjectBuilderType@)">TryParse(String, out MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool TryParse(string value, out MyObjectBuilderType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_UnregisterAssemblies_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.UnregisterAssemblies*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_UnregisterAssemblies" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.UnregisterAssemblies">UnregisterAssemblies()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void UnregisterAssemblies()</code></pre>
  </div>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_UnregisterFromAssembly_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.UnregisterFromAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_UnregisterFromAssembly_System_Reflection_Assembly_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.UnregisterFromAssembly(System.Reflection.Assembly)">UnregisterFromAssembly(Assembly)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void UnregisterFromAssembly(Assembly assembly)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span></td>
        <td><span class="parametername">assembly</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_System_Type_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality(System.Type,VRage.ObjectBuilders.MyObjectBuilderType)">Equality(Type, MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Type lhs, MyObjectBuilderType rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_VRage_ObjectBuilders_MyObjectBuilderType_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality(VRage.ObjectBuilders.MyObjectBuilderType,System.Type)">Equality(MyObjectBuilderType, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(MyObjectBuilderType lhs, Type rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Equality_VRage_ObjectBuilders_MyObjectBuilderType_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Equality(VRage.ObjectBuilders.MyObjectBuilderType,VRage.ObjectBuilders.MyObjectBuilderType)">Equality(MyObjectBuilderType, MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(MyObjectBuilderType lhs, MyObjectBuilderType rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Explicit_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Explicit*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Explicit_VRage_ObjectBuilders_MyObjectBuilderType__VRage_ObjectBuilders_MyRuntimeObjectBuilderId" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Explicit(VRage.ObjectBuilders.MyObjectBuilderType)~VRage.ObjectBuilders.MyRuntimeObjectBuilderId">Explicit(MyObjectBuilderType to MyRuntimeObjectBuilderId)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator MyRuntimeObjectBuilderId(MyObjectBuilderType t)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">t</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyRuntimeObjectBuilderId.html">MyRuntimeObjectBuilderId</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Explicit_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Explicit*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Explicit_VRage_ObjectBuilders_MyRuntimeObjectBuilderId__VRage_ObjectBuilders_MyObjectBuilderType" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Explicit(VRage.ObjectBuilders.MyRuntimeObjectBuilderId)~VRage.ObjectBuilders.MyObjectBuilderType">Explicit(MyRuntimeObjectBuilderId to MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator MyObjectBuilderType(MyRuntimeObjectBuilderId id)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyRuntimeObjectBuilderId.html">MyRuntimeObjectBuilderId</a></td>
        <td><span class="parametername">id</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Implicit_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Implicit*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Implicit_System_Type__VRage_ObjectBuilders_MyObjectBuilderType" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Implicit(System.Type)~VRage.ObjectBuilders.MyObjectBuilderType">Implicit(Type to MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator MyObjectBuilderType(Type t)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">t</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Implicit_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Implicit*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Implicit_VRage_ObjectBuilders_MyObjectBuilderType__System_Type" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Implicit(VRage.ObjectBuilders.MyObjectBuilderType)~System.Type">Implicit(MyObjectBuilderType to Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Type(MyObjectBuilderType t)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">t</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_System_Type_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality(System.Type,VRage.ObjectBuilders.MyObjectBuilderType)">Inequality(Type, MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Type lhs, MyObjectBuilderType rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_VRage_ObjectBuilders_MyObjectBuilderType_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality(VRage.ObjectBuilders.MyObjectBuilderType,System.Type)">Inequality(MyObjectBuilderType, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(MyObjectBuilderType lhs, Type rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderType_op_Inequality_VRage_ObjectBuilders_MyObjectBuilderType_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderType.op_Inequality(VRage.ObjectBuilders.MyObjectBuilderType,VRage.ObjectBuilders.MyObjectBuilderType)">Inequality(MyObjectBuilderType, MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(MyObjectBuilderType lhs, MyObjectBuilderType rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">lhs</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">rhs</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
