﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.Game.ObjectBuilders.ComponentSystem
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.Game.ObjectBuilders.ComponentSystem
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.ObjectBuilders.ComponentSystem">
  
  <h1 id="VRage_Game_ObjectBuilders_ComponentSystem" data-uid="VRage.Game.ObjectBuilders.ComponentSystem" class="text-break">Namespace VRage.Game.ObjectBuilders.ComponentSystem
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AreaInventory.html">MyObjectBuilder_AreaInventory</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AreaInventoryAggregate.html">MyObjectBuilder_AreaInventoryAggregate</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AreaTrigger.html">MyObjectBuilder_AreaTrigger</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AssetModifierComponent.html">MyObjectBuilder_AssetModifierComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AtmosphereDetectorComponent.html">MyObjectBuilder_AtmosphereDetectorComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_CharacterPickupComponent.html">MyObjectBuilder_CharacterPickupComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_CharacterSoundComponent.html">MyObjectBuilder_CharacterSoundComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_CharacterStatComponent.html">MyObjectBuilder_CharacterStatComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.html">MyObjectBuilder_ComponentBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentContainer.html">MyObjectBuilder_ComponentContainer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentContainer.ComponentData.html">MyObjectBuilder_ComponentContainer.ComponentData</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ContainerDropComponent.html">MyObjectBuilder_ContainerDropComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_DurabilityComponentDefinition.html">MyObjectBuilder_DurabilityComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_DurabilityComponentDefinition.HitDefinition.html">MyObjectBuilder_DurabilityComponentDefinition.HitDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityCapacitorComponent.html">MyObjectBuilder_EntityCapacitorComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityCapacitorComponentDefinition.html">MyObjectBuilder_EntityCapacitorComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityDurabilityComponent.html">MyObjectBuilder_EntityDurabilityComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityReverbDetectorComponent.html">MyObjectBuilder_EntityReverbDetectorComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityStatComponent.html">MyObjectBuilder_EntityStatComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityStatComponentDefinition.html">MyObjectBuilder_EntityStatComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityStorageComponent.html">MyObjectBuilder_EntityStorageComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventoryAggregate.html">MyObjectBuilder_InventoryAggregate</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventoryBase.html">MyObjectBuilder_InventoryBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventoryComponentDefinition.html">MyObjectBuilder_InventoryComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventoryComponentDefinition.InventoryConstraintDefinition.html">MyObjectBuilder_InventoryComponentDefinition.InventoryConstraintDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventorySpawnComponent.html">MyObjectBuilder_InventorySpawnComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventorySpawnComponent_Definition.html">MyObjectBuilder_InventorySpawnComponent_Definition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ModCustomComponent.html">MyObjectBuilder_ModCustomComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ModStorageComponent.html">MyObjectBuilder_ModStorageComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ModStorageComponentDefinition.html">MyObjectBuilder_ModStorageComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsBodyComponent.html">MyObjectBuilder_PhysicsBodyComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsBodyComponentDefinition.html">MyObjectBuilder_PhysicsBodyComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsComponentBase.html">MyObjectBuilder_PhysicsComponentBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsComponentDefinitionBase.html">MyObjectBuilder_PhysicsComponentDefinitionBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsComponentDefinitionBase.MyMassPropertiesComputationType.html">MyObjectBuilder_PhysicsComponentDefinitionBase.MyMassPropertiesComputationType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsComponentDefinitionBase.MyUpdateFlags.html">MyObjectBuilder_PhysicsComponentDefinitionBase.MyUpdateFlags</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_RandomCargoEntityComponentDefinition.html">MyObjectBuilder_RandomCargoEntityComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ResourceSinkComponent.html">MyObjectBuilder_ResourceSinkComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ResourceSourceComponent.html">MyObjectBuilder_ResourceSourceComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_RespawnComponent.html">MyObjectBuilder_RespawnComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ShipSoundComponent.html">MyObjectBuilder_ShipSoundComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_SunTrackingComponent.html">MyObjectBuilder_SunTrackingComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetFocusComponent.html">MyObjectBuilder_TargetFocusComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetFocusComponentDefinition.html">MyObjectBuilder_TargetFocusComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingBlockComponent.html">MyObjectBuilder_TargetLockingBlockComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingBlockComponentDefinition.html">MyObjectBuilder_TargetLockingBlockComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingComponent.html">MyObjectBuilder_TargetLockingComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingComponentDefinition.html">MyObjectBuilder_TargetLockingComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TimerComponent.html">MyObjectBuilder_TimerComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TimerComponentDefinition.html">MyObjectBuilder_TimerComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TriggerAggregate.html">MyObjectBuilder_TriggerAggregate</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TriggerBase.html">MyObjectBuilder_TriggerBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_UpdateTrigger.html">MyObjectBuilder_UpdateTrigger</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_UseObjectsComponent.html">MyObjectBuilder_UseObjectsComponent</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_UseObjectsComponentDefinition.html">MyObjectBuilder_UseObjectsComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyTimerTypes.html">MyTimerTypes</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
