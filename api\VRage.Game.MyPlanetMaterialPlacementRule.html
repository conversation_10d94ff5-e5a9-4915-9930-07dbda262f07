﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyPlanetMaterialPlacementRule
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyPlanetMaterialPlacementRule
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyPlanetMaterialPlacementRule">
  
  
  <h1 id="VRage_Game_MyPlanetMaterialPlacementRule" data-uid="VRage.Game.MyPlanetMaterialPlacementRule" class="text-break">Class MyPlanetMaterialPlacementRule
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html">MyPlanetMaterialDefinition</a></div>
    <div class="level2"><span class="xref">MyPlanetMaterialPlacementRule</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_Material">MyPlanetMaterialDefinition.Material</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_Value">MyPlanetMaterialDefinition.Value</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_MaxDepth">MyPlanetMaterialDefinition.MaxDepth</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_Layers">MyPlanetMaterialDefinition.Layers</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_HasLayers">MyPlanetMaterialDefinition.HasLayers</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_FirstOrDefault">MyPlanetMaterialDefinition.FirstOrDefault</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyPlanetMaterialPlacementRule_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyPlanetMaterialPlacementRule : MyPlanetMaterialDefinition, ICloneable</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyPlanetMaterialPlacementRule__ctor_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.#ctor*"></a>
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule__ctor" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.#ctor">MyPlanetMaterialPlacementRule()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyPlanetMaterialPlacementRule()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyPlanetMaterialPlacementRule__ctor_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.#ctor*"></a>
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule__ctor_VRage_Game_MyPlanetMaterialPlacementRule_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.#ctor(VRage.Game.MyPlanetMaterialPlacementRule)">MyPlanetMaterialPlacementRule(MyPlanetMaterialPlacementRule)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyPlanetMaterialPlacementRule(MyPlanetMaterialPlacementRule copyFrom)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyPlanetMaterialPlacementRule.html">MyPlanetMaterialPlacementRule</a></td>
        <td><span class="parametername">copyFrom</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Height" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Height">Height</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableRange Height</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.SerializableRange.html">SerializableRange</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Latitude" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Latitude">Latitude</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SymmetricSerializableRange Latitude</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.SymmetricSerializableRange.html">SymmetricSerializableRange</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Longitude" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Longitude">Longitude</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableRange Longitude</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.SerializableRange.html">SerializableRange</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Slope" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Slope">Slope</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableRange Slope</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.SerializableRange.html">SerializableRange</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyPlanetMaterialPlacementRule_IsRule_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.IsRule*"></a>
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_IsRule" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.IsRule">IsRule</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool IsRule { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyPlanetMaterialDefinition.html#VRage_Game_MyPlanetMaterialDefinition_IsRule">MyPlanetMaterialDefinition.IsRule</a></div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyPlanetMaterialPlacementRule_Check_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Check*"></a>
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Check_System_Single_System_Single_System_Single_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Check(System.Single,System.Single,System.Single)">Check(Single, Single, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Check(float height, float latitude, float slope)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">latitude</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">slope</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyPlanetMaterialPlacementRule_Clone_" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Clone*"></a>
  <h4 id="VRage_Game_MyPlanetMaterialPlacementRule_Clone" data-uid="VRage.Game.MyPlanetMaterialPlacementRule.Clone">Clone()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public object Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
