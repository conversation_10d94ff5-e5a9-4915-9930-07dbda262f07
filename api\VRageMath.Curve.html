﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Curve
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Curve
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Curve">
  
  
  <h1 id="VRageMath_Curve" data-uid="VRageMath.Curve" class="text-break">Class Curve
  </h1>
  <div class="markdown level0 summary"><p>Stores an arbitrary collection of 2D CurveKey points, and provides methods for evaluating features of the curve they define.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Curve</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Curve_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class Curve : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Curve__ctor_" data-uid="VRageMath.Curve.#ctor*"></a>
  <h4 id="VRageMath_Curve__ctor" data-uid="VRageMath.Curve.#ctor">Curve()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Curve()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Curve_IsConstant_" data-uid="VRageMath.Curve.IsConstant*"></a>
  <h4 id="VRageMath_Curve_IsConstant" data-uid="VRageMath.Curve.IsConstant">IsConstant</h4>
  <div class="markdown level1 summary"><p>Gets a value indicating whether the curve is constant.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsConstant { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_Keys_" data-uid="VRageMath.Curve.Keys*"></a>
  <h4 id="VRageMath_Curve_Keys" data-uid="VRageMath.Curve.Keys">Keys</h4>
  <div class="markdown level1 summary"><p>The points that make up the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKeyCollection Keys { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKeyCollection.html">CurveKeyCollection</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_PostLoop_" data-uid="VRageMath.Curve.PostLoop*"></a>
  <h4 id="VRageMath_Curve_PostLoop" data-uid="VRageMath.Curve.PostLoop">PostLoop</h4>
  <div class="markdown level1 summary"><p>Specifies how to handle weighting values that are greater than the last control point in the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveLoopType PostLoop { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveLoopType.html">CurveLoopType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_PreLoop_" data-uid="VRageMath.Curve.PreLoop*"></a>
  <h4 id="VRageMath_Curve_PreLoop" data-uid="VRageMath.Curve.PreLoop">PreLoop</h4>
  <div class="markdown level1 summary"><p>Specifies how to handle weighting values that are less than the first control point in the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveLoopType PreLoop { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveLoopType.html">CurveLoopType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Curve_Clone_" data-uid="VRageMath.Curve.Clone*"></a>
  <h4 id="VRageMath_Curve_Clone" data-uid="VRageMath.Curve.Clone">Clone()</h4>
  <div class="markdown level1 summary"><p>Creates a copy of the Curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Curve Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Curve.html">Curve</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_ComputeTangent_" data-uid="VRageMath.Curve.ComputeTangent*"></a>
  <h4 id="VRageMath_Curve_ComputeTangent_System_Int32_VRageMath_CurveTangent_" data-uid="VRageMath.Curve.ComputeTangent(System.Int32,VRageMath.CurveTangent)">ComputeTangent(Int32, CurveTangent)</h4>
  <div class="markdown level1 summary"><p>Computes both the TangentIn and the TangentOut for a CurveKey specified by its index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ComputeTangent(int keyIndex, CurveTangent tangentType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">keyIndex</span></td>
        <td><p>The index of the CurveKey for which to compute tangents (in the Keys collection of the Curve).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentType</span></td>
        <td><p>The type of tangents to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_ComputeTangent_" data-uid="VRageMath.Curve.ComputeTangent*"></a>
  <h4 id="VRageMath_Curve_ComputeTangent_System_Int32_VRageMath_CurveTangent_VRageMath_CurveTangent_" data-uid="VRageMath.Curve.ComputeTangent(System.Int32,VRageMath.CurveTangent,VRageMath.CurveTangent)">ComputeTangent(Int32, CurveTangent, CurveTangent)</h4>
  <div class="markdown level1 summary"><p>Computes a specified type of TangentIn and a specified type of TangentOut for a given CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ComputeTangent(int keyIndex, CurveTangent tangentInType, CurveTangent tangentOutType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">keyIndex</span></td>
        <td><p>The index of the CurveKey for which to compute tangents (in the Keys collection of the Curve).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentInType</span></td>
        <td><p>The type of TangentIn to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentOutType</span></td>
        <td><p>The type of TangentOut to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_ComputeTangents_" data-uid="VRageMath.Curve.ComputeTangents*"></a>
  <h4 id="VRageMath_Curve_ComputeTangents_VRageMath_CurveTangent_" data-uid="VRageMath.Curve.ComputeTangents(VRageMath.CurveTangent)">ComputeTangents(CurveTangent)</h4>
  <div class="markdown level1 summary"><p>Computes all tangents for all CurveKeys in this Curve, using a specified tangent type for both TangentIn and TangentOut.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ComputeTangents(CurveTangent tangentType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentType</span></td>
        <td><p>The type of TangentOut and TangentIn to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_ComputeTangents_" data-uid="VRageMath.Curve.ComputeTangents*"></a>
  <h4 id="VRageMath_Curve_ComputeTangents_VRageMath_CurveTangent_VRageMath_CurveTangent_" data-uid="VRageMath.Curve.ComputeTangents(VRageMath.CurveTangent,VRageMath.CurveTangent)">ComputeTangents(CurveTangent, CurveTangent)</h4>
  <div class="markdown level1 summary"><p>Computes all tangents for all CurveKeys in this Curve, using different tangent types for TangentOut and TangentIn.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ComputeTangents(CurveTangent tangentInType, CurveTangent tangentOutType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentInType</span></td>
        <td><p>The type of TangentIn to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveTangent.html">CurveTangent</a></td>
        <td><span class="parametername">tangentOutType</span></td>
        <td><p>The type of TangentOut to compute (one of the types specified in the CurveTangent enumeration).</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Curve_Evaluate_" data-uid="VRageMath.Curve.Evaluate*"></a>
  <h4 id="VRageMath_Curve_Evaluate_System_Single_" data-uid="VRageMath.Curve.Evaluate(System.Single)">Evaluate(Single)</h4>
  <div class="markdown level1 summary"><p>Finds the value at a position on the Curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Evaluate(float position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>The position on the Curve.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
