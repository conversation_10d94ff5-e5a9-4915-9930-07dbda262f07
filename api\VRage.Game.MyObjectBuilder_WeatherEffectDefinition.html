﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_WeatherEffectDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_WeatherEffectDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition" class="text-break">Class MyObjectBuilder_WeatherEffectDefinition
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></div>
    <div class="level3"><span class="xref">MyObjectBuilder_WeatherEffectDefinition</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Id">MyObjectBuilder_DefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DisplayName">MyObjectBuilder_DefinitionBase.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Description">MyObjectBuilder_DefinitionBase.Description</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Icons">MyObjectBuilder_DefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Public">MyObjectBuilder_DefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Enabled">MyObjectBuilder_DefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_AvailableInSurvival">MyObjectBuilder_DefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DescriptionArgs">MyObjectBuilder_DefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DLCs">MyObjectBuilder_DefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_WeatherEffectDefinition : MyObjectBuilder_DefinitionBase</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition__ctor_" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition__ctor" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.#ctor">MyObjectBuilder_WeatherEffectDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_WeatherEffectDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_AmbientSound" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.AmbientSound">AmbientSound</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string AmbientSound</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_AmbientVolume" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.AmbientVolume">AmbientVolume</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float AmbientVolume</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_EffectName" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.EffectName">EffectName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string EffectName</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FogAtmoMultiplier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FogAtmoMultiplier">FogAtmoMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FogAtmoMultiplier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FogColor" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FogColor">FogColor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 FogColor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FogDensity" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FogDensity">FogDensity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FogDensity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FogMultiplier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FogMultiplier">FogMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FogMultiplier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FogSkyboxMultiplier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FogSkyboxMultiplier">FogSkyboxMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FogSkyboxMultiplier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_FoliageWindModifier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.FoliageWindModifier">FoliageWindModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float FoliageWindModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_HazardNotification" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.HazardNotification">HazardNotification</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_WeatherEffectDefinition.HazardNotificationData HazardNotification</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.HazardNotificationData.html">MyObjectBuilder_WeatherEffectDefinition.HazardNotificationData</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_HealthHazard" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.HealthHazard">HealthHazard</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_WeatherEffectDefinition.HealthHazardSource HealthHazard</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.HealthHazardSource.html">MyObjectBuilder_WeatherEffectDefinition.HealthHazardSource</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_Lightning" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.Lightning">Lightning</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_WeatherLightning Lightning</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherLightning.html">MyObjectBuilder_WeatherLightning</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningCharacterHitIntervalMax" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningCharacterHitIntervalMax">LightningCharacterHitIntervalMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningCharacterHitIntervalMax</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningCharacterHitIntervalMin" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningCharacterHitIntervalMin">LightningCharacterHitIntervalMin</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningCharacterHitIntervalMin</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningGridHitIntervalMax" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningGridHitIntervalMax">LightningGridHitIntervalMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningGridHitIntervalMax</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningGridHitIntervalMin" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningGridHitIntervalMin">LightningGridHitIntervalMin</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningGridHitIntervalMin</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningIntervalMax" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningIntervalMax">LightningIntervalMax</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningIntervalMax</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_LightningIntervalMin" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.LightningIntervalMin">LightningIntervalMin</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LightningIntervalMin</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_OxygenLevelModifier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.OxygenLevelModifier">OxygenLevelModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float OxygenLevelModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_ParticleAlphaMultiplier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.ParticleAlphaMultiplier">ParticleAlphaMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ParticleAlphaMultiplier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_ParticleCount" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.ParticleCount">ParticleCount</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ParticleCount</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_ParticleRadius" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.ParticleRadius">ParticleRadius</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ParticleRadius</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_ParticleScale" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.ParticleScale">ParticleScale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ParticleScale</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_RadiationHazard" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.RadiationHazard">RadiationHazard</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_WeatherEffectDefinition.RadiationHazardSource RadiationHazard</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.RadiationHazardSource.html">MyObjectBuilder_WeatherEffectDefinition.RadiationHazardSource</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_ShadowFadeout" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.ShadowFadeout">ShadowFadeout</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ShadowFadeout</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_SolarOutputModifier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.SolarOutputModifier">SolarOutputModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SolarOutputModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_SunColor" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.SunColor">SunColor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 SunColor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_SunIntensity" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.SunIntensity">SunIntensity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SunIntensity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_SunSpecularColor" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.SunSpecularColor">SunSpecularColor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 SunSpecularColor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_TemperatureModifier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.TemperatureModifier">TemperatureModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float TemperatureModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_WeatherEffectDefinition_WindOutputModifier" data-uid="VRage.Game.MyObjectBuilder_WeatherEffectDefinition.WindOutputModifier">WindOutputModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float WindOutputModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
