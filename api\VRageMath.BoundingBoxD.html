﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingBoxD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingBoxD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingBoxD">
  
  
  <h1 id="VRageMath_BoundingBoxD" data-uid="VRageMath.BoundingBoxD" class="text-break">Class BoundingBoxD
  </h1>
  <div class="markdown level0 summary"><p>Defines an axis-aligned box-shaped 3D volume.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingBoxD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingBoxD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class BoundingBoxD : ValueType, IEquatable&lt;BoundingBoxD&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingBoxD__ctor_" data-uid="VRageMath.BoundingBoxD.#ctor*"></a>
  <h4 id="VRageMath_BoundingBoxD__ctor_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.#ctor(VRageMath.Vector3D,VRageMath.Vector3D)">BoundingBoxD(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD(Vector3D min, Vector3D max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum point the BoundingBox includes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum point the BoundingBox includes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingBoxD_Comparer" data-uid="VRageMath.BoundingBoxD.Comparer">Comparer</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BoundingBoxD.ComparerType Comparer</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.ComparerType.html">BoundingBoxD.ComparerType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBoxD_Max" data-uid="VRageMath.BoundingBoxD.Max">Max</h4>
  <div class="markdown level1 summary"><p>The maximum point the BoundingBox contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Max</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBoxD_Min" data-uid="VRageMath.BoundingBoxD.Min">Min</h4>
  <div class="markdown level1 summary"><p>The minimum point the BoundingBox contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Min</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBoxD_NUMBER_OF_CORNERS" data-uid="VRageMath.BoundingBoxD.NUMBER_OF_CORNERS">NUMBER_OF_CORNERS</h4>
  <div class="markdown level1 summary"><p>Number of corners of the BB</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly int NUMBER_OF_CORNERS</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingBoxD_Center_" data-uid="VRageMath.BoundingBoxD.Center*"></a>
  <h4 id="VRageMath_BoundingBoxD_Center" data-uid="VRageMath.BoundingBoxD.Center">Center</h4>
  <div class="markdown level1 summary"><p>Calculates center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Center { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Extents_" data-uid="VRageMath.BoundingBoxD.Extents*"></a>
  <h4 id="VRageMath_BoundingBoxD_Extents" data-uid="VRageMath.BoundingBoxD.Extents">Extents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Extents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_HalfExtents_" data-uid="VRageMath.BoundingBoxD.HalfExtents*"></a>
  <h4 id="VRageMath_BoundingBoxD_HalfExtents" data-uid="VRageMath.BoundingBoxD.HalfExtents">HalfExtents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D HalfExtents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Matrix_" data-uid="VRageMath.BoundingBoxD.Matrix*"></a>
  <h4 id="VRageMath_BoundingBoxD_Matrix" data-uid="VRageMath.BoundingBoxD.Matrix">Matrix</h4>
  <div class="markdown level1 summary"><p>Matrix of AABB, respecting center and size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixD Matrix { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Perimeter_" data-uid="VRageMath.BoundingBoxD.Perimeter*"></a>
  <h4 id="VRageMath_BoundingBoxD_Perimeter" data-uid="VRageMath.BoundingBoxD.Perimeter">Perimeter</h4>
  <div class="markdown level1 summary"><p>return perimeter of edges</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Perimeter { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Size_" data-uid="VRageMath.BoundingBoxD.Size*"></a>
  <h4 id="VRageMath_BoundingBoxD_Size" data-uid="VRageMath.BoundingBoxD.Size">Size</h4>
  <div class="markdown level1 summary"><p>Size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_SurfaceArea_" data-uid="VRageMath.BoundingBoxD.SurfaceArea*"></a>
  <h4 id="VRageMath_BoundingBoxD_SurfaceArea" data-uid="VRageMath.BoundingBoxD.SurfaceArea">SurfaceArea</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double SurfaceArea { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Valid_" data-uid="VRageMath.BoundingBoxD.Valid*"></a>
  <h4 id="VRageMath_BoundingBoxD_Valid" data-uid="VRageMath.BoundingBoxD.Valid">Valid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Valid { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Volume_" data-uid="VRageMath.BoundingBoxD.Volume*"></a>
  <h4 id="VRageMath_BoundingBoxD_Volume" data-uid="VRageMath.BoundingBoxD.Volume">Volume</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Volume { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingBoxD_AssertIsValid_" data-uid="VRageMath.BoundingBoxD.AssertIsValid*"></a>
  <h4 id="VRageMath_BoundingBoxD_AssertIsValid" data-uid="VRageMath.BoundingBoxD.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_BoundingBoxD_Centerize_" data-uid="VRageMath.BoundingBoxD.Centerize*"></a>
  <h4 id="VRageMath_BoundingBoxD_Centerize_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Centerize(VRageMath.Vector3D)">Centerize(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Centerize(Vector3D center)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.BoundingBoxD)">Contains(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains another BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_BoundingBoxD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.BoundingBoxD@,VRageMath.ContainmentType@)">Contains(ref BoundingBoxD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBoxD box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.BoundingFrustumD)">Contains(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.BoundingSphereD)">Contains(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_BoundingSphereD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.BoundingSphereD@,VRageMath.ContainmentType@)">Contains(ref BoundingSphereD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingSphereD sphere, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.Vector3D)">Contains(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Contains_" data-uid="VRageMath.BoundingBoxD.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxD_Contains_VRageMath_Vector3D__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBoxD.Contains(VRageMath.Vector3D@,VRageMath.ContainmentType@)">Contains(ref Vector3D, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector3D point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateFromPoints_" data-uid="VRageMath.BoundingBoxD.CreateFromPoints*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateFromPoints_System_Collections_Generic_IEnumerable_VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.CreateFromPoints(System.Collections.Generic.IEnumerable{VRageMath.Vector3D})">CreateFromPoints(IEnumerable&lt;Vector3D&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox that will contain a group of points.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxD CreateFromPoints(IEnumerable&lt;Vector3D&gt; points)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><span class="parametername">points</span></td>
        <td><p>A list of points the BoundingBox should contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateFromSphere_" data-uid="VRageMath.BoundingBoxD.CreateFromSphere*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateFromSphere_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingBoxD.CreateFromSphere(VRageMath.BoundingSphereD)">CreateFromSphere(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox that will contain the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxD CreateFromSphere(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateFromSphere_" data-uid="VRageMath.BoundingBoxD.CreateFromSphere*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateFromSphere_VRageMath_BoundingSphereD__VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.CreateFromSphere(VRageMath.BoundingSphereD@,VRageMath.BoundingBoxD@)">CreateFromSphere(ref BoundingSphereD, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox that will contain the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromSphere(ref BoundingSphereD sphere, out BoundingBoxD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateInvalid_" data-uid="VRageMath.BoundingBoxD.CreateInvalid*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateInvalid" data-uid="VRageMath.BoundingBoxD.CreateInvalid">CreateInvalid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxD CreateInvalid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateMerged_" data-uid="VRageMath.BoundingBoxD.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateMerged_VRageMath_BoundingBoxD_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.CreateMerged(VRageMath.BoundingBoxD,VRageMath.BoundingBoxD)">CreateMerged(BoundingBoxD, BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox that contains the two specified BoundingBox instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxD CreateMerged(BoundingBoxD original, BoundingBoxD additional)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBoxs to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBoxs to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_CreateMerged_" data-uid="VRageMath.BoundingBoxD.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBoxD_CreateMerged_VRageMath_BoundingBoxD__VRageMath_BoundingBoxD__VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.CreateMerged(VRageMath.BoundingBoxD@,VRageMath.BoundingBoxD@,VRageMath.BoundingBoxD@)">CreateMerged(ref BoundingBoxD, ref BoundingBoxD, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox that contains the two specified BoundingBox instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateMerged(ref BoundingBoxD original, ref BoundingBoxD additional, out BoundingBoxD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBox instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBox instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Distance_" data-uid="VRageMath.BoundingBoxD.Distance*"></a>
  <h4 id="VRageMath_BoundingBoxD_Distance_VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.Distance(VRageMath.BoundingBoxD@)">Distance(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Distance(ref BoundingBoxD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Distance_" data-uid="VRageMath.BoundingBoxD.Distance*"></a>
  <h4 id="VRageMath_BoundingBoxD_Distance_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Distance(VRageMath.Vector3D)">Distance(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Distance(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_DistanceSquared_" data-uid="VRageMath.BoundingBoxD.DistanceSquared*"></a>
  <h4 id="VRageMath_BoundingBoxD_DistanceSquared_VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.DistanceSquared(VRageMath.BoundingBoxD@)">DistanceSquared(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DistanceSquared(ref BoundingBoxD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_DistanceSquared_" data-uid="VRageMath.BoundingBoxD.DistanceSquared*"></a>
  <h4 id="VRageMath_BoundingBoxD_DistanceSquared_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.DistanceSquared(VRageMath.Vector3D)">DistanceSquared(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DistanceSquared(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_DistanceSquared_" data-uid="VRageMath.BoundingBoxD.DistanceSquared*"></a>
  <h4 id="VRageMath_BoundingBoxD_DistanceSquared_VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.DistanceSquared(VRageMath.Vector3D@)">DistanceSquared(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DistanceSquared(ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Equals_" data-uid="VRageMath.BoundingBoxD.Equals*"></a>
  <h4 id="VRageMath_BoundingBoxD_Equals_System_Object_" data-uid="VRageMath.BoundingBoxD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Equals_" data-uid="VRageMath.BoundingBoxD.Equals*"></a>
  <h4 id="VRageMath_BoundingBoxD_Equals_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.Equals(VRageMath.BoundingBoxD)">Equals(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingBoxD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingBox to compare with the current BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Equals_" data-uid="VRageMath.BoundingBoxD.Equals*"></a>
  <h4 id="VRageMath_BoundingBoxD_Equals_VRageMath_BoundingBoxD_System_Double_" data-uid="VRageMath.BoundingBoxD.Equals(VRageMath.BoundingBoxD,System.Double)">Equals(BoundingBoxD, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingBoxD other, double epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetCorner_" data-uid="VRageMath.BoundingBoxD.GetCorner*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetCorner_System_Int32_" data-uid="VRageMath.BoundingBoxD.GetCorner(System.Int32)">GetCorner(Int32)</h4>
  <div class="markdown level1 summary"><p>Gets corner of the BB by index</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D GetCorner(int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td><p>index of the corner</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><p>corner position</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetCorners_" data-uid="VRageMath.BoundingBoxD.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetCorners" data-uid="VRageMath.BoundingBoxD.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingBox. ALLOCATION!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetCorners_" data-uid="VRageMath.BoundingBoxD.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetCorners_VRageMath_Vector3D___" data-uid="VRageMath.BoundingBoxD.GetCorners(VRageMath.Vector3D[])">GetCorners(Vector3D[])</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector3D[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3 points where the corners of the BoundingBox are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetCornersUnsafe_" data-uid="VRageMath.BoundingBoxD.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetCornersUnsafe_VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.GetCornersUnsafe(VRageMath.Vector3D*)">GetCornersUnsafe(Vector3D*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector3D*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3 points where the corners of the BoundingBox are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetHashCode_" data-uid="VRageMath.BoundingBoxD.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetHashCode" data-uid="VRageMath.BoundingBoxD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetInflated_" data-uid="VRageMath.BoundingBoxD.GetInflated*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetInflated_System_Double_" data-uid="VRageMath.BoundingBoxD.GetInflated(System.Double)">GetInflated(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD GetInflated(double size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetInflated_" data-uid="VRageMath.BoundingBoxD.GetInflated*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetInflated_VRageMath_Vector3_" data-uid="VRageMath.BoundingBoxD.GetInflated(VRageMath.Vector3)">GetInflated(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD GetInflated(Vector3 size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_GetInflated_" data-uid="VRageMath.BoundingBoxD.GetInflated*"></a>
  <h4 id="VRageMath_BoundingBoxD_GetInflated_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.GetInflated(VRageMath.Vector3D)">GetInflated(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD GetInflated(Vector3D size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.BoundingBoxD)">Include(BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.BoundingBoxD@)">Include(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (aabb include aabb)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(ref BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_BoundingFrustumD__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.BoundingFrustumD@)">Include(ref BoundingFrustumD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(ref BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.BoundingSphereD)">Include(BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.BoundingSphereD@)">Include(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_LineD__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.LineD@)">Include(ref LineD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Include(ref LineD line)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.Vector3D)">Include(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">Include(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(Vector3D p0, Vector3D p1, Vector3D p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.Vector3D@)">Include(ref Vector3D)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (aabb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Include_" data-uid="VRageMath.BoundingBoxD.Include*"></a>
  <h4 id="VRageMath_BoundingBoxD_Include_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.Include(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@)">Include(ref Vector3D, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Include(ref Vector3D p0, ref Vector3D p1, ref Vector3D p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Inflate_" data-uid="VRageMath.BoundingBoxD.Inflate*"></a>
  <h4 id="VRageMath_BoundingBoxD_Inflate_System_Double_" data-uid="VRageMath.BoundingBoxD.Inflate(System.Double)">Inflate(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Inflate(double size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Inflate_" data-uid="VRageMath.BoundingBoxD.Inflate*"></a>
  <h4 id="VRageMath_BoundingBoxD_Inflate_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Inflate(VRageMath.Vector3D)">Inflate(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Inflate(Vector3D size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_InflateToMinimum_" data-uid="VRageMath.BoundingBoxD.InflateToMinimum*"></a>
  <h4 id="VRageMath_BoundingBoxD_InflateToMinimum_System_Double_" data-uid="VRageMath.BoundingBoxD.InflateToMinimum(System.Double)">InflateToMinimum(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InflateToMinimum(double minimumSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minimumSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_InflateToMinimum_" data-uid="VRageMath.BoundingBoxD.InflateToMinimum*"></a>
  <h4 id="VRageMath_BoundingBoxD_InflateToMinimum_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.InflateToMinimum(VRageMath.Vector3D)">InflateToMinimum(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InflateToMinimum(Vector3D minimumSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">minimumSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersect_" data-uid="VRageMath.BoundingBoxD.Intersect*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersect_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.Intersect(VRageMath.BoundingBoxD)">Intersect(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Returns bounding box which is intersection of this and box<br>
Result is invalid box when there's no intersection (Min &gt; Max)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Intersect(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersect_" data-uid="VRageMath.BoundingBoxD.Intersect*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersect_VRageMath_LineD__System_Double__System_Double__" data-uid="VRageMath.BoundingBoxD.Intersect(VRageMath.LineD@,System.Double@,System.Double@)">Intersect(ref LineD, out Double, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersect(ref LineD line, out double t1, out double t2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">t1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">t2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersect_" data-uid="VRageMath.BoundingBoxD.Intersect*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersect_VRageMath_LineD__VRageMath_LineD__" data-uid="VRageMath.BoundingBoxD.Intersect(VRageMath.LineD@,VRageMath.LineD@)">Intersect(ref LineD, out LineD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersect(ref LineD line, out LineD intersectedLine)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">intersectedLine</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersect_" data-uid="VRageMath.BoundingBoxD.Intersect*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersect_VRageMath_RayD__System_Double__System_Double__" data-uid="VRageMath.BoundingBoxD.Intersect(VRageMath.RayD@,System.Double@,System.Double@)">Intersect(ref RayD, out Double, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersect(ref RayD ray, out double tmin, out double tmax)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">tmin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">tmax</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingBox__System_Boolean__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingBox@,System.Boolean@)">Intersects(ref BoundingBox, out Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingBoxD)">Intersects(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects another BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingBoxD@)">Intersects(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingBoxD__System_Boolean__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingBoxD@,System.Boolean@)">Intersects(ref BoundingBoxD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects another BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBoxD box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingBox instances intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingFrustumD)">Intersects(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingSphereD)">Intersects(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingSphereD@)">Intersects(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_BoundingSphereD__System_Boolean__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.BoundingSphereD@,System.Boolean@)">Intersects(ref BoundingSphereD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphereD sphere, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingBox and BoundingSphere intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_LineD__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.LineD@)">Intersects(ref LineD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref LineD line)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_LineD__System_Double__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.LineD@,System.Double@)">Intersects(ref LineD, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref LineD line, out double distance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">distance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_PlaneD_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.PlaneD)">Intersects(PlaneD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(PlaneD plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_PlaneD__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.PlaneD@,VRageMath.PlaneIntersectionType@)">Intersects(ref PlaneD, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref PlaneD plane, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the BoundingBox intersects the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_Ray_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.Ray)">Intersects(Ray)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;double&gt; Intersects(Ray ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_RayD_" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.RayD)">Intersects(RayD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;double&gt; Intersects(RayD ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Intersects_" data-uid="VRageMath.BoundingBoxD.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxD_Intersects_VRageMath_RayD__System_Nullable_System_Double___" data-uid="VRageMath.BoundingBoxD.Intersects(VRageMath.RayD@,System.Nullable{System.Double}@)">Intersects(ref RayD, out Nullable&lt;Double&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref RayD ray, out Nullable&lt;double&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingBox, or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_IntersectsTriangle_" data-uid="VRageMath.BoundingBoxD.IntersectsTriangle*"></a>
  <h4 id="VRageMath_BoundingBoxD_IntersectsTriangle_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.IntersectsTriangle(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">IntersectsTriangle(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IntersectsTriangle(Vector3D v0, Vector3D v1, Vector3D v2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_IntersectsTriangle_" data-uid="VRageMath.BoundingBoxD.IntersectsTriangle*"></a>
  <h4 id="VRageMath_BoundingBoxD_IntersectsTriangle_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRageMath.BoundingBoxD.IntersectsTriangle(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.Vector3D@)">IntersectsTriangle(ref Vector3D, ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IntersectsTriangle(ref Vector3D v0, ref Vector3D v1, ref Vector3D v2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_ProjectedArea_" data-uid="VRageMath.BoundingBoxD.ProjectedArea*"></a>
  <h4 id="VRageMath_BoundingBoxD_ProjectedArea_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.ProjectedArea(VRageMath.Vector3D)">ProjectedArea(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double ProjectedArea(Vector3D viewDir)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">viewDir</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Round_" data-uid="VRageMath.BoundingBoxD.Round*"></a>
  <h4 id="VRageMath_BoundingBoxD_Round" data-uid="VRageMath.BoundingBoxD.Round">Round()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Round()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Round_" data-uid="VRageMath.BoundingBoxD.Round*"></a>
  <h4 id="VRageMath_BoundingBoxD_Round_System_Int32_" data-uid="VRageMath.BoundingBoxD.Round(System.Int32)">Round(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Round(int decimals)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimals</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_ToString_" data-uid="VRageMath.BoundingBoxD.ToString*"></a>
  <h4 id="VRageMath_BoundingBoxD_ToString" data-uid="VRageMath.BoundingBoxD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_TransformFast_" data-uid="VRageMath.BoundingBoxD.TransformFast*"></a>
  <h4 id="VRageMath_BoundingBoxD_TransformFast_VRageMath_MatrixD_" data-uid="VRageMath.BoundingBoxD.TransformFast(VRageMath.MatrixD)">TransformFast(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transform this AABB by matrix. Matrix has to be only rotation and translation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD TransformFast(MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td><p>transformation matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><p>transformed aabb</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_TransformFast_" data-uid="VRageMath.BoundingBoxD.TransformFast*"></a>
  <h4 id="VRageMath_BoundingBoxD_TransformFast_VRageMath_MatrixD__" data-uid="VRageMath.BoundingBoxD.TransformFast(VRageMath.MatrixD@)">TransformFast(ref MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transform this AABB by matrix. Matrix has to be only rotation and translation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD TransformFast(ref MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td><p>transformation matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><p>transformed aabb</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_TransformFast_" data-uid="VRageMath.BoundingBoxD.TransformFast*"></a>
  <h4 id="VRageMath_BoundingBoxD_TransformFast_VRageMath_MatrixD__VRageMath_BoundingBoxD__" data-uid="VRageMath.BoundingBoxD.TransformFast(VRageMath.MatrixD@,VRageMath.BoundingBoxD@)">TransformFast(ref MatrixD, ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Transform this AABB by matrix. Matrix has to be only rotation and translation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TransformFast(ref MatrixD m, ref BoundingBoxD bb)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td><p>transformation matrix</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">bb</span></td>
        <td><p>output transformed aabb</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_TransformSlow_" data-uid="VRageMath.BoundingBoxD.TransformSlow*"></a>
  <h4 id="VRageMath_BoundingBoxD_TransformSlow_VRageMath_MatrixD_" data-uid="VRageMath.BoundingBoxD.TransformSlow(VRageMath.MatrixD)">TransformSlow(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transform this AABB by matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD TransformSlow(MatrixD m)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">m</span></td>
        <td><p>transformation matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><p>transformed aabb</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_TransformSlow_" data-uid="VRageMath.BoundingBoxD.TransformSlow*"></a>
  <h4 id="VRageMath_BoundingBoxD_TransformSlow_VRageMath_MatrixD__" data-uid="VRageMath.BoundingBoxD.TransformSlow(VRageMath.MatrixD@)">TransformSlow(ref MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transform this AABB by matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD TransformSlow(ref MatrixD worldMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td><p>transformation matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><p>transformed aabb</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Translate_" data-uid="VRageMath.BoundingBoxD.Translate*"></a>
  <h4 id="VRageMath_BoundingBoxD_Translate_VRageMath_MatrixD_" data-uid="VRageMath.BoundingBoxD.Translate(VRageMath.MatrixD)">Translate(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Translate</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Translate(MatrixD worldMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_Translate_" data-uid="VRageMath.BoundingBoxD.Translate*"></a>
  <h4 id="VRageMath_BoundingBoxD_Translate_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.Translate(VRageMath.Vector3D)">Translate(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Translate</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD Translate(Vector3D vctTranlsation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vctTranlsation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingBoxD_op_Addition_" data-uid="VRageMath.BoundingBoxD.op_Addition*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Addition_VRageMath_BoundingBoxD_VRageMath_Vector3D_" data-uid="VRageMath.BoundingBoxD.op_Addition(VRageMath.BoundingBoxD,VRageMath.Vector3D)">Addition(BoundingBoxD, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxD operator +(BoundingBoxD a, Vector3D b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_op_Equality_" data-uid="VRageMath.BoundingBoxD.op_Equality*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Equality_VRageMath_BoundingBoxD_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.op_Equality(VRageMath.BoundingBoxD,VRageMath.BoundingBoxD)">Equality(BoundingBoxD, BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingBoxD a, BoundingBoxD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>BoundingBox to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>BoundingBox to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_op_Explicit_" data-uid="VRageMath.BoundingBoxD.op_Explicit*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Explicit_VRageMath_BoundingBoxD__VRageMath_BoundingBox" data-uid="VRageMath.BoundingBoxD.op_Explicit(VRageMath.BoundingBoxD)~VRageMath.BoundingBox">Explicit(BoundingBoxD to BoundingBox)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator BoundingBox(BoundingBoxD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_op_Implicit_" data-uid="VRageMath.BoundingBoxD.op_Implicit*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Implicit_VRageMath_BoundingBox__VRageMath_BoundingBoxD" data-uid="VRageMath.BoundingBoxD.op_Implicit(VRageMath.BoundingBox)~VRageMath.BoundingBoxD">Implicit(BoundingBox to BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator BoundingBoxD(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_op_Implicit_" data-uid="VRageMath.BoundingBoxD.op_Implicit*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Implicit_VRageMath_BoundingBoxI__VRageMath_BoundingBoxD" data-uid="VRageMath.BoundingBoxD.op_Implicit(VRageMath.BoundingBoxI)~VRageMath.BoundingBoxD">Implicit(BoundingBoxI to BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator BoundingBoxD(BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxD_op_Inequality_" data-uid="VRageMath.BoundingBoxD.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingBoxD_op_Inequality_VRageMath_BoundingBoxD_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingBoxD.op_Inequality(VRageMath.BoundingBoxD,VRageMath.BoundingBoxD)">Inequality(BoundingBoxD, BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingBoxD a, BoundingBoxD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
