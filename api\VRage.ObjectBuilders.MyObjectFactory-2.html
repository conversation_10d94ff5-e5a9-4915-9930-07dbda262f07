﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectFactory&lt;TA<PERSON><PERSON><PERSON>e, TCreatedObjectBase&gt;
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectFactory&lt;TAttribute, TCreatedObjectBase&gt;
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders.MyObjectFactory`2">
  
  
  <h1 id="VRage_ObjectBuilders_MyObjectFactory_2" data-uid="VRage.ObjectBuilders.MyObjectFactory`2" class="text-break">Class MyObjectFactory&lt;TAttribute, TCreatedObjectBase&gt;
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectFactory&lt;TAttribute, TCreatedObjectBase&gt;</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ObjectBuilders.html">VRage.ObjectBuilders</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ObjectBuilders_MyObjectFactory_2_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyObjectFactory&lt;TAttribute, TCreatedObjectBase&gt; : Object where TAttribute : MyFactoryTagAttribute where TCreatedObjectBase : class</code></pre>
  </div>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TAttribute</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TCreatedObjectBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2__ctor_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.#ctor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2__ctor" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.#ctor">MyObjectFactory()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectFactory()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_Attributes_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.Attributes*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_Attributes" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.Attributes">Attributes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DictionaryValuesReader&lt;Type, TAttribute&gt; Attributes { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Collections.DictionaryValuesReader-2.html">DictionaryValuesReader</a>&lt;<span class="xref">System.Type</span>, TAttribute&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance(VRage.ObjectBuilders.MyObjectBuilderType)">CreateInstance(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TCreatedObjectBase CreateInstance(MyObjectBuilderType objectBuilderType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">objectBuilderType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TCreatedObjectBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance__1" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance``1">CreateInstance&lt;TBase&gt;()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TBase CreateInstance&lt;TBase&gt;()
    where TBase : class, TCreatedObjectBase, new()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_CreateInstance__1_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateInstance``1(VRage.ObjectBuilders.MyObjectBuilderType)">CreateInstance&lt;TBase&gt;(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TBase CreateInstance&lt;TBase&gt;(MyObjectBuilderType objectBuilderType)
    where TBase : class, TCreatedObjectBase</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">objectBuilderType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TBase</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_CreateObjectBuilder_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateObjectBuilder*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_CreateObjectBuilder__1__1_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateObjectBuilder``1(`1)">CreateObjectBuilder&lt;TObjectBuilder&gt;(TCreatedObjectBase)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TObjectBuilder CreateObjectBuilder&lt;TObjectBuilder&gt;(TCreatedObjectBase instance)
    where TObjectBuilder : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TCreatedObjectBase</span></td>
        <td><span class="parametername">instance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TObjectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TObjectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_CreateObjectBuilder_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateObjectBuilder*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_CreateObjectBuilder__1_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.CreateObjectBuilder``1(System.Type)">CreateObjectBuilder&lt;TObjectBuilder&gt;(Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TObjectBuilder CreateObjectBuilder&lt;TObjectBuilder&gt;(Type instanceType)
    where TObjectBuilder : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">instanceType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TObjectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TObjectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_GetProducedType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.GetProducedType*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_GetProducedType_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.GetProducedType(VRage.ObjectBuilders.MyObjectBuilderType)">GetProducedType(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Type GetProducedType(MyObjectBuilderType objectBuilderType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">objectBuilderType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterDescriptor_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterDescriptor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterDescriptor__0_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterDescriptor(`0,System.Type)">RegisterDescriptor(TAttribute, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterDescriptor(TAttribute descriptor, Type type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TAttribute</span></td>
        <td><span class="parametername">descriptor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromAssembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromAssembly_System_Reflection_Assembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromAssembly(System.Reflection.Assembly)">RegisterFromAssembly(Assembly)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterFromAssembly(Assembly assembly)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span></td>
        <td><span class="parametername">assembly</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromAssembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromAssembly_System_Reflection_Assembly___" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromAssembly(System.Reflection.Assembly[])">RegisterFromAssembly(Assembly[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterFromAssembly(Assembly[] assemblies)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span>[]</td>
        <td><span class="parametername">assemblies</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromCreatedObjectAssembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromCreatedObjectAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_RegisterFromCreatedObjectAssembly" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.RegisterFromCreatedObjectAssembly">RegisterFromCreatedObjectAssembly()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterFromCreatedObjectAssembly()</code></pre>
  </div>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_TryGetProducedType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.TryGetProducedType*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_TryGetProducedType_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.TryGetProducedType(VRage.ObjectBuilders.MyObjectBuilderType)">TryGetProducedType(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Type TryGetProducedType(MyObjectBuilderType objectBuilderType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">objectBuilderType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectFactory_2_UnregisterFromAssembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.UnregisterFromAssembly*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectFactory_2_UnregisterFromAssembly_System_Reflection_Assembly_" data-uid="VRage.ObjectBuilders.MyObjectFactory`2.UnregisterFromAssembly(System.Reflection.Assembly)">UnregisterFromAssembly(Assembly)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UnregisterFromAssembly(Assembly assembly)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span></td>
        <td><span class="parametername">assembly</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
