﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyTextSurfaceScriptFactory
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyTextSurfaceScriptFactory
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx ********">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory">
  
  
  <h1 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory" class="text-break">Class MyTextSurfaceScriptFactory
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyTextSurfaceScriptFactory</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.html">Sandbox.Game.GameSystems.TextSurfaceScripts</a></h6>
  <h6><strong>Assembly</strong>: Sandbox.Game.dll</h6>
  <h5 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyTextSurfaceScriptFactory : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory__ctor_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.#ctor*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory__ctor" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.#ctor">MyTextSurfaceScriptFactory()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyTextSurfaceScriptFactory()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_Instance_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.Instance*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_Instance" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.Instance">Instance</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTextSurfaceScriptFactory Instance { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.html">MyTextSurfaceScriptFactory</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_Scripts_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.Scripts*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_Scripts" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.Scripts">Scripts</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DictionaryReader&lt;string, MyTextSurfaceScriptFactory.ScriptInfo&gt; Scripts { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Collections.DictionaryReader-2.html">DictionaryReader</a>&lt;<span class="xref">System.String</span>, <a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.ScriptInfo.html">MyTextSurfaceScriptFactory.ScriptInfo</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_CreateScript_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.CreateScript*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_CreateScript_System_String_Sandbox_ModAPI_Ingame_IMyTextSurface_VRage_Game_ModAPI_Ingame_IMyCubeBlock_VRageMath_Vector2_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.CreateScript(System.String,Sandbox.ModAPI.Ingame.IMyTextSurface,VRage.Game.ModAPI.Ingame.IMyCubeBlock,VRageMath.Vector2)">CreateScript(String, IMyTextSurface, IMyCubeBlock, Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IMyTextSurfaceScript CreateScript(string id, IMyTextSurface surface, IMyCubeBlock block, Vector2 size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">id</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="Sandbox.ModAPI.Ingame.IMyTextSurface.html">IMyTextSurface</a></td>
        <td><span class="parametername">surface</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.Ingame.IMyCubeBlock.html">IMyCubeBlock</a></td>
        <td><span class="parametername">block</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Sandbox.Game.GameSystems.TextSurfaceScripts.IMyTextSurfaceScript.html">IMyTextSurfaceScript</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_LoadScripts_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.LoadScripts*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_LoadScripts" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.LoadScripts">LoadScripts()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void LoadScripts()</code></pre>
  </div>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_RegisterFromAssembly_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.RegisterFromAssembly*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_RegisterFromAssembly_System_Collections_Generic_IEnumerable_System_Reflection_Assembly__" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.RegisterFromAssembly(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">RegisterFromAssembly(IEnumerable&lt;Assembly&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterFromAssembly(IEnumerable&lt;Assembly&gt; assemblies)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<span class="xref">System.Reflection.Assembly</span>&gt;</td>
        <td><span class="parametername">assemblies</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_RegisterFromAssembly_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.RegisterFromAssembly*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_RegisterFromAssembly_System_Reflection_Assembly_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.RegisterFromAssembly(System.Reflection.Assembly)">RegisterFromAssembly(Assembly)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RegisterFromAssembly(Assembly assembly)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Reflection.Assembly</span></td>
        <td><span class="parametername">assembly</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_UnloadScripts_" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.UnloadScripts*"></a>
  <h4 id="Sandbox_Game_GameSystems_TextSurfaceScripts_MyTextSurfaceScriptFactory_UnloadScripts" data-uid="Sandbox.Game.GameSystems.TextSurfaceScripts.MyTextSurfaceScriptFactory.UnloadScripts">UnloadScripts()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void UnloadScripts()</code></pre>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
