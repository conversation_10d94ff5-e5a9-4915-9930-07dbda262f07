﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyIsoMesh
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyIsoMesh
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Voxels.MyIsoMesh">
  
  
  <h1 id="VRage_Voxels_MyIsoMesh" data-uid="VRage.Voxels.MyIsoMesh" class="text-break">Class MyIsoMesh
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyIsoMesh</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Voxels.html">VRage.Voxels</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Voxels_MyIsoMesh_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MyIsoMesh : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Voxels_MyIsoMesh__ctor_" data-uid="VRage.Voxels.MyIsoMesh.#ctor*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh__ctor" data-uid="VRage.Voxels.MyIsoMesh.#ctor">MyIsoMesh()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyIsoMesh()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_CellEnd" data-uid="VRage.Voxels.MyIsoMesh.CellEnd">CellEnd</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I CellEnd</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Cells" data-uid="VRage.Voxels.MyIsoMesh.Cells">Cells</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;Vector3I&gt; Cells</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_CellStart" data-uid="VRage.Voxels.MyIsoMesh.CellStart">CellStart</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I CellStart</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_ColorShiftHSV" data-uid="VRage.Voxels.MyIsoMesh.ColorShiftHSV">ColorShiftHSV</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;uint&gt; ColorShiftHSV</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<span class="xref">System.UInt32</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Densities" data-uid="VRage.Voxels.MyIsoMesh.Densities">Densities</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;sbyte&gt; Densities</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<span class="xref">System.SByte</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Lod" data-uid="VRage.Voxels.MyIsoMesh.Lod">Lod</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Lod</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Materials" data-uid="VRage.Voxels.MyIsoMesh.Materials">Materials</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;byte&gt; Materials</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<span class="xref">System.Byte</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Normals" data-uid="VRage.Voxels.MyIsoMesh.Normals">Normals</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;Vector3&gt; Normals</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_PositionOffset" data-uid="VRage.Voxels.MyIsoMesh.PositionOffset">PositionOffset</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D PositionOffset</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Positions" data-uid="VRage.Voxels.MyIsoMesh.Positions">Positions</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;Vector3&gt; Positions</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_PositionScale" data-uid="VRage.Voxels.MyIsoMesh.PositionScale">PositionScale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 PositionScale</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Voxels_MyIsoMesh_Triangles" data-uid="VRage.Voxels.MyIsoMesh.Triangles">Triangles</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyList&lt;MyVoxelTriangle&gt; Triangles</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.MyList</span>&lt;<span class="xref">VRage.Game.Voxels.MyVoxelTriangle</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Voxels_MyIsoMesh_Size_" data-uid="VRage.Voxels.MyIsoMesh.Size*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_Size" data-uid="VRage.Voxels.MyIsoMesh.Size">Size</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_TrianglesCount_" data-uid="VRage.Voxels.MyIsoMesh.TrianglesCount*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_TrianglesCount" data-uid="VRage.Voxels.MyIsoMesh.TrianglesCount">TrianglesCount</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int TrianglesCount { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_VerticesCount_" data-uid="VRage.Voxels.MyIsoMesh.VerticesCount*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_VerticesCount" data-uid="VRage.Voxels.MyIsoMesh.VerticesCount">VerticesCount</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int VerticesCount { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Voxels_MyIsoMesh_Clear_" data-uid="VRage.Voxels.MyIsoMesh.Clear*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_Clear" data-uid="VRage.Voxels.MyIsoMesh.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRage_Voxels_MyIsoMesh_CopyFromNative_" data-uid="VRage.Voxels.MyIsoMesh.CopyFromNative*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_CopyFromNative_VRage_Voxels_VrVoxelMesh_" data-uid="VRage.Voxels.MyIsoMesh.CopyFromNative(VRage.Voxels.VrVoxelMesh)">CopyFromNative(VrVoxelMesh)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CopyFromNative(VrVoxelMesh vrMesh)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Voxels.VrVoxelMesh</span></td>
        <td><span class="parametername">vrMesh</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_FromNative_" data-uid="VRage.Voxels.MyIsoMesh.FromNative*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_FromNative_VRage_Voxels_VrVoxelMesh_" data-uid="VRage.Voxels.MyIsoMesh.FromNative(VRage.Voxels.VrVoxelMesh)">FromNative(VrVoxelMesh)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyIsoMesh FromNative(VrVoxelMesh nativeMesh)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Voxels.VrVoxelMesh</span></td>
        <td><span class="parametername">nativeMesh</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyIsoMesh.html">MyIsoMesh</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_GetUnpackedPosition_" data-uid="VRage.Voxels.MyIsoMesh.GetUnpackedPosition*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_GetUnpackedPosition_System_Int32_VRageMath_Vector3__" data-uid="VRage.Voxels.MyIsoMesh.GetUnpackedPosition(System.Int32,VRageMath.Vector3@)">GetUnpackedPosition(Int32, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetUnpackedPosition(int idx, out Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">idx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_GetUnpackedVertex_" data-uid="VRage.Voxels.MyIsoMesh.GetUnpackedVertex*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_GetUnpackedVertex_System_Int32_VRage_Game_Voxels_MyVoxelVertex__" data-uid="VRage.Voxels.MyIsoMesh.GetUnpackedVertex(System.Int32,VRage.Game.Voxels.MyVoxelVertex@)">GetUnpackedVertex(Int32, out MyVoxelVertex)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetUnpackedVertex(int idx, out MyVoxelVertex vertex)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">idx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRage.Game.Voxels.MyVoxelVertex</span></td>
        <td><span class="parametername">vertex</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_IsEdge_" data-uid="VRage.Voxels.MyIsoMesh.IsEdge*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_IsEdge_VRageMath_Vector3I__" data-uid="VRage.Voxels.MyIsoMesh.IsEdge(VRageMath.Vector3I@)">IsEdge(ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsEdge(ref Vector3I cell)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">cell</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_IsEmpty_" data-uid="VRage.Voxels.MyIsoMesh.IsEmpty*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_IsEmpty_VRage_Voxels_MyIsoMesh_" data-uid="VRage.Voxels.MyIsoMesh.IsEmpty(VRage.Voxels.MyIsoMesh)">IsEmpty(MyIsoMesh)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsEmpty(MyIsoMesh self)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyIsoMesh.html">MyIsoMesh</a></td>
        <td><span class="parametername">self</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_Reserve_" data-uid="VRage.Voxels.MyIsoMesh.Reserve*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_Reserve_System_Int32_System_Int32_" data-uid="VRage.Voxels.MyIsoMesh.Reserve(System.Int32,System.Int32)">Reserve(Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Reserve(int vertexCount, int triangleCount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">vertexCount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">triangleCount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_Resize_" data-uid="VRage.Voxels.MyIsoMesh.Resize*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_Resize_System_Int32_System_Int32_" data-uid="VRage.Voxels.MyIsoMesh.Resize(System.Int32,System.Int32)">Resize(Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Resize(int vertexCount, int triangleCount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">vertexCount</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">triangleCount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_WriteTriangle_" data-uid="VRage.Voxels.MyIsoMesh.WriteTriangle*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_WriteTriangle_System_Int32_System_Int32_System_Int32_" data-uid="VRage.Voxels.MyIsoMesh.WriteTriangle(System.Int32,System.Int32,System.Int32)">WriteTriangle(Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void WriteTriangle(int v0, int v1, int v2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_MyIsoMesh_WriteVertex_" data-uid="VRage.Voxels.MyIsoMesh.WriteVertex*"></a>
  <h4 id="VRage_Voxels_MyIsoMesh_WriteVertex_VRageMath_Vector3I__VRageMath_Vector3__VRageMath_Vector3__System_Byte_System_UInt32_" data-uid="VRage.Voxels.MyIsoMesh.WriteVertex(VRageMath.Vector3I@,VRageMath.Vector3@,VRageMath.Vector3@,System.Byte,System.UInt32)">WriteVertex(ref Vector3I, ref Vector3, ref Vector3, Byte, UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int WriteVertex(ref Vector3I cell, ref Vector3 position, ref Vector3 normal, byte material, uint colorShift)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">cell</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">colorShift</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
