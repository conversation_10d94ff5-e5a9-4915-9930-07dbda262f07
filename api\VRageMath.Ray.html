﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Ray
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Ray
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Ray">
  
  
  <h1 id="VRageMath_Ray" data-uid="VRageMath.Ray" class="text-break">Class Ray
  </h1>
  <div class="markdown level0 summary"><p>Defines a ray.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Ray</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Ray_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Ray : ValueType, IEquatable&lt;Ray&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Ray__ctor_" data-uid="VRageMath.Ray.#ctor*"></a>
  <h4 id="VRageMath_Ray__ctor_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Ray.#ctor(VRageMath.Vector3,VRageMath.Vector3)">Ray(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Ray(Vector3 position, Vector3 direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The starting point of the Ray.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>Unit vector describing the direction of the Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Ray_Direction" data-uid="VRageMath.Ray.Direction">Direction</h4>
  <div class="markdown level1 summary"><p>Unit vector specifying the direction the Ray is pointing.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Direction</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Ray_Position" data-uid="VRageMath.Ray.Position">Position</h4>
  <div class="markdown level1 summary"><p>Specifies the starting point of the Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Position</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Ray_Equals_" data-uid="VRageMath.Ray.Equals*"></a>
  <h4 id="VRageMath_Ray_Equals_System_Object_" data-uid="VRageMath.Ray.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of Ray are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Equals_" data-uid="VRageMath.Ray.Equals*"></a>
  <h4 id="VRageMath_Ray_Equals_VRageMath_Ray_" data-uid="VRageMath.Ray.Equals(VRageMath.Ray)">Equals(Ray)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Ray is equal to the current Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Ray other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Ray to compare with the current Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_GetHashCode_" data-uid="VRageMath.Ray.GetHashCode*"></a>
  <h4 id="VRageMath_Ray_GetHashCode" data-uid="VRageMath.Ray.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_BoundingBox_" data-uid="VRageMath.Ray.Intersects(VRageMath.BoundingBox)">Intersects(BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Checks whether the Ray intersects a specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with the Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_BoundingBox__System_Nullable_System_Single___" data-uid="VRageMath.Ray.Intersects(VRageMath.BoundingBox@,System.Nullable{System.Single}@)">Intersects(ref BoundingBox, out Nullable&lt;Single&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Ray intersects a BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox box, out Nullable&lt;float&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingBox or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_BoundingFrustum_" data-uid="VRageMath.Ray.Intersects(VRageMath.BoundingFrustum)">Intersects(BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Checks whether the Ray intersects a specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection with the Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_BoundingSphere_" data-uid="VRageMath.Ray.Intersects(VRageMath.BoundingSphere)">Intersects(BoundingSphere)</h4>
  <div class="markdown level1 summary"><p>Checks whether the Ray intersects a specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with the Ray.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_BoundingSphere__System_Nullable_System_Single___" data-uid="VRageMath.Ray.Intersects(VRageMath.BoundingSphere@,System.Nullable{System.Single}@)">Intersects(ref BoundingSphere, out Nullable&lt;Single&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Ray intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphere sphere, out Nullable&lt;float&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingSphere or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_Plane_" data-uid="VRageMath.Ray.Intersects(VRageMath.Plane)">Intersects(Plane)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Ray intersects a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane with which to calculate this Ray's intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_Intersects_" data-uid="VRageMath.Ray.Intersects*"></a>
  <h4 id="VRageMath_Ray_Intersects_VRageMath_Plane__System_Nullable_System_Single___" data-uid="VRageMath.Ray.Intersects(VRageMath.Plane@,System.Nullable{System.Single}@)">Intersects(ref Plane, out Nullable&lt;Single&gt;)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Ray intersects a specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Plane plane, out Nullable&lt;float&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane with which to calculate this Ray's intersection.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance at which this Ray intersects the specified Plane, or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_ToString_" data-uid="VRageMath.Ray.ToString*"></a>
  <h4 id="VRageMath_Ray_ToString" data-uid="VRageMath.Ray.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Ray_op_Equality_" data-uid="VRageMath.Ray.op_Equality*"></a>
  <h4 id="VRageMath_Ray_op_Equality_VRageMath_Ray_VRageMath_Ray_" data-uid="VRageMath.Ray.op_Equality(VRageMath.Ray,VRageMath.Ray)">Equality(Ray, Ray)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of Ray are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Ray a, Ray b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Ray_op_Inequality_" data-uid="VRageMath.Ray.op_Inequality*"></a>
  <h4 id="VRageMath_Ray_op_Inequality_VRageMath_Ray_VRageMath_Ray_" data-uid="VRageMath.Ray.op_Inequality(VRageMath.Ray,VRageMath.Ray)">Inequality(Ray, Ray)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of Ray are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Ray a, Ray b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
