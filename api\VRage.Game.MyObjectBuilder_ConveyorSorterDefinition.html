﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_ConveyorSorterDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_ConveyorSorterDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition" class="text-break">Class MyObjectBuilder_ConveyorSorterDefinition
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></div>
    <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html">MyObjectBuilder_PhysicalModelDefinition</a></div>
    <div class="level4"><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html">MyObjectBuilder_CubeBlockDefinition</a></div>
    <div class="level5"><a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlockDefinition.html">MyObjectBuilder_FunctionalBlockDefinition</a></div>
    <div class="level6"><span class="xref">MyObjectBuilder_ConveyorSorterDefinition</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlockDefinition.html#VRage_Game_MyObjectBuilder_FunctionalBlockDefinition_ScreenAreas">MyObjectBuilder_FunctionalBlockDefinition.ScreenAreas</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_VoxelPlacement">MyObjectBuilder_CubeBlockDefinition.VoxelPlacement</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_SilenceableByShipSoundSystem">MyObjectBuilder_CubeBlockDefinition.SilenceableByShipSoundSystem</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_CubeSize">MyObjectBuilder_CubeBlockDefinition.CubeSize</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BlockTopology">MyObjectBuilder_CubeBlockDefinition.BlockTopology</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Size">MyObjectBuilder_CubeBlockDefinition.Size</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_ModelOffset">MyObjectBuilder_CubeBlockDefinition.ModelOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_CubeDefinition">MyObjectBuilder_CubeBlockDefinition.CubeDefinition</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Components">MyObjectBuilder_CubeBlockDefinition.Components</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Effects">MyObjectBuilder_CubeBlockDefinition.Effects</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_CriticalComponent">MyObjectBuilder_CubeBlockDefinition.CriticalComponent</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MountPoints">MyObjectBuilder_CubeBlockDefinition.MountPoints</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Variants">MyObjectBuilder_CubeBlockDefinition.Variants</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_EntityComponents">MyObjectBuilder_CubeBlockDefinition.EntityComponents</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_PhysicsOption">MyObjectBuilder_CubeBlockDefinition.PhysicsOption</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BuildProgressModels">MyObjectBuilder_CubeBlockDefinition.BuildProgressModels</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BlockPairName">MyObjectBuilder_CubeBlockDefinition.BlockPairName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Center">MyObjectBuilder_CubeBlockDefinition.Center</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MirroringX">MyObjectBuilder_CubeBlockDefinition.MirroringX</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MirroringY">MyObjectBuilder_CubeBlockDefinition.MirroringY</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MirroringZ">MyObjectBuilder_CubeBlockDefinition.MirroringZ</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DeformationRatio">MyObjectBuilder_CubeBlockDefinition.DeformationRatio</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_EdgeType">MyObjectBuilder_CubeBlockDefinition.EdgeType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BuildTimeSeconds">MyObjectBuilder_CubeBlockDefinition.BuildTimeSeconds</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DisassembleRatio">MyObjectBuilder_CubeBlockDefinition.DisassembleRatio</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_AutorotateMode">MyObjectBuilder_CubeBlockDefinition.AutorotateMode</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MirroringBlock">MyObjectBuilder_CubeBlockDefinition.MirroringBlock</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_UseModelIntersection">MyObjectBuilder_CubeBlockDefinition.UseModelIntersection</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_PrimarySound">MyObjectBuilder_CubeBlockDefinition.PrimarySound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_ActionSound">MyObjectBuilder_CubeBlockDefinition.ActionSound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BuildType">MyObjectBuilder_CubeBlockDefinition.BuildType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BuildMaterial">MyObjectBuilder_CubeBlockDefinition.BuildMaterial</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_NavigationDefinition">MyObjectBuilder_CubeBlockDefinition.NavigationDefinition</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_GuiVisible">MyObjectBuilder_CubeBlockDefinition.GuiVisible</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BlockVariants">MyObjectBuilder_CubeBlockDefinition.BlockVariants</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Direction">MyObjectBuilder_CubeBlockDefinition.Direction</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Rotation">MyObjectBuilder_CubeBlockDefinition.Rotation</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_GeneratedBlocks">MyObjectBuilder_CubeBlockDefinition.GeneratedBlocks</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_GeneratedBlockType">MyObjectBuilder_CubeBlockDefinition.GeneratedBlockType</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Mirrored">MyObjectBuilder_CubeBlockDefinition.Mirrored</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamageEffectId">MyObjectBuilder_CubeBlockDefinition.DamageEffectId</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DestroyEffect">MyObjectBuilder_CubeBlockDefinition.DestroyEffect</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DestroySound">MyObjectBuilder_CubeBlockDefinition.DestroySound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Skeleton">MyObjectBuilder_CubeBlockDefinition.Skeleton</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_RandomRotation">MyObjectBuilder_CubeBlockDefinition.RandomRotation</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_IsAirTight">MyObjectBuilder_CubeBlockDefinition.IsAirTight</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_IsStandAlone">MyObjectBuilder_CubeBlockDefinition.IsStandAlone</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_HasPhysics">MyObjectBuilder_CubeBlockDefinition.HasPhysics</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_UseNeighbourOxygenRooms">MyObjectBuilder_CubeBlockDefinition.UseNeighbourOxygenRooms</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_Points">MyObjectBuilder_CubeBlockDefinition.Points</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MaxIntegrity">MyObjectBuilder_CubeBlockDefinition.MaxIntegrity</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_BuildProgressToPlaceGeneratedBlocks">MyObjectBuilder_CubeBlockDefinition.BuildProgressToPlaceGeneratedBlocks</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamagedSound">MyObjectBuilder_CubeBlockDefinition.DamagedSound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_EmissiveColorPreset">MyObjectBuilder_CubeBlockDefinition.EmissiveColorPreset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_GeneralDamageMultiplier">MyObjectBuilder_CubeBlockDefinition.GeneralDamageMultiplier</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamageEffectName">MyObjectBuilder_CubeBlockDefinition.DamageEffectName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_UsesDeformation">MyObjectBuilder_CubeBlockDefinition.UsesDeformation</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DestroyEffectOffset">MyObjectBuilder_CubeBlockDefinition.DestroyEffectOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_PCU">MyObjectBuilder_CubeBlockDefinition.PCU</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_PlaceDecals">MyObjectBuilder_CubeBlockDefinition.PlaceDecals</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DepressurizationEffectOffset">MyObjectBuilder_CubeBlockDefinition.DepressurizationEffectOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_TieredUpdateTimes">MyObjectBuilder_CubeBlockDefinition.TieredUpdateTimes</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_TargetingGroups">MyObjectBuilder_CubeBlockDefinition.TargetingGroups</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_PriorityModifier">MyObjectBuilder_CubeBlockDefinition.PriorityModifier</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_NotWorkingPriorityMultiplier">MyObjectBuilder_CubeBlockDefinition.NotWorkingPriorityMultiplier</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamageMultiplierExplosion">MyObjectBuilder_CubeBlockDefinition.DamageMultiplierExplosion</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamageThreshold">MyObjectBuilder_CubeBlockDefinition.DamageThreshold</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DetonateChance">MyObjectBuilder_CubeBlockDefinition.DetonateChance</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_AmmoExplosionEffect">MyObjectBuilder_CubeBlockDefinition.AmmoExplosionEffect</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_AmmoExplosionSound">MyObjectBuilder_CubeBlockDefinition.AmmoExplosionSound</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DamageEffectOffset">MyObjectBuilder_CubeBlockDefinition.DamageEffectOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_AimingOffset">MyObjectBuilder_CubeBlockDefinition.AimingOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MirroringCenter">MyObjectBuilder_CubeBlockDefinition.MirroringCenter</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_DestroyEffectScale">MyObjectBuilder_CubeBlockDefinition.DestroyEffectScale</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_EnableUseObjectSimpleTargeting">MyObjectBuilder_CubeBlockDefinition.EnableUseObjectSimpleTargeting</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_MechanicalTopInitialPlacementOffset">MyObjectBuilder_CubeBlockDefinition.MechanicalTopInitialPlacementOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_WheelPlacementCollider">MyObjectBuilder_CubeBlockDefinition.WheelPlacementCollider</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_AllowInteractionThroughBlock">MyObjectBuilder_CubeBlockDefinition.AllowInteractionThroughBlock</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_UseVanillaPlacementDetection">MyObjectBuilder_CubeBlockDefinition.UseVanillaPlacementDetection</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_NewsletterSubscriptionNeeded">MyObjectBuilder_CubeBlockDefinition.NewsletterSubscriptionNeeded</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_SimpleUpdateVisualOnSkinChange">MyObjectBuilder_CubeBlockDefinition.SimpleUpdateVisualOnSkinChange</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_YesNoToolbarBackground">MyObjectBuilder_CubeBlockDefinition.YesNoToolbarBackground</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_YesNoToolbarYesTooltip">MyObjectBuilder_CubeBlockDefinition.YesNoToolbarYesTooltip</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_YesNoToolbarNoTooltip">MyObjectBuilder_CubeBlockDefinition.YesNoToolbarNoTooltip</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_ShouldSerializeCenter">MyObjectBuilder_CubeBlockDefinition.ShouldSerializeCenter()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlockDefinition.html#VRage_Game_MyObjectBuilder_CubeBlockDefinition_ShouldSerializeMirroringCenter">MyObjectBuilder_CubeBlockDefinition.ShouldSerializeMirroringCenter()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html#VRage_Game_MyObjectBuilder_PhysicalModelDefinition_Model">MyObjectBuilder_PhysicalModelDefinition.Model</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html#VRage_Game_MyObjectBuilder_PhysicalModelDefinition_PhysicalMaterial">MyObjectBuilder_PhysicalModelDefinition.PhysicalMaterial</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html#VRage_Game_MyObjectBuilder_PhysicalModelDefinition_Mass">MyObjectBuilder_PhysicalModelDefinition.Mass</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalModelDefinition.html#VRage_Game_MyObjectBuilder_PhysicalModelDefinition_UseInstanceRender">MyObjectBuilder_PhysicalModelDefinition.UseInstanceRender</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Id">MyObjectBuilder_DefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DisplayName">MyObjectBuilder_DefinitionBase.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Description">MyObjectBuilder_DefinitionBase.Description</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Icons">MyObjectBuilder_DefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Public">MyObjectBuilder_DefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Enabled">MyObjectBuilder_DefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_AvailableInSurvival">MyObjectBuilder_DefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DescriptionArgs">MyObjectBuilder_DefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DLCs">MyObjectBuilder_DefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_ConveyorSorterDefinition : MyObjectBuilder_FunctionalBlockDefinition</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition__ctor_" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition__ctor" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition.#ctor">MyObjectBuilder_ConveyorSorterDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_ConveyorSorterDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition_InventorySize" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition.InventorySize">InventorySize</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 InventorySize</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition_PowerInput" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition.PowerInput">PowerInput</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PowerInput</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ConveyorSorterDefinition_ResourceSinkGroup" data-uid="VRage.Game.MyObjectBuilder_ConveyorSorterDefinition.ResourceSinkGroup">ResourceSinkGroup</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string ResourceSinkGroup</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
