﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_ComponentBase
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_ComponentBase
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase">
  
  
  <h1 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase" class="text-break">Class MyObjectBuilder_ComponentBase
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><span class="xref">MyObjectBuilder_ComponentBase</span></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AiBlockComponent.html">MyObjectBuilder_AiBlockComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AiBlockPowerComponent.html">MyObjectBuilder_AiBlockPowerComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AutopilotComponent.html">MyObjectBuilder_AutopilotComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_BasicMissionAutopilot.html">MyObjectBuilder_BasicMissionAutopilot</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_BasicMissionFollowHome.html">MyObjectBuilder_BasicMissionFollowHome</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_BasicMissionFollowPlayer.html">MyObjectBuilder_BasicMissionFollowPlayer</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ChatBroadcastEntityComponent.html">MyObjectBuilder_ChatBroadcastEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ConveyorEndpointComponent.html">MyObjectBuilder_ConveyorEndpointComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EmotionControllerComponent.html">MyObjectBuilder_EmotionControllerComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventAngleChanged.html">MyObjectBuilder_EventAngleChanged</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventBlockAddedRemoved.html">MyObjectBuilder_EventBlockAddedRemoved</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventBlockIntegrity.html">MyObjectBuilder_EventBlockIntegrity</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventBlockOnOff.html">MyObjectBuilder_EventBlockOnOff</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventCargoFilledEntityComponent.html">MyObjectBuilder_EventCargoFilledEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventCockpitOccupied.html">MyObjectBuilder_EventCockpitOccupied</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventConnectorConnected.html">MyObjectBuilder_EventConnectorConnected</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventConnectorReadyToLock.html">MyObjectBuilder_EventConnectorReadyToLock</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventDistanceToLockedTarget.html">MyObjectBuilder_EventDistanceToLockedTarget</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventDoorOpened.html">MyObjectBuilder_EventDoorOpened</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventGasTankFilled.html">MyObjectBuilder_EventGasTankFilled</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventGridSpeedChanged.html">MyObjectBuilder_EventGridSpeedChanged</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventLandingGearLocked.html">MyObjectBuilder_EventLandingGearLocked</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventMagneticLockReady.html">MyObjectBuilder_EventMagneticLockReady</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventMerged.html">MyObjectBuilder_EventMerged</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventNaturalGravityChanged.html">MyObjectBuilder_EventNaturalGravityChanged</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventPistonPosition.html">MyObjectBuilder_EventPistonPosition</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventPowerOutput.html">MyObjectBuilder_EventPowerOutput</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventRotorHingeAttachedDetached.html">MyObjectBuilder_EventRotorHingeAttachedDetached</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventStoredPower.html">MyObjectBuilder_EventStoredPower</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventSurfaceHeight.html">MyObjectBuilder_EventSurfaceHeight</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EventThrustPercentage.html">MyObjectBuilder_EventThrustPercentage</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_FarmPlotLogic.html">MyObjectBuilder_FarmPlotLogic</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_GlobalEncounterComponent.html">MyObjectBuilder_GlobalEncounterComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ItemProducerComponent.html">MyObjectBuilder_ItemProducerComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_LcdSurfaceComponent.html">MyObjectBuilder_LcdSurfaceComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_LightingComponent.html">MyObjectBuilder_LightingComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_MaintenancePanelComponent.html">MyObjectBuilder_MaintenancePanelComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_MultiTextPanelComponent.html">MyObjectBuilder_MultiTextPanelComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_NewSolarGameLogicComponent.html">MyObjectBuilder_NewSolarGameLogicComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_OffensiveCombatCircleOrbit.html">MyObjectBuilder_OffensiveCombatCircleOrbit</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_OffensiveCombatHitAndRun.html">MyObjectBuilder_OffensiveCombatHitAndRun</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_OffensiveCombatIntercept.html">MyObjectBuilder_OffensiveCombatIntercept</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_OffensiveCombatStayAtRange.html">MyObjectBuilder_OffensiveCombatStayAtRange</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ParticleEntityComponent.html">MyObjectBuilder_ParticleEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_PathRecorderComponent.html">MyObjectBuilder_PathRecorderComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_RadiationSourceEntityComponent.html">MyObjectBuilder_RadiationSourceEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_RandomCargoEntityComponent.html">MyObjectBuilder_RandomCargoEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_RandomMovementSubpartComponent.html">MyObjectBuilder_RandomMovementSubpartComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ResourceStorageComponent.html">MyObjectBuilder_ResourceStorageComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_RotatingSubpartComponent.html">MyObjectBuilder_RotatingSubpartComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SearchEnemyComponent.html">MyObjectBuilder_SearchEnemyComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SignalChannelEntityComponent.html">MyObjectBuilder_SignalChannelEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SignalConnectivityCheckerEntityComponent.html">MyObjectBuilder_SignalConnectivityCheckerEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SignalReceiverEntityComponent.html">MyObjectBuilder_SignalReceiverEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SignalSenderEntityComponent.html">MyObjectBuilder_SignalSenderEntityComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SolarFoodGenerator.html">MyObjectBuilder_SolarFoodGenerator</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SolarGameLogicComponent.html">MyObjectBuilder_SolarGameLogicComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SolarOccludableComponent.html">MyObjectBuilder_SolarOccludableComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SubModelComponent.html">MyObjectBuilder_SubModelComponent</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SurvivalBuffBase.html">MyObjectBuilder_SurvivalBuffBase</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SurvivalBuffsProgression.html">MyObjectBuilder_SurvivalBuffsProgression</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Game.EntityComponents.MyObjectBuilder_MaintenancePanelAggregate.html">MyObjectBuilder_MaintenancePanelAggregate</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_ModelComponent.html">MyObjectBuilder_ModelComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AssetModifierComponent.html">MyObjectBuilder_AssetModifierComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_AtmosphereDetectorComponent.html">MyObjectBuilder_AtmosphereDetectorComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_CharacterPickupComponent.html">MyObjectBuilder_CharacterPickupComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_CharacterSoundComponent.html">MyObjectBuilder_CharacterSoundComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ContainerDropComponent.html">MyObjectBuilder_ContainerDropComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityCapacitorComponent.html">MyObjectBuilder_EntityCapacitorComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityDurabilityComponent.html">MyObjectBuilder_EntityDurabilityComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityReverbDetectorComponent.html">MyObjectBuilder_EntityReverbDetectorComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityStatComponent.html">MyObjectBuilder_EntityStatComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_EntityStorageComponent.html">MyObjectBuilder_EntityStorageComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventoryBase.html">MyObjectBuilder_InventoryBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_InventorySpawnComponent.html">MyObjectBuilder_InventorySpawnComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ModCustomComponent.html">MyObjectBuilder_ModCustomComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ModStorageComponent.html">MyObjectBuilder_ModStorageComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_PhysicsComponentBase.html">MyObjectBuilder_PhysicsComponentBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ResourceSinkComponent.html">MyObjectBuilder_ResourceSinkComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ResourceSourceComponent.html">MyObjectBuilder_ResourceSourceComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_RespawnComponent.html">MyObjectBuilder_RespawnComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ShipSoundComponent.html">MyObjectBuilder_ShipSoundComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_SunTrackingComponent.html">MyObjectBuilder_SunTrackingComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetFocusComponent.html">MyObjectBuilder_TargetFocusComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingBlockComponent.html">MyObjectBuilder_TargetLockingBlockComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TargetLockingComponent.html">MyObjectBuilder_TargetLockingComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TimerComponent.html">MyObjectBuilder_TimerComponent</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TriggerAggregate.html">MyObjectBuilder_TriggerAggregate</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_TriggerBase.html">MyObjectBuilder_TriggerBase</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_UseObjectsComponent.html">MyObjectBuilder_UseObjectsComponent</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.html">VRage.Game.ObjectBuilders.ComponentSystem</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public abstract class MyObjectBuilder_ComponentBase : MyObjectBuilder_Base</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase__ctor_" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.#ctor*"></a>
  <h4 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase__ctor" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.#ctor">MyObjectBuilder_ComponentBase()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected MyObjectBuilder_ComponentBase()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_RemoveExistingComponentOnNewInsert" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.RemoveExistingComponentOnNewInsert">RemoveExistingComponentOnNewInsert</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RemoveExistingComponentOnNewInsert</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_Remap_" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.Remap*"></a>
  <h4 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_Remap_VRage_ModAPI_IMyRemapHelper_" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.Remap(VRage.ModAPI.IMyRemapHelper)">Remap(IMyRemapHelper)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Remap(IMyRemapHelper remapHelper)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyRemapHelper.html">IMyRemapHelper</a></td>
        <td><span class="parametername">remapHelper</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_SetupForProjector_" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.SetupForProjector*"></a>
  <h4 id="VRage_Game_ObjectBuilders_ComponentSystem_MyObjectBuilder_ComponentBase_SetupForProjector" data-uid="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.SetupForProjector">SetupForProjector()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void SetupForProjector()</code></pre>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
