﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyStorage
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyStorage
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyStorage">
  
  
  <h1 id="VRage_ModAPI_IMyStorage" data-uid="VRage.ModAPI.IMyStorage" class="text-break">Interface IMyStorage
  </h1>
  <div class="markdown level0 summary"><p>ModAPI interface giving access to voxel functions</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyStorage_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyStorage</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ModAPI_IMyStorage_Closed_" data-uid="VRage.ModAPI.IMyStorage.Closed*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Closed" data-uid="VRage.ModAPI.IMyStorage.Closed">Closed</h4>
  <div class="markdown level1 summary"><p>Returns true if voxel storage was closed</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Closed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_DeleteSupported_" data-uid="VRage.ModAPI.IMyStorage.DeleteSupported*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_DeleteSupported" data-uid="VRage.ModAPI.IMyStorage.DeleteSupported">DeleteSupported</h4>
  <div class="markdown level1 summary"><p>Returns if deletion is supported</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool DeleteSupported { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_MarkedForClose_" data-uid="VRage.ModAPI.IMyStorage.MarkedForClose*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_MarkedForClose" data-uid="VRage.ModAPI.IMyStorage.MarkedForClose">MarkedForClose</h4>
  <div class="markdown level1 summary"><p>Returns true if the voxel storage is marked for a pending close</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool MarkedForClose { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_Size_" data-uid="VRage.ModAPI.IMyStorage.Size*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Size" data-uid="VRage.ModAPI.IMyStorage.Size">Size</h4>
  <div class="markdown level1 summary"><p>The size of the voxel storage, in voxel coordinates</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3I Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyStorage_DeleteRange_" data-uid="VRage.ModAPI.IMyStorage.DeleteRange*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_DeleteRange_VRage_Voxels_MyStorageDataTypeFlags_VRageMath_Vector3I_VRageMath_Vector3I_System_Boolean_" data-uid="VRage.ModAPI.IMyStorage.DeleteRange(VRage.Voxels.MyStorageDataTypeFlags,VRageMath.Vector3I,VRageMath.Vector3I,System.Boolean)">DeleteRange(MyStorageDataTypeFlags, Vector3I, Vector3I, Boolean)</h4>
  <div class="markdown level1 summary"><p>Deletes content in cache and storage</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void DeleteRange(MyStorageDataTypeFlags dataToWrite, Vector3I voxelRangeMin, Vector3I voxelRangeMax, bool notify)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToWrite</span></td>
        <td><p>Content to delete</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMin</span></td>
        <td><p>From (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMax</span></td>
        <td><p>To (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">notify</span></td>
        <td><p>Notify that range changed</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_ExecuteOperationFast_" data-uid="VRage.ModAPI.IMyStorage.ExecuteOperationFast*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_ExecuteOperationFast__1___0__VRage_Voxels_MyStorageDataTypeFlags_VRageMath_Vector3I__VRageMath_Vector3I__System_Boolean_" data-uid="VRage.ModAPI.IMyStorage.ExecuteOperationFast``1(``0@,VRage.Voxels.MyStorageDataTypeFlags,VRageMath.Vector3I@,VRageMath.Vector3I@,System.Boolean)">ExecuteOperationFast&lt;TVoxelOperator&gt;(ref TVoxelOperator, MyStorageDataTypeFlags, ref Vector3I, ref Vector3I, Boolean)</h4>
  <div class="markdown level1 summary"><p>Performs in-place voxel operation</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ExecuteOperationFast&lt;TVoxelOperator&gt;(ref TVoxelOperator voxelOperator, MyStorageDataTypeFlags dataToWrite, ref Vector3I voxelRangeMin, ref Vector3I voxelRangeMax, bool notifyRangeChanged)
    where TVoxelOperator : struct, IVoxelOperator, ValueType</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">TVoxelOperator</span></td>
        <td><span class="parametername">voxelOperator</span></td>
        <td><p>Function to be called over voxels</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToWrite</span></td>
        <td><p>Content to operate with</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMin</span></td>
        <td><p>From (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMax</span></td>
        <td><p>To (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">notifyRangeChanged</span></td>
        <td><p>Notify that range changed</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">TVoxelOperator</span></td>
        <td><p>Function to be called over voxels</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_Intersect_" data-uid="VRage.ModAPI.IMyStorage.Intersect*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Intersect_VRageMath_BoundingBox__System_Boolean_" data-uid="VRage.ModAPI.IMyStorage.Intersect(VRageMath.BoundingBox@,System.Boolean)">Intersect(ref BoundingBox, Boolean)</h4>
  <div class="markdown level1 summary"><p>Returns the intersection with the storage region</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">ContainmentType Intersect(ref BoundingBox box, bool lazy)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>Position in local coordinates</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">lazy</span></td>
        <td><p>When true, you get less detailed results</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><p>Containment type</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_Intersect_" data-uid="VRage.ModAPI.IMyStorage.Intersect*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Intersect_VRageMath_LineD__" data-uid="VRage.ModAPI.IMyStorage.Intersect(VRageMath.LineD@)">Intersect(ref LineD)</h4>
  <div class="markdown level1 summary"><p>Returns true if the specific line intersects the storage region</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Intersect(ref LineD line)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td><p>Line in local coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if line intersects</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_NotifyRangeChanged_" data-uid="VRage.ModAPI.IMyStorage.NotifyRangeChanged*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_NotifyRangeChanged_VRageMath_Vector3I__VRageMath_Vector3I__VRage_Voxels_MyStorageDataTypeFlags_" data-uid="VRage.ModAPI.IMyStorage.NotifyRangeChanged(VRageMath.Vector3I@,VRageMath.Vector3I@,VRage.Voxels.MyStorageDataTypeFlags)">NotifyRangeChanged(ref Vector3I, ref Vector3I, MyStorageDataTypeFlags)</h4>
  <div class="markdown level1 summary"><p>Notify that range changed</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void NotifyRangeChanged(ref Vector3I voxelRangeMin, ref Vector3I voxelRangeMax, MyStorageDataTypeFlags dataChanged)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMin</span></td>
        <td><p>From (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMax</span></td>
        <td><p>To (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataChanged</span></td>
        <td><p>Content that changed</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_OverwriteAllMaterials_" data-uid="VRage.ModAPI.IMyStorage.OverwriteAllMaterials*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_OverwriteAllMaterials_System_Byte_" data-uid="VRage.ModAPI.IMyStorage.OverwriteAllMaterials(System.Byte)">OverwriteAllMaterials(Byte)</h4>
  <div class="markdown level1 summary"><p>Replaces all materials in range with the specific material</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void OverwriteAllMaterials(byte materialIndex)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td><span class="parametername">materialIndex</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_PinAndExecute_" data-uid="VRage.ModAPI.IMyStorage.PinAndExecute*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_PinAndExecute_System_Action_" data-uid="VRage.ModAPI.IMyStorage.PinAndExecute(System.Action)">PinAndExecute(Action)</h4>
  <div class="markdown level1 summary"><p>Pins the voxel storage to prevent closing, then executes specified action. Unpins when action completes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void PinAndExecute(Action action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span></td>
        <td><span class="parametername">action</span></td>
        <td><p>Action to execute</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_PinAndExecute_" data-uid="VRage.ModAPI.IMyStorage.PinAndExecute*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_PinAndExecute_System_Action_VRage_ModAPI_IMyStorage__" data-uid="VRage.ModAPI.IMyStorage.PinAndExecute(System.Action{VRage.ModAPI.IMyStorage})">PinAndExecute(Action&lt;IMyStorage&gt;)</h4>
  <div class="markdown level1 summary"><p>Pins the voxel storage to prevent closing, then executes specified action. Unpins when action completes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void PinAndExecute(Action&lt;IMyStorage&gt; action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyStorage.html">IMyStorage</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p>Action to execute</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_ReadRange_" data-uid="VRage.ModAPI.IMyStorage.ReadRange*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_ReadRange_VRage_Voxels_MyStorageData_VRage_Voxels_MyStorageDataTypeFlags_System_Int32_VRageMath_Vector3I_VRageMath_Vector3I_" data-uid="VRage.ModAPI.IMyStorage.ReadRange(VRage.Voxels.MyStorageData,VRage.Voxels.MyStorageDataTypeFlags,System.Int32,VRageMath.Vector3I,VRageMath.Vector3I)">ReadRange(MyStorageData, MyStorageDataTypeFlags, Int32, Vector3I, Vector3I)</h4>
  <div class="markdown level1 summary"><p>Reads range of content and/or materials from specified LOD. If you want to write data back later, you must read LOD0 as that is the only writable one.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReadRange(MyStorageData target, MyStorageDataTypeFlags dataToRead, int lodIndex, Vector3I lodVoxelRangeMin, Vector3I lodVoxelRangeMax)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">target</span></td>
        <td><p>Source from where to read data</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToRead</span></td>
        <td><p>Content to read</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lodIndex</span></td>
        <td><p>Level of detail. 0 - most detailed</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">lodVoxelRangeMin</span></td>
        <td><p>From (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">lodVoxelRangeMax</span></td>
        <td><p>To (Inclusive)</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_ReadRange_" data-uid="VRage.ModAPI.IMyStorage.ReadRange*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_ReadRange_VRage_Voxels_MyStorageData_VRage_Voxels_MyStorageDataTypeFlags_System_Int32_VRageMath_Vector3I_VRageMath_Vector3I_VRage_Voxels_MyVoxelRequestFlags__" data-uid="VRage.ModAPI.IMyStorage.ReadRange(VRage.Voxels.MyStorageData,VRage.Voxels.MyStorageDataTypeFlags,System.Int32,VRageMath.Vector3I,VRageMath.Vector3I,VRage.Voxels.MyVoxelRequestFlags@)">ReadRange(MyStorageData, MyStorageDataTypeFlags, Int32, Vector3I, Vector3I, ref MyVoxelRequestFlags)</h4>
  <div class="markdown level1 summary"><p>Reads range of content and/or materials from specified LOD. If you want to write data back later, you must read LOD0 as that is the only writable one.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReadRange(MyStorageData target, MyStorageDataTypeFlags dataToRead, int lodIndex, Vector3I lodVoxelRangeMin, Vector3I lodVoxelRangeMax, ref MyVoxelRequestFlags requestFlags)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">target</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToRead</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lodIndex</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">lodVoxelRangeMin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">lodVoxelRangeMax</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyVoxelRequestFlags.html">MyVoxelRequestFlags</a></td>
        <td><span class="parametername">requestFlags</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_Reset_" data-uid="VRage.ModAPI.IMyStorage.Reset*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Reset_VRage_Voxels_MyStorageDataTypeFlags_" data-uid="VRage.ModAPI.IMyStorage.Reset(VRage.Voxels.MyStorageDataTypeFlags)">Reset(MyStorageDataTypeFlags)</h4>
  <div class="markdown level1 summary"><p>Resets the data specified by flags to values from data provider, or default if no provider is assigned.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Reset(MyStorageDataTypeFlags dataToReset)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToReset</span></td>
        <td><p>Content that should be read from disk</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_Save_" data-uid="VRage.ModAPI.IMyStorage.Save*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_Save_System_Byte____" data-uid="VRage.ModAPI.IMyStorage.Save(System.Byte[]@)">Save(out Byte[])</h4>
  <div class="markdown level1 summary"><p>Gets compressed voxel data</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Save(out byte[] outCompressedData)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td><span class="parametername">outCompressedData</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyStorage_WriteRange_" data-uid="VRage.ModAPI.IMyStorage.WriteRange*"></a>
  <h4 id="VRage_ModAPI_IMyStorage_WriteRange_VRage_Voxels_MyStorageData_VRage_Voxels_MyStorageDataTypeFlags_VRageMath_Vector3I_VRageMath_Vector3I_System_Boolean_System_Boolean_" data-uid="VRage.ModAPI.IMyStorage.WriteRange(VRage.Voxels.MyStorageData,VRage.Voxels.MyStorageDataTypeFlags,VRageMath.Vector3I,VRageMath.Vector3I,System.Boolean,System.Boolean)">WriteRange(MyStorageData, MyStorageDataTypeFlags, Vector3I, Vector3I, Boolean, Boolean)</h4>
  <div class="markdown level1 summary"><p>Writes range of content and/or materials from cache to storage. Note that this can only write to LOD0 (higher LODs must be computed based on that).</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void WriteRange(MyStorageData source, MyStorageDataTypeFlags dataToWrite, Vector3I voxelRangeMin, Vector3I voxelRangeMax, bool notify = true, bool skipCache = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">source</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataToWrite</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMin</span></td>
        <td><p>From (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">voxelRangeMax</span></td>
        <td><p>To (Inclusive)</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">notify</span></td>
        <td><p>Notify that range changed</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">skipCache</span></td>
        <td><p>Skips cache</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
