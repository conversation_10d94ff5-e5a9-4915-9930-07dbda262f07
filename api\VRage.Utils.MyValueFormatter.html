﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyValueFormatter
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyValueFormatter
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils.MyValueFormatter">
  
  
  <h1 id="VRage_Utils_MyValueFormatter" data-uid="VRage.Utils.MyValueFormatter" class="text-break">Class MyValueFormatter
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyValueFormatter</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Utils.html">VRage.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.dll</h6>
  <h5 id="VRage_Utils_MyValueFormatter_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyValueFormatter : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Utils_MyValueFormatter__ctor_" data-uid="VRage.Utils.MyValueFormatter.#ctor*"></a>
  <h4 id="VRage_Utils_MyValueFormatter__ctor" data-uid="VRage.Utils.MyValueFormatter.#ctor">MyValueFormatter()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyValueFormatter()</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendDistanceInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendDistanceInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendDistanceInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendDistanceInBestUnit(System.Single,System.Text.StringBuilder)">AppendDistanceInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendDistanceInBestUnit(float distanceInMeters, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">distanceInMeters</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendForceInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendForceInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendForceInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendForceInBestUnit(System.Single,System.Text.StringBuilder)">AppendForceInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendForceInBestUnit(float forceInNewtons, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">forceInNewtons</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendFormattedValueInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendFormattedValueInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendFormattedValueInBestUnit_System_Single_System_String___System_Single___System_Int32_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendFormattedValueInBestUnit(System.Single,System.String[],System.Single[],System.Int32,System.Text.StringBuilder)">AppendFormattedValueInBestUnit(Single, String[], Single[], Int32, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendFormattedValueInBestUnit(float value, string[] unitNames, float[] unitMultipliers, int unitDecimalDigits, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span>[]</td>
        <td><span class="parametername">unitNames</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span>[]</td>
        <td><span class="parametername">unitMultipliers</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">unitDecimalDigits</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendFormattedValueInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendFormattedValueInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendFormattedValueInBestUnit_System_Single_System_String___System_Single___System_Int32___System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendFormattedValueInBestUnit(System.Single,System.String[],System.Single[],System.Int32[],System.Text.StringBuilder)">AppendFormattedValueInBestUnit(Single, String[], Single[], Int32[], StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendFormattedValueInBestUnit(float value, string[] unitNames, float[] unitMultipliers, int[] unitDecimalDigits, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span>[]</td>
        <td><span class="parametername">unitNames</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span>[]</td>
        <td><span class="parametername">unitMultipliers</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span>[]</td>
        <td><span class="parametername">unitDecimalDigits</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendGenericInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendGenericInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendGenericInBestUnit_System_Single_System_Int32_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendGenericInBestUnit(System.Single,System.Int32,System.Text.StringBuilder)">AppendGenericInBestUnit(Single, Int32, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendGenericInBestUnit(float genericInBase, int numDecimals, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">genericInBase</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">numDecimals</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendGenericInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendGenericInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendGenericInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendGenericInBestUnit(System.Single,System.Text.StringBuilder)">AppendGenericInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendGenericInBestUnit(float genericInBase, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">genericInBase</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendTimeExact_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExact*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendTimeExact_System_Int32_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExact(System.Int32,System.Text.StringBuilder)">AppendTimeExact(Int32, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendTimeExact(int timeInSeconds, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">timeInSeconds</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendTimeExactHoursMinSec_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExactHoursMinSec*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendTimeExactHoursMinSec_System_Int32_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExactHoursMinSec(System.Int32,System.Text.StringBuilder)">AppendTimeExactHoursMinSec(Int32, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendTimeExactHoursMinSec(int timeInSeconds, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">timeInSeconds</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendTimeExactMinSec_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExactMinSec*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendTimeExactMinSec_System_Int32_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeExactMinSec(System.Int32,System.Text.StringBuilder)">AppendTimeExactMinSec(Int32, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendTimeExactMinSec(int timeInSeconds, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">timeInSeconds</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendTimeInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendTimeInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendTimeInBestUnit(System.Single,System.Text.StringBuilder)">AppendTimeInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendTimeInBestUnit(float timeInSeconds, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">timeInSeconds</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendTorqueInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendTorqueInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendTorqueInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendTorqueInBestUnit(System.Single,System.Text.StringBuilder)">AppendTorqueInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendTorqueInBestUnit(float torqueInNewtonMeters, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">torqueInNewtonMeters</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendVolumeInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendVolumeInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendVolumeInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendVolumeInBestUnit(System.Single,System.Text.StringBuilder)">AppendVolumeInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendVolumeInBestUnit(float volumeInCubicMeters, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">volumeInCubicMeters</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendWeightInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendWeightInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendWeightInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendWeightInBestUnit(System.Single,System.Text.StringBuilder)">AppendWeightInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendWeightInBestUnit(float weightInKG, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">weightInKG</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendWorkHoursInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendWorkHoursInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendWorkHoursInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendWorkHoursInBestUnit(System.Single,System.Text.StringBuilder)">AppendWorkHoursInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendWorkHoursInBestUnit(float workInMegaWatts, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">workInMegaWatts</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_AppendWorkInBestUnit_" data-uid="VRage.Utils.MyValueFormatter.AppendWorkInBestUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_AppendWorkInBestUnit_System_Single_System_Text_StringBuilder_" data-uid="VRage.Utils.MyValueFormatter.AppendWorkInBestUnit(System.Single,System.Text.StringBuilder)">AppendWorkInBestUnit(Single, StringBuilder)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendWorkInBestUnit(float workInMegaWatts, StringBuilder output)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">workInMegaWatts</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Text.StringBuilder</span></td>
        <td><span class="parametername">output</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetDecimalFromString_" data-uid="VRage.Utils.MyValueFormatter.GetDecimalFromString*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetDecimalFromString_System_String_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetDecimalFromString(System.String,System.Int32)">GetDecimalFromString(String, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Decimal GetDecimalFromString(string number, int decimalDigits)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">number</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFloatFromString_" data-uid="VRage.Utils.MyValueFormatter.GetFloatFromString*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFloatFromString_System_String_System_Int32_System_String_" data-uid="VRage.Utils.MyValueFormatter.GetFloatFromString(System.String,System.Int32,System.String)">GetFloatFromString(String, Int32, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Nullable&lt;float&gt; GetFloatFromString(string number, int decimalDigits, string groupSeparator)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">number</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">groupSeparator</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedArray_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedArray*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedArray__1___0___" data-uid="VRage.Utils.MyValueFormatter.GetFormatedArray``1(``0[])">GetFormatedArray&lt;T&gt;(T[])</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedArray&lt;T&gt;(T[] array)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>T[]</td>
        <td><span class="parametername">array</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDateTime_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTime*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDateTime_System_DateTime_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTime(System.DateTime)">GetFormatedDateTime(DateTime)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDateTime(DateTime dt)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.DateTime</span></td>
        <td><span class="parametername">dt</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeForFilename_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeForFilename*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeForFilename_System_DateTime_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeForFilename(System.DateTime)">GetFormatedDateTimeForFilename(DateTime)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDateTimeForFilename(DateTime dt)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.DateTime</span></td>
        <td><span class="parametername">dt</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeOffset_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeOffset*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeOffset_System_DateTimeOffset_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeOffset(System.DateTimeOffset)">GetFormatedDateTimeOffset(DateTimeOffset)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDateTimeOffset(DateTimeOffset dt)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.DateTimeOffset</span></td>
        <td><span class="parametername">dt</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeWithoutMilliseconds_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeWithoutMilliseconds*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDateTimeWithoutMilliseconds_System_DateTime_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDateTimeWithoutMilliseconds(System.DateTime)">GetFormatedDateTimeWithoutMilliseconds(DateTime)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDateTimeWithoutMilliseconds(DateTime dt)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.DateTime</span></td>
        <td><span class="parametername">dt</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDecimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDecimal*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDecimal_System_Decimal_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDecimal(System.Decimal,System.Int32)">GetFormatedDecimal(Decimal, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDecimal(Decimal num, int decimalDigits)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedDouble_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDouble*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedDouble_System_Double_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedDouble(System.Double,System.Int32)">GetFormatedDouble(Double, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedDouble(double num, int decimalDigits)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedFloat_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedFloat*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedFloat_System_Single_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedFloat(System.Single,System.Int32)">GetFormatedFloat(Single, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedFloat(float num, int decimalDigits)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedFloat_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedFloat*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedFloat_System_Single_System_Int32_System_String_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedFloat(System.Single,System.Int32,System.String)">GetFormatedFloat(Single, Int32, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedFloat(float num, int decimalDigits, string groupSeparator)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">decimalDigits</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">groupSeparator</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedGameMoney_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedGameMoney*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedGameMoney_System_Decimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedGameMoney(System.Decimal)">GetFormatedGameMoney(Decimal)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedGameMoney(Decimal num)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedInt_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedInt*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedInt_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedInt(System.Int32)">GetFormatedInt(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedInt(int i)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">i</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedLong_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedLong*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedLong_System_Int64_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedLong(System.Int64)">GetFormatedLong(Int64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedLong(long l)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">l</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedPriceEUR_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceEUR*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedPriceEUR_System_Decimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceEUR(System.Decimal)">GetFormatedPriceEUR(Decimal)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedPriceEUR(Decimal num)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedPriceUSD_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceUSD*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedPriceUSD_System_Decimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceUSD(System.Decimal)">GetFormatedPriceUSD(Decimal)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedPriceUSD(Decimal num)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedPriceUSD_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceUSD*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedPriceUSD_System_Decimal_System_Decimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedPriceUSD(System.Decimal,System.Decimal)">GetFormatedPriceUSD(Decimal, Decimal)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedPriceUSD(Decimal priceInEur, Decimal exchangeRateEurToUsd)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">priceInEur</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">exchangeRateEurToUsd</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormatedQP_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedQP*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormatedQP_System_Decimal_" data-uid="VRage.Utils.MyValueFormatter.GetFormatedQP(System.Decimal)">GetFormatedQP(Decimal)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormatedQP(Decimal num)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Decimal</span></td>
        <td><span class="parametername">num</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormattedFileSizeInMB_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedFileSizeInMB*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormattedFileSizeInMB_System_UInt64_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedFileSizeInMB(System.UInt64)">GetFormattedFileSizeInMB(UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormattedFileSizeInMB(ulong bytes)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">bytes</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormattedFileSizeInMBWithoutUnit_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedFileSizeInMBWithoutUnit*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormattedFileSizeInMBWithoutUnit_System_UInt64_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedFileSizeInMBWithoutUnit(System.UInt64)">GetFormattedFileSizeInMBWithoutUnit(UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormattedFileSizeInMBWithoutUnit(ulong bytes)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">bytes</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormattedGasAmount_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedGasAmount*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormattedGasAmount_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedGasAmount(System.Int32)">GetFormattedGasAmount(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormattedGasAmount(int amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormattedOreAmount_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedOreAmount*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormattedOreAmount_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedOreAmount(System.Int32)">GetFormattedOreAmount(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormattedOreAmount(int amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyValueFormatter_GetFormattedPiecesAmount_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedPiecesAmount*"></a>
  <h4 id="VRage_Utils_MyValueFormatter_GetFormattedPiecesAmount_System_Int32_" data-uid="VRage.Utils.MyValueFormatter.GetFormattedPiecesAmount(System.Int32)">GetFormattedPiecesAmount(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string GetFormattedPiecesAmount(int amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">amount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
