﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingBoxI
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingBoxI
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingBoxI">
  
  
  <h1 id="VRageMath_BoundingBoxI" data-uid="VRageMath.BoundingBoxI" class="text-break">Class BoundingBoxI
  </h1>
  <div class="markdown level0 summary"><p>Defines an axis-aligned box-shaped 3D volume.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingBoxI</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingBoxI_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class BoundingBoxI : ValueType, IEquatable&lt;BoundingBoxI&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingBoxI__ctor_" data-uid="VRageMath.BoundingBoxI.#ctor*"></a>
  <h4 id="VRageMath_BoundingBoxI__ctor_System_Int32_System_Int32_" data-uid="VRageMath.BoundingBoxI.#ctor(System.Int32,System.Int32)">BoundingBoxI(Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI(int min, int max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum point the BoundingBoxI includes.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum point the BoundingBoxI includes.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI__ctor_" data-uid="VRageMath.BoundingBoxI.#ctor*"></a>
  <h4 id="VRageMath_BoundingBoxI__ctor_VRageMath_BoundingBox_" data-uid="VRageMath.BoundingBoxI.#ctor(VRageMath.BoundingBox)">BoundingBoxI(BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI__ctor_" data-uid="VRageMath.BoundingBoxI.#ctor*"></a>
  <h4 id="VRageMath_BoundingBoxI__ctor_VRageMath_Vector3I_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.#ctor(VRageMath.Vector3I,VRageMath.Vector3I)">BoundingBoxI(Vector3I, Vector3I)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI(Vector3I min, Vector3I max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum point the BoundingBoxI includes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum point the BoundingBoxI includes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingBoxI_Max" data-uid="VRageMath.BoundingBoxI.Max">Max</h4>
  <div class="markdown level1 summary"><p>The maximum point the BoundingBoxI contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Max</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBoxI_Min" data-uid="VRageMath.BoundingBoxI.Min">Min</h4>
  <div class="markdown level1 summary"><p>The minimum point the BoundingBoxI contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Min</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingBoxI_Center_" data-uid="VRageMath.BoundingBoxI.Center*"></a>
  <h4 id="VRageMath_BoundingBoxI_Center" data-uid="VRageMath.BoundingBoxI.Center">Center</h4>
  <div class="markdown level1 summary"><p>Calculates center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Center { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_HalfExtents_" data-uid="VRageMath.BoundingBoxI.HalfExtents*"></a>
  <h4 id="VRageMath_BoundingBoxI_HalfExtents" data-uid="VRageMath.BoundingBoxI.HalfExtents">HalfExtents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I HalfExtents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_IsValid_" data-uid="VRageMath.BoundingBoxI.IsValid*"></a>
  <h4 id="VRageMath_BoundingBoxI_IsValid" data-uid="VRageMath.BoundingBoxI.IsValid">IsValid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Perimeter_" data-uid="VRageMath.BoundingBoxI.Perimeter*"></a>
  <h4 id="VRageMath_BoundingBoxI_Perimeter" data-uid="VRageMath.BoundingBoxI.Perimeter">Perimeter</h4>
  <div class="markdown level1 summary"><p>return perimeter of edges</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Perimeter { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Size_" data-uid="VRageMath.BoundingBoxI.Size*"></a>
  <h4 id="VRageMath_BoundingBoxI_Size" data-uid="VRageMath.BoundingBoxI.Size">Size</h4>
  <div class="markdown level1 summary"><p>Size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingBoxI_Contains_" data-uid="VRageMath.BoundingBoxI.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxI_Contains_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.Contains(VRageMath.BoundingBoxI)">Contains(BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBoxI contains another BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxI to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Contains_" data-uid="VRageMath.BoundingBoxI.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxI_Contains_VRageMath_BoundingBoxI__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBoxI.Contains(VRageMath.BoundingBoxI@,VRageMath.ContainmentType@)">Contains(ref BoundingBoxI, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBoxI contains a BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBoxI box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxI to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Contains_" data-uid="VRageMath.BoundingBoxI.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxI_Contains_VRageMath_Vector3_" data-uid="VRageMath.BoundingBoxI.Contains(VRageMath.Vector3)">Contains(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3 point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Contains_" data-uid="VRageMath.BoundingBoxI.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxI_Contains_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.Contains(VRageMath.Vector3I)">Contains(Vector3I)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBoxI contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Contains_" data-uid="VRageMath.BoundingBoxI.Contains*"></a>
  <h4 id="VRageMath_BoundingBoxI_Contains_VRageMath_Vector3I__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBoxI.Contains(VRageMath.Vector3I@,VRageMath.ContainmentType@)">Contains(ref Vector3I, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBoxI contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector3I point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateFromPoints_" data-uid="VRageMath.BoundingBoxI.CreateFromPoints*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateFromPoints_System_Collections_Generic_IEnumerable_VRageMath_Vector3I__" data-uid="VRageMath.BoundingBoxI.CreateFromPoints(System.Collections.Generic.IEnumerable{VRageMath.Vector3I})">CreateFromPoints(IEnumerable&lt;Vector3I&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBoxI that will contain a group of points.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxI CreateFromPoints(IEnumerable&lt;Vector3I&gt; points)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>&gt;</td>
        <td><span class="parametername">points</span></td>
        <td><p>A list of points the BoundingBoxI should contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateFromSphere_" data-uid="VRageMath.BoundingBoxI.CreateFromSphere*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateFromSphere_VRageMath_BoundingSphere_" data-uid="VRageMath.BoundingBoxI.CreateFromSphere(VRageMath.BoundingSphere)">CreateFromSphere(BoundingSphere)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBoxI that will contain the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxI CreateFromSphere(BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateFromSphere_" data-uid="VRageMath.BoundingBoxI.CreateFromSphere*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateFromSphere_VRageMath_BoundingSphere__VRageMath_BoundingBoxI__" data-uid="VRageMath.BoundingBoxI.CreateFromSphere(VRageMath.BoundingSphere@,VRageMath.BoundingBoxI@)">CreateFromSphere(ref BoundingSphere, out BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBoxI that will contain the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromSphere(ref BoundingSphere sphere, out BoundingBoxI result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBoxI.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateInvalid_" data-uid="VRageMath.BoundingBoxI.CreateInvalid*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateInvalid" data-uid="VRageMath.BoundingBoxI.CreateInvalid">CreateInvalid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxI CreateInvalid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateMerged_" data-uid="VRageMath.BoundingBoxI.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateMerged_VRageMath_BoundingBoxI_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.CreateMerged(VRageMath.BoundingBoxI,VRageMath.BoundingBoxI)">CreateMerged(BoundingBoxI, BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBoxI that contains the two specified BoundingBoxI instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBoxI CreateMerged(BoundingBoxI original, BoundingBoxI additional)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBoxIs to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBoxIs to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_CreateMerged_" data-uid="VRageMath.BoundingBoxI.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBoxI_CreateMerged_VRageMath_BoundingBoxI__VRageMath_BoundingBoxI__VRageMath_BoundingBoxI__" data-uid="VRageMath.BoundingBoxI.CreateMerged(VRageMath.BoundingBoxI@,VRageMath.BoundingBoxI@,VRageMath.BoundingBoxI@)">CreateMerged(ref BoundingBoxI, ref BoundingBoxI, out BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBoxI that contains the two specified BoundingBoxI instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateMerged(ref BoundingBoxI original, ref BoundingBoxI additional, out BoundingBoxI result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBoxI instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBoxI instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBoxI.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Distance_" data-uid="VRageMath.BoundingBoxI.Distance*"></a>
  <h4 id="VRageMath_BoundingBoxI_Distance_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.Distance(VRageMath.Vector3I)">Distance(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Distance(Vector3I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_EnumeratePoints_" data-uid="VRageMath.BoundingBoxI.EnumeratePoints*"></a>
  <h4 id="VRageMath_BoundingBoxI_EnumeratePoints_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.EnumeratePoints(VRageMath.BoundingBoxI)">EnumeratePoints(BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Enumerate all values in a integer interval (a cuboid).</p>
<p>This method is an allocating version of the Vector3I_RangeIterator.
This once can be used in the foreach syntax though so it's more convenient for debug routines.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IEnumerable&lt;Vector3I&gt; EnumeratePoints(BoundingBoxI rangeInclusive)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">rangeInclusive</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>&gt;</td>
        <td><p>An iterator for that range.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Equals_" data-uid="VRageMath.BoundingBoxI.Equals*"></a>
  <h4 id="VRageMath_BoundingBoxI_Equals_System_Object_" data-uid="VRageMath.BoundingBoxI.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBoxI are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingBoxI.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Equals_" data-uid="VRageMath.BoundingBoxI.Equals*"></a>
  <h4 id="VRageMath_BoundingBoxI_Equals_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.Equals(VRageMath.BoundingBoxI)">Equals(BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBoxI are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingBoxI other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingBoxI to compare with the current BoundingBoxI.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_GetCorners_" data-uid="VRageMath.BoundingBoxI.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBoxI_GetCorners" data-uid="VRageMath.BoundingBoxI.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_GetCorners_" data-uid="VRageMath.BoundingBoxI.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBoxI_GetCorners_VRageMath_Vector3I___" data-uid="VRageMath.BoundingBoxI.GetCorners(VRageMath.Vector3I[])">GetCorners(Vector3I[])</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector3I[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3I points where the corners of the BoundingBoxI are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_GetCornersUnsafe_" data-uid="VRageMath.BoundingBoxI.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingBoxI_GetCornersUnsafe_VRageMath_Vector3I__" data-uid="VRageMath.BoundingBoxI.GetCornersUnsafe(VRageMath.Vector3I*)">GetCornersUnsafe(Vector3I*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector3I*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3I points where the corners of the BoundingBoxI are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_GetHashCode_" data-uid="VRageMath.BoundingBoxI.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingBoxI_GetHashCode" data-uid="VRageMath.BoundingBoxI.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_GetIncluded_" data-uid="VRageMath.BoundingBoxI.GetIncluded*"></a>
  <h4 id="VRageMath_BoundingBoxI_GetIncluded_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.GetIncluded(VRageMath.Vector3I)">GetIncluded(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI GetIncluded(Vector3I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.BoundingBoxI)">Include(BoundingBoxI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_BoundingBoxI__" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.BoundingBoxI@)">Include(ref BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(ref BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.Vector3I)">Include(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(Vector3I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_Vector3I_VRageMath_Vector3I_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.Vector3I,VRageMath.Vector3I,VRageMath.Vector3I)">Include(Vector3I, Vector3I, Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(Vector3I p0, Vector3I p1, Vector3I p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_Vector3I__" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.Vector3I@)">Include(ref Vector3I)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(ref Vector3I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Include_" data-uid="VRageMath.BoundingBoxI.Include*"></a>
  <h4 id="VRageMath_BoundingBoxI_Include_VRageMath_Vector3I__VRageMath_Vector3I__VRageMath_Vector3I__" data-uid="VRageMath.BoundingBoxI.Include(VRageMath.Vector3I@,VRageMath.Vector3I@,VRageMath.Vector3I@)">Include(ref Vector3I, ref Vector3I, ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Include(ref Vector3I p0, ref Vector3I p1, ref Vector3I p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Inflate_" data-uid="VRageMath.BoundingBoxI.Inflate*"></a>
  <h4 id="VRageMath_BoundingBoxI_Inflate_System_Int32_" data-uid="VRageMath.BoundingBoxI.Inflate(System.Int32)">Inflate(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Inflate(int size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_InflateToMinimum_" data-uid="VRageMath.BoundingBoxI.InflateToMinimum*"></a>
  <h4 id="VRageMath_BoundingBoxI_InflateToMinimum_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.InflateToMinimum(VRageMath.Vector3I)">InflateToMinimum(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InflateToMinimum(Vector3I minimumSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">minimumSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersect_" data-uid="VRageMath.BoundingBoxI.Intersect*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersect_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.Intersect(VRageMath.BoundingBoxI)">Intersect(BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Returns bounding box which is intersection of this and box<br>
Result is invalid box when there's no intersection (Min &gt; Max)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Intersect(BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.BoundingBoxI)">Intersects(BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects another BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxI to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_BoundingBoxI__" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.BoundingBoxI@)">Intersects(ref BoundingBoxI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_BoundingBoxI__System_Boolean__" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.BoundingBoxI@,System.Boolean@)">Intersects(ref BoundingBoxI, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects another BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBoxI box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxI to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingBoxI instances intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_Line_System_Single__" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.Line,System.Single@)">Intersects(Line, out Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(Line line, out float distance)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Line.html">Line</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">distance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_Plane_" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.Plane)">Intersects(Plane)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_Plane__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.Plane@,VRageMath.PlaneIntersectionType@)">Intersects(ref Plane, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Plane plane, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the BoundingBoxI intersects the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_Ray_" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.Ray)">Intersects(Ray)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(Ray ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Intersects_" data-uid="VRageMath.BoundingBoxI.Intersects*"></a>
  <h4 id="VRageMath_BoundingBoxI_Intersects_VRageMath_Ray__System_Nullable_System_Single___" data-uid="VRageMath.BoundingBoxI.Intersects(VRageMath.Ray@,System.Nullable{System.Single}@)">Intersects(ref Ray, out Nullable&lt;Single&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBoxI intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Ray ray, out Nullable&lt;float&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingBoxI, or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_IntersectsTriangle_" data-uid="VRageMath.BoundingBoxI.IntersectsTriangle*"></a>
  <h4 id="VRageMath_BoundingBoxI_IntersectsTriangle_VRageMath_Vector3I_VRageMath_Vector3I_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.IntersectsTriangle(VRageMath.Vector3I,VRageMath.Vector3I,VRageMath.Vector3I)">IntersectsTriangle(Vector3I, Vector3I, Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IntersectsTriangle(Vector3I v0, Vector3I v1, Vector3I v2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_IntersectsTriangle_" data-uid="VRageMath.BoundingBoxI.IntersectsTriangle*"></a>
  <h4 id="VRageMath_BoundingBoxI_IntersectsTriangle_VRageMath_Vector3I__VRageMath_Vector3I__VRageMath_Vector3I__" data-uid="VRageMath.BoundingBoxI.IntersectsTriangle(VRageMath.Vector3I@,VRageMath.Vector3I@,VRageMath.Vector3I@)">IntersectsTriangle(ref Vector3I, ref Vector3I, ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IntersectsTriangle(ref Vector3I v0, ref Vector3I v1, ref Vector3I v2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_IntersectWith_" data-uid="VRageMath.BoundingBoxI.IntersectWith*"></a>
  <h4 id="VRageMath_BoundingBoxI_IntersectWith_VRageMath_BoundingBoxI__" data-uid="VRageMath.BoundingBoxI.IntersectWith(VRageMath.BoundingBoxI@)">IntersectWith(ref BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Returns bounding box which is intersection of this and box<br>
Result is invalid box when there's no intersection (Min &gt; Max)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void IntersectWith(ref BoundingBoxI box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_IterateDifference_" data-uid="VRageMath.BoundingBoxI.IterateDifference*"></a>
  <h4 id="VRageMath_BoundingBoxI_IterateDifference_VRageMath_BoundingBoxI_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.IterateDifference(VRageMath.BoundingBoxI,VRageMath.BoundingBoxI)">IterateDifference(BoundingBoxI, BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Iterate every cell contained in {left} - {right},
where we interpret {box} as the set of all distinct Vector3I points inside a 'box'.</p>
<p>Containment is taken in a typical inclusive start, exclusive end fashion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static IEnumerable&lt;Vector3I&gt; IterateDifference(BoundingBoxI left, BoundingBoxI right)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">left</span></td>
        <td><p>The left bounding box.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">right</span></td>
        <td><p>The right bounding box.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector3I.html">Vector3I</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_SurfaceArea_" data-uid="VRageMath.BoundingBoxI.SurfaceArea*"></a>
  <h4 id="VRageMath_BoundingBoxI_SurfaceArea" data-uid="VRageMath.BoundingBoxI.SurfaceArea">SurfaceArea()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SurfaceArea()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_ToString_" data-uid="VRageMath.BoundingBoxI.ToString*"></a>
  <h4 id="VRageMath_BoundingBoxI_ToString" data-uid="VRageMath.BoundingBoxI.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingBoxI.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Translate_" data-uid="VRageMath.BoundingBoxI.Translate*"></a>
  <h4 id="VRageMath_BoundingBoxI_Translate_VRageMath_Vector3I_" data-uid="VRageMath.BoundingBoxI.Translate(VRageMath.Vector3I)">Translate(Vector3I)</h4>
  <div class="markdown level1 summary"><p>Translate</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxI Translate(Vector3I vctTranlsation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">vctTranlsation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_Volume_" data-uid="VRageMath.BoundingBoxI.Volume*"></a>
  <h4 id="VRageMath_BoundingBoxI_Volume" data-uid="VRageMath.BoundingBoxI.Volume">Volume()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Volume()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingBoxI_op_Equality_" data-uid="VRageMath.BoundingBoxI.op_Equality*"></a>
  <h4 id="VRageMath_BoundingBoxI_op_Equality_VRageMath_BoundingBoxI_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.op_Equality(VRageMath.BoundingBoxI,VRageMath.BoundingBoxI)">Equality(BoundingBoxI, BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBoxI are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingBoxI a, BoundingBoxI b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>BoundingBoxI to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>BoundingBoxI to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_op_Explicit_" data-uid="VRageMath.BoundingBoxI.op_Explicit*"></a>
  <h4 id="VRageMath_BoundingBoxI_op_Explicit_VRageMath_BoundingBox__VRageMath_BoundingBoxI" data-uid="VRageMath.BoundingBoxI.op_Explicit(VRageMath.BoundingBox)~VRageMath.BoundingBoxI">Explicit(BoundingBox to BoundingBoxI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator BoundingBoxI(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_op_Explicit_" data-uid="VRageMath.BoundingBoxI.op_Explicit*"></a>
  <h4 id="VRageMath_BoundingBoxI_op_Explicit_VRageMath_BoundingBoxD__VRageMath_BoundingBoxI" data-uid="VRageMath.BoundingBoxI.op_Explicit(VRageMath.BoundingBoxD)~VRageMath.BoundingBoxI">Explicit(BoundingBoxD to BoundingBoxI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator BoundingBoxI(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBoxI_op_Inequality_" data-uid="VRageMath.BoundingBoxI.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingBoxI_op_Inequality_VRageMath_BoundingBoxI_VRageMath_BoundingBoxI_" data-uid="VRageMath.BoundingBoxI.op_Inequality(VRageMath.BoundingBoxI,VRageMath.BoundingBoxI)">Inequality(BoundingBoxI, BoundingBoxI)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBoxI are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingBoxI a, BoundingBoxI b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
