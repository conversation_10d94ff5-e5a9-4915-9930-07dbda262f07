﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyVoxelMaterialDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyVoxelMaterialDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyVoxelMaterialDefinition">
  
  
  <h1 id="VRage_Game_MyVoxelMaterialDefinition" data-uid="VRage.Game.MyVoxelMaterialDefinition" class="text-break">Class MyVoxelMaterialDefinition
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.Game.MyDefinitionBase.html">MyDefinitionBase</a></div>
    <div class="level2"><span class="xref">MyVoxelMaterialDefinition</span></div>
      <div class="level3"><a class="xref" href="Sandbox.Definitions.MyDx11VoxelMaterialDefinition.html">MyDx11VoxelMaterialDefinition</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Id">MyDefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameEnum">MyDefinitionBase.DisplayNameEnum</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionEnum">MyDefinitionBase.DescriptionEnum</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameString">MyDefinitionBase.DisplayNameString</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionString">MyDefinitionBase.DescriptionString</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionArgs">MyDefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Icons">MyDefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Enabled">MyDefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Public">MyDefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_AvailableInSurvival">MyDefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Context">MyDefinitionBase.Context</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Init_VRage_Game_MyObjectBuilder_DefinitionBase_VRage_Game_MyModContext_">MyDefinitionBase.Init(MyObjectBuilder_DefinitionBase, MyModContext)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Postprocess">MyDefinitionBase.Postprocess()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_ToString">MyDefinitionBase.ToString()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_CheckDefinitionDLCs_System_String___">MyDefinitionBase.CheckDefinitionDLCs(String[])</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DLCs">MyDefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameText">MyDefinitionBase.DisplayNameText</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionText">MyDefinitionBase.DescriptionText</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyVoxelMaterialDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyDefinitionType]
public class MyVoxelMaterialDefinition : MyDefinitionBase</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition__ctor_" data-uid="VRage.Game.MyVoxelMaterialDefinition.#ctor*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition__ctor" data-uid="VRage.Game.MyVoxelMaterialDefinition.#ctor">MyVoxelMaterialDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyVoxelMaterialDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_AsteroidGeneratorSpawnProbabilityMultiplier" data-uid="VRage.Game.MyVoxelMaterialDefinition.AsteroidGeneratorSpawnProbabilityMultiplier">AsteroidGeneratorSpawnProbabilityMultiplier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int AsteroidGeneratorSpawnProbabilityMultiplier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_BareVariant" data-uid="VRage.Game.MyVoxelMaterialDefinition.BareVariant">BareVariant</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string BareVariant</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_CanBeHarvested" data-uid="VRage.Game.MyVoxelMaterialDefinition.CanBeHarvested">CanBeHarvested</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool CanBeHarvested</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_ColorKey" data-uid="VRage.Game.MyVoxelMaterialDefinition.ColorKey">ColorKey</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;Vector3&gt; ColorKey</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_DamagedMaterial" data-uid="VRage.Game.MyVoxelMaterialDefinition.DamagedMaterial">DamagedMaterial</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStringHash DamagedMaterial</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringHash.html">MyStringHash</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_Friction" data-uid="VRage.Game.MyVoxelMaterialDefinition.Friction">Friction</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Friction</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_IsRare" data-uid="VRage.Game.MyVoxelMaterialDefinition.IsRare">IsRare</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRare</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_LandingEffect" data-uid="VRage.Game.MyVoxelMaterialDefinition.LandingEffect">LandingEffect</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string LandingEffect</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MaterialTypeName" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaterialTypeName">MaterialTypeName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string MaterialTypeName</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MaxVersion" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaxVersion">MaxVersion</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int MaxVersion</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MinedOre" data-uid="VRage.Game.MyVoxelMaterialDefinition.MinedOre">MinedOre</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string MinedOre</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MinedOreRatio" data-uid="VRage.Game.MyVoxelMaterialDefinition.MinedOreRatio">MinedOreRatio</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float MinedOreRatio</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MinVersion" data-uid="VRage.Game.MyVoxelMaterialDefinition.MinVersion">MinVersion</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int MinVersion</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_RenderParams" data-uid="VRage.Game.MyVoxelMaterialDefinition.RenderParams">RenderParams</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyRenderVoxelMaterialData RenderParams</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.MyRenderVoxelMaterialData</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_Restitution" data-uid="VRage.Game.MyVoxelMaterialDefinition.Restitution">Restitution</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Restitution</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_SpawnsFromMeteorites" data-uid="VRage.Game.MyVoxelMaterialDefinition.SpawnsFromMeteorites">SpawnsFromMeteorites</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool SpawnsFromMeteorites</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_SpawnsInAsteroids" data-uid="VRage.Game.MyVoxelMaterialDefinition.SpawnsInAsteroids">SpawnsInAsteroids</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool SpawnsInAsteroids</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_VoxelHandPreview" data-uid="VRage.Game.MyVoxelMaterialDefinition.VoxelHandPreview">VoxelHandPreview</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string VoxelHandPreview</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_HasDamageMaterial_" data-uid="VRage.Game.MyVoxelMaterialDefinition.HasDamageMaterial*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_HasDamageMaterial" data-uid="VRage.Game.MyVoxelMaterialDefinition.HasDamageMaterial">HasDamageMaterial</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasDamageMaterial { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_Icon_" data-uid="VRage.Game.MyVoxelMaterialDefinition.Icon*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_Icon" data-uid="VRage.Game.MyVoxelMaterialDefinition.Icon">Icon</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Icon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_Index_" data-uid="VRage.Game.MyVoxelMaterialDefinition.Index*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_Index" data-uid="VRage.Game.MyVoxelMaterialDefinition.Index">Index</h4>
  <div class="markdown level1 summary"><p>Value generated at runtime to ensure correctness. Do not serialize or deserialize.
This is what the old cast to int used to result into, but now numbers depend on order in XML file.
TODO Serialize to XML and ensure upon loading that these values are starting from 0 and continuous.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Index { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_MaterialTypeNameHash_" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaterialTypeNameHash*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MaterialTypeNameHash" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaterialTypeNameHash">MaterialTypeNameHash</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStringHash MaterialTypeNameHash { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringHash.html">MyStringHash</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_MaterialTypeNameId_" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaterialTypeNameId*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_MaterialTypeNameId" data-uid="VRage.Game.MyVoxelMaterialDefinition.MaterialTypeNameId">MaterialTypeNameId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStringId MaterialTypeNameId { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_AssignIndex_" data-uid="VRage.Game.MyVoxelMaterialDefinition.AssignIndex*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_AssignIndex" data-uid="VRage.Game.MyVoxelMaterialDefinition.AssignIndex">AssignIndex()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AssignIndex()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_GetObjectBuilder_" data-uid="VRage.Game.MyVoxelMaterialDefinition.GetObjectBuilder*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_GetObjectBuilder" data-uid="VRage.Game.MyVoxelMaterialDefinition.GetObjectBuilder">GetObjectBuilder()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override MyObjectBuilder_DefinitionBase GetObjectBuilder()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_GetObjectBuilder">MyDefinitionBase.GetObjectBuilder()</a></div>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_Init_" data-uid="VRage.Game.MyVoxelMaterialDefinition.Init*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_Init_VRage_Game_MyObjectBuilder_DefinitionBase_" data-uid="VRage.Game.MyVoxelMaterialDefinition.Init(VRage.Game.MyObjectBuilder_DefinitionBase)">Init(MyObjectBuilder_DefinitionBase)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Init(MyObjectBuilder_DefinitionBase ob)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></td>
        <td><span class="parametername">ob</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Init_VRage_Game_MyObjectBuilder_DefinitionBase_">MyDefinitionBase.Init(MyObjectBuilder_DefinitionBase)</a></div>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_ResetIndexing_" data-uid="VRage.Game.MyVoxelMaterialDefinition.ResetIndexing*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_ResetIndexing" data-uid="VRage.Game.MyVoxelMaterialDefinition.ResetIndexing">ResetIndexing()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ResetIndexing()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyVoxelMaterialDefinition_UpdateVoxelMaterial_" data-uid="VRage.Game.MyVoxelMaterialDefinition.UpdateVoxelMaterial*"></a>
  <h4 id="VRage_Game_MyVoxelMaterialDefinition_UpdateVoxelMaterial" data-uid="VRage.Game.MyVoxelMaterialDefinition.UpdateVoxelMaterial">UpdateVoxelMaterial()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateVoxelMaterial()</code></pre>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
