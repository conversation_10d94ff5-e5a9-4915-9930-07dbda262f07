﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyParticleEffectsIDEnum
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyParticleEffectsIDEnum
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyParticleEffectsIDEnum">
  
  
  <h1 id="VRage_Game_MyParticleEffectsIDEnum" data-uid="VRage.Game.MyParticleEffectsIDEnum" class="text-break">Class MyParticleEffectsIDEnum
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyParticleEffectsIDEnum</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyParticleEffectsIDEnum_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MyParticleEffectsIDEnum : Enum</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_AngleGrinder" data-uid="VRage.Game.MyParticleEffectsIDEnum.AngleGrinder">AngleGrinder</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum AngleGrinder</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_ChipOff_Gravel" data-uid="VRage.Game.MyParticleEffectsIDEnum.ChipOff_Gravel">ChipOff_Gravel</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum ChipOff_Gravel</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_ChipOff_Wood" data-uid="VRage.Game.MyParticleEffectsIDEnum.ChipOff_Wood">ChipOff_Wood</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum ChipOff_Wood</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Collision_Meteor" data-uid="VRage.Game.MyParticleEffectsIDEnum.Collision_Meteor">Collision_Meteor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Collision_Meteor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Collision_Smoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.Collision_Smoke">Collision_Smoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Collision_Smoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Collision_Sparks" data-uid="VRage.Game.MyParticleEffectsIDEnum.Collision_Sparks">Collision_Sparks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Collision_Sparks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_CollisionSparksHandDrill" data-uid="VRage.Game.MyParticleEffectsIDEnum.CollisionSparksHandDrill">CollisionSparksHandDrill</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum CollisionSparksHandDrill</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_CollisionSparksLargeClose" data-uid="VRage.Game.MyParticleEffectsIDEnum.CollisionSparksLargeClose">CollisionSparksLargeClose</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum CollisionSparksLargeClose</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_CollisionSparksLargeDistant" data-uid="VRage.Game.MyParticleEffectsIDEnum.CollisionSparksLargeDistant">CollisionSparksLargeDistant</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum CollisionSparksLargeDistant</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Electrical" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Electrical">Damage_Electrical</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Electrical</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Gravitons" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Gravitons">Damage_Gravitons</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Gravitons</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Mechanical" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Mechanical">Damage_Mechanical</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Mechanical</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Radioactive" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Radioactive">Damage_Radioactive</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Radioactive</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Smoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Smoke">Damage_Smoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Smoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_SmokeBiochem" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_SmokeBiochem">Damage_SmokeBiochem</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_SmokeBiochem</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_SmokeDirectionalA" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_SmokeDirectionalA">Damage_SmokeDirectionalA</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_SmokeDirectionalA</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_SmokeDirectionalB" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_SmokeDirectionalB">Damage_SmokeDirectionalB</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_SmokeDirectionalB</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_SmokeDirectionalC" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_SmokeDirectionalC">Damage_SmokeDirectionalC</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_SmokeDirectionalC</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_Sparks" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_Sparks">Damage_Sparks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_Sparks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Damage_WeapExpl" data-uid="VRage.Game.MyParticleEffectsIDEnum.Damage_WeapExpl">Damage_WeapExpl</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Damage_WeapExpl</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_DestructionHit" data-uid="VRage.Game.MyParticleEffectsIDEnum.DestructionHit">DestructionHit</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum DestructionHit</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_DestructionSmokeLarge" data-uid="VRage.Game.MyParticleEffectsIDEnum.DestructionSmokeLarge">DestructionSmokeLarge</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum DestructionSmokeLarge</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_DestructionTree" data-uid="VRage.Game.MyParticleEffectsIDEnum.DestructionTree">DestructionTree</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum DestructionTree</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Laser" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Laser">Drill_Laser</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Laser</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Nuclear" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Nuclear">Drill_Nuclear</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Nuclear</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Nuclear_Original" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Nuclear_Original">Drill_Nuclear_Original</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Nuclear_Original</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Pressure_Charge" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Pressure_Charge">Drill_Pressure_Charge</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Pressure_Charge</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Pressure_Fire" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Pressure_Fire">Drill_Pressure_Fire</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Pressure_Fire</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Pressure_Impact" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Pressure_Impact">Drill_Pressure_Impact</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Pressure_Impact</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Pressure_Impact_Metal" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Pressure_Impact_Metal">Drill_Pressure_Impact_Metal</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Pressure_Impact_Metal</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Saw" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Saw">Drill_Saw</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Saw</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Drill_Thermal" data-uid="VRage.Game.MyParticleEffectsIDEnum.Drill_Thermal">Drill_Thermal</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Drill_Thermal</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Dummy" data-uid="VRage.Game.MyParticleEffectsIDEnum.Dummy">Dummy</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Dummy</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_EngineThrust" data-uid="VRage.Game.MyParticleEffectsIDEnum.EngineThrust">EngineThrust</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum EngineThrust</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Ammo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Ammo">Explosion_Ammo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Ammo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Asteroid" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Asteroid">Explosion_Asteroid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Asteroid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_BioChem" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_BioChem">Explosion_BioChem</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_BioChem</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Blaster" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Blaster">Explosion_Blaster</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Blaster</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Bomb" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Bomb">Explosion_Bomb</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Bomb</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_EMP" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_EMP">Explosion_EMP</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_EMP</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Huge" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Huge">Explosion_Huge</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Huge</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Large" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Large">Explosion_Large</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Large</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Medium" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Medium">Explosion_Medium</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Medium</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Meteor" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Meteor">Explosion_Meteor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Meteor</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Missile" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Missile">Explosion_Missile</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Missile</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Missile_Close" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Missile_Close">Explosion_Missile_Close</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Missile_Close</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Nuclear" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Nuclear">Explosion_Nuclear</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Nuclear</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Plasma" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Plasma">Explosion_Plasma</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Plasma</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_SmallPrefab" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_SmallPrefab">Explosion_SmallPrefab</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_SmallPrefab</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Smallship" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Smallship">Explosion_Smallship</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Smallship</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Warhead_02" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Warhead_02">Explosion_Warhead_02</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Warhead_02</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Warhead_15" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Warhead_15">Explosion_Warhead_15</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Warhead_15</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Warhead_30" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Warhead_30">Explosion_Warhead_30</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Warhead_30</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Explosion_Warhead_50" data-uid="VRage.Game.MyParticleEffectsIDEnum.Explosion_Warhead_50">Explosion_Warhead_50</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Explosion_Warhead_50</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_FireTorch" data-uid="VRage.Game.MyParticleEffectsIDEnum.FireTorch">FireTorch</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum FireTorch</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Grid_Deformation" data-uid="VRage.Game.MyParticleEffectsIDEnum.Grid_Deformation">Grid_Deformation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Grid_Deformation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Grid_Destruction" data-uid="VRage.Game.MyParticleEffectsIDEnum.Grid_Destruction">Grid_Destruction</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Grid_Destruction</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Harvester_Finished" data-uid="VRage.Game.MyParticleEffectsIDEnum.Harvester_Finished">Harvester_Finished</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Harvester_Finished</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Harvester_Harvesting" data-uid="VRage.Game.MyParticleEffectsIDEnum.Harvester_Harvesting">Harvester_Harvesting</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Harvester_Harvesting</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonBasicAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonBasicAmmo">Hit_AutocannonBasicAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonBasicAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonChemicalAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonChemicalAmmo">Hit_AutocannonChemicalAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonChemicalAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonEMPAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonEMPAmmo">Hit_AutocannonEMPAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonEMPAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonExplosiveAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonExplosiveAmmo">Hit_AutocannonExplosiveAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonExplosiveAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonHighSpeedAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonHighSpeedAmmo">Hit_AutocannonHighSpeedAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonHighSpeedAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_AutocannonPiercingAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_AutocannonPiercingAmmo">Hit_AutocannonPiercingAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_AutocannonPiercingAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_BasicAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_BasicAmmo">Hit_BasicAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_BasicAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_BasicAmmoSmall" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_BasicAmmoSmall">Hit_BasicAmmoSmall</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_BasicAmmoSmall</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_ChemicalAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_ChemicalAmmo">Hit_ChemicalAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_ChemicalAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_EMPAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_EMPAmmo">Hit_EMPAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_EMPAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_ExplosiveAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_ExplosiveAmmo">Hit_ExplosiveAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_ExplosiveAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_HighSpeedAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_HighSpeedAmmo">Hit_HighSpeedAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_HighSpeedAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Hit_PiercingAmmo" data-uid="VRage.Game.MyParticleEffectsIDEnum.Hit_PiercingAmmo">Hit_PiercingAmmo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Hit_PiercingAmmo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MaterialExplosion_Destructible" data-uid="VRage.Game.MyParticleEffectsIDEnum.MaterialExplosion_Destructible">MaterialExplosion_Destructible</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MaterialExplosion_Destructible</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MaterialHit_DestructibleSmall" data-uid="VRage.Game.MyParticleEffectsIDEnum.MaterialHit_DestructibleSmall">MaterialHit_DestructibleSmall</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MaterialHit_DestructibleSmall</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MaterialHit_IndestructibleSmall" data-uid="VRage.Game.MyParticleEffectsIDEnum.MaterialHit_IndestructibleSmall">MaterialHit_IndestructibleSmall</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MaterialHit_IndestructibleSmall</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MaterialHit_MetalSmall" data-uid="VRage.Game.MyParticleEffectsIDEnum.MaterialHit_MetalSmall">MaterialHit_MetalSmall</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MaterialHit_MetalSmall</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MaterialHit_MetalSparks" data-uid="VRage.Game.MyParticleEffectsIDEnum.MaterialHit_MetalSparks">MaterialHit_MetalSparks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MaterialHit_MetalSparks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MeteorAsteroidCollision" data-uid="VRage.Game.MyParticleEffectsIDEnum.MeteorAsteroidCollision">MeteorAsteroidCollision</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MeteorAsteroidCollision</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MeteorParticle" data-uid="VRage.Game.MyParticleEffectsIDEnum.MeteorParticle">MeteorParticle</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MeteorParticle</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MeteorParticleAfterHit" data-uid="VRage.Game.MyParticleEffectsIDEnum.MeteorParticleAfterHit">MeteorParticleAfterHit</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MeteorParticleAfterHit</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MeteorTrail_FireAndSmoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.MeteorTrail_FireAndSmoke">MeteorTrail_FireAndSmoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MeteorTrail_FireAndSmoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_MeteorTrail_Smoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.MeteorTrail_Smoke">MeteorTrail_Smoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum MeteorTrail_Smoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_None" data-uid="VRage.Game.MyParticleEffectsIDEnum.None">None</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum None</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_DestructionSmoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_DestructionSmoke">Prefab_DestructionSmoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_DestructionSmoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_DustyArea" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_DustyArea">Prefab_DustyArea</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_DustyArea</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_EMP_Storm" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_EMP_Storm">Prefab_EMP_Storm</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_EMP_Storm</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_Fire_Field" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_Fire_Field">Prefab_Fire_Field</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_Fire_Field</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingBiohazard" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingBiohazard">Prefab_LeakingBiohazard</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingBiohazard</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingBiohazard2" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingBiohazard2">Prefab_LeakingBiohazard2</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingBiohazard2</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingElectricity" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingElectricity">Prefab_LeakingElectricity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingElectricity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingFire" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingFire">Prefab_LeakingFire</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingFire</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingFire_x2" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingFire_x2">Prefab_LeakingFire_x2</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingFire_x2</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingSmoke" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingSmoke">Prefab_LeakingSmoke</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingSmoke</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingSteamBlack" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingSteamBlack">Prefab_LeakingSteamBlack</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingSteamBlack</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingSteamGrey" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingSteamGrey">Prefab_LeakingSteamGrey</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingSteamGrey</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Prefab_LeakingSteamWhite" data-uid="VRage.Game.MyParticleEffectsIDEnum.Prefab_LeakingSteamWhite">Prefab_LeakingSteamWhite</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Prefab_LeakingSteamWhite</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_Autocannon" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_Autocannon">Smoke_Autocannon</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_Autocannon</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_CannonShot" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_CannonShot">Smoke_CannonShot</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_CannonShot</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_Collector" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_Collector">Smoke_Collector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_Collector</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_Construction" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_Construction">Smoke_Construction</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_Construction</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_DrillDust" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_DrillDust">Smoke_DrillDust</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_DrillDust</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_HandDrillDust" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_HandDrillDust">Smoke_HandDrillDust</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_HandDrillDust</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_HandDrillDustStones" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_HandDrillDustStones">Smoke_HandDrillDustStones</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_HandDrillDustStones</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_LargeGunShot" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_LargeGunShot">Smoke_LargeGunShot</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_LargeGunShot</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_Missile" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_Missile">Smoke_Missile</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_Missile</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_MissileStart" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_MissileStart">Smoke_MissileStart</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_MissileStart</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Smoke_SmallGunShot" data-uid="VRage.Game.MyParticleEffectsIDEnum.Smoke_SmallGunShot">Smoke_SmallGunShot</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Smoke_SmallGunShot</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Trail_Shotgun" data-uid="VRage.Game.MyParticleEffectsIDEnum.Trail_Shotgun">Trail_Shotgun</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Trail_Shotgun</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_UniversalLauncher_DecoyFlare" data-uid="VRage.Game.MyParticleEffectsIDEnum.UniversalLauncher_DecoyFlare">UniversalLauncher_DecoyFlare</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum UniversalLauncher_DecoyFlare</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_UniversalLauncher_IlluminatingShell" data-uid="VRage.Game.MyParticleEffectsIDEnum.UniversalLauncher_IlluminatingShell">UniversalLauncher_IlluminatingShell</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum UniversalLauncher_IlluminatingShell</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_UniversalLauncher_SmokeBomb" data-uid="VRage.Game.MyParticleEffectsIDEnum.UniversalLauncher_SmokeBomb">UniversalLauncher_SmokeBomb</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum UniversalLauncher_SmokeBomb</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_value__" data-uid="VRage.Game.MyParticleEffectsIDEnum.value__">value__</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int value__</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_Welder" data-uid="VRage.Game.MyParticleEffectsIDEnum.Welder">Welder</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum Welder</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyParticleEffectsIDEnum_WelderSecondary" data-uid="VRage.Game.MyParticleEffectsIDEnum.WelderSecondary">WelderSecondary</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const MyParticleEffectsIDEnum WelderSecondary</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyParticleEffectsIDEnum.html">MyParticleEffectsIDEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
