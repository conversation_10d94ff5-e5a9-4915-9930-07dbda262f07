﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Quaternion
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Quaternion
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Quaternion">
  
  
  <h1 id="VRageMath_Quaternion" data-uid="VRageMath.Quaternion" class="text-break">Class Quaternion
  </h1>
  <div class="markdown level0 summary"><p>Defines a four-dimensional vector (x,y,z,w), which is used to efficiently rotate an object about the (x, y, z) vector by the angle theta, where w = cos(theta/2).</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Quaternion</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Quaternion_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Quaternion : ValueType, IEquatable&lt;Quaternion&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Quaternion__ctor_" data-uid="VRageMath.Quaternion.#ctor*"></a>
  <h4 id="VRageMath_Quaternion__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Quaternion.#ctor(System.Single,System.Single,System.Single,System.Single)">Quaternion(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Quaternion(float x, float y, float z, float w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>The x-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>The y-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>The z-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>The w-value of the quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion__ctor_" data-uid="VRageMath.Quaternion.#ctor*"></a>
  <h4 id="VRageMath_Quaternion__ctor_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Quaternion.#ctor(VRageMath.Vector3,System.Single)">Quaternion(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Quaternion(Vector3 vectorPart, float scalarPart)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vectorPart</span></td>
        <td><p>The vector component of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scalarPart</span></td>
        <td><p>The rotation component of the quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Quaternion_Identity" data-uid="VRageMath.Quaternion.Identity">Identity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Identity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Quaternion_W" data-uid="VRageMath.Quaternion.W">W</h4>
  <div class="markdown level1 summary"><p>Specifies the rotation component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float W</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Quaternion_X" data-uid="VRageMath.Quaternion.X">X</h4>
  <div class="markdown level1 summary"><p>Specifies the x-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Quaternion_Y" data-uid="VRageMath.Quaternion.Y">Y</h4>
  <div class="markdown level1 summary"><p>Specifies the y-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Quaternion_Z" data-uid="VRageMath.Quaternion.Z">Z</h4>
  <div class="markdown level1 summary"><p>Specifies the z-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Z</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Quaternion_Zero" data-uid="VRageMath.Quaternion.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Quaternion_Forward_" data-uid="VRageMath.Quaternion.Forward*"></a>
  <h4 id="VRageMath_Quaternion_Forward" data-uid="VRageMath.Quaternion.Forward">Forward</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Forward { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Right_" data-uid="VRageMath.Quaternion.Right*"></a>
  <h4 id="VRageMath_Quaternion_Right" data-uid="VRageMath.Quaternion.Right">Right</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Right { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Up_" data-uid="VRageMath.Quaternion.Up*"></a>
  <h4 id="VRageMath_Quaternion_Up" data-uid="VRageMath.Quaternion.Up">Up</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Up { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Quaternion_Add_" data-uid="VRageMath.Quaternion.Add*"></a>
  <h4 id="VRageMath_Quaternion_Add_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Add(VRageMath.Quaternion,VRageMath.Quaternion)">Add(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Add(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Add_" data-uid="VRageMath.Quaternion.Add*"></a>
  <h4 id="VRageMath_Quaternion_Add_VRageMath_Quaternion__VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Add(VRageMath.Quaternion@,VRageMath.Quaternion@,VRageMath.Quaternion@)">Add(ref Quaternion, ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Quaternion quaternion1, ref Quaternion quaternion2, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of adding the Quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Concatenate_" data-uid="VRageMath.Quaternion.Concatenate*"></a>
  <h4 id="VRageMath_Quaternion_Concatenate_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Concatenate(VRageMath.Quaternion,VRageMath.Quaternion)">Concatenate(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Concatenates two Quaternions; the result represents the value1 rotation followed by the value2 rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Concatenate(Quaternion value1, Quaternion value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Quaternion rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Quaternion rotation in the series.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Concatenate_" data-uid="VRageMath.Quaternion.Concatenate*"></a>
  <h4 id="VRageMath_Quaternion_Concatenate_VRageMath_Quaternion__VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Concatenate(VRageMath.Quaternion@,VRageMath.Quaternion@,VRageMath.Quaternion@)">Concatenate(ref Quaternion, ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Concatenates two Quaternions; the result represents the value1 rotation followed by the value2 rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Concatenate(ref Quaternion value1, ref Quaternion value2, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Quaternion rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Quaternion rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Quaternion rotation representing the concatenation of value1 followed by value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Conjugate_" data-uid="VRageMath.Quaternion.Conjugate*"></a>
  <h4 id="VRageMath_Quaternion_Conjugate" data-uid="VRageMath.Quaternion.Conjugate">Conjugate()</h4>
  <div class="markdown level1 summary"><p>Transforms this Quaternion into its conjugate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Conjugate()</code></pre>
  </div>
  
  
  <a id="VRageMath_Quaternion_Conjugate_" data-uid="VRageMath.Quaternion.Conjugate*"></a>
  <h4 id="VRageMath_Quaternion_Conjugate_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Conjugate(VRageMath.Quaternion)">Conjugate(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Returns the conjugate of a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Conjugate(Quaternion value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Quaternion of which to return the conjugate.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Conjugate_" data-uid="VRageMath.Quaternion.Conjugate*"></a>
  <h4 id="VRageMath_Quaternion_Conjugate_VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Conjugate(VRageMath.Quaternion@,VRageMath.Quaternion@)">Conjugate(ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Returns the conjugate of a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Conjugate(ref Quaternion value, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Quaternion of which to return the conjugate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Quaternion filled in to be the conjugate of the specified one.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromAxisAngle_" data-uid="VRageMath.Quaternion.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromAxisAngle_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Quaternion.CreateFromAxisAngle(VRageMath.Vector3,System.Single)">CreateFromAxisAngle(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a Quaternion from a vector and an angle to rotate about the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromAxisAngle(Vector3 axis, float angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The vector to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromAxisAngle_" data-uid="VRageMath.Quaternion.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromAxisAngle_VRageMath_Vector3__System_Single_VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromAxisAngle(VRageMath.Vector3@,System.Single,VRageMath.Quaternion@)">CreateFromAxisAngle(ref Vector3, Single, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a Quaternion from a vector and an angle to rotate about the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAxisAngle(ref Vector3 axis, float angle, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The vector to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromForwardUp_" data-uid="VRageMath.Quaternion.CreateFromForwardUp*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromForwardUp_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Quaternion.CreateFromForwardUp(VRageMath.Vector3,VRageMath.Vector3)">CreateFromForwardUp(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Works for normalized vectors only</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromForwardUp(Vector3 forward, Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromRotationMatrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromRotationMatrix_VRageMath_Matrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix(VRageMath.Matrix)">CreateFromRotationMatrix(Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a Quaternion from a rotation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromRotationMatrix(Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The rotation Matrix to create the Quaternion from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromRotationMatrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromRotationMatrix_VRageMath_Matrix__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix(VRageMath.Matrix@,VRageMath.Quaternion@)">CreateFromRotationMatrix(ref Matrix, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a Quaternion from a rotation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromRotationMatrix(ref Matrix matrix, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The rotation Matrix to create the Quaternion from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromRotationMatrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromRotationMatrix_VRageMath_Matrix3x3__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix(VRageMath.Matrix3x3@,VRageMath.Quaternion@)">CreateFromRotationMatrix(ref Matrix3x3, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a Quaternion from a rotation Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromRotationMatrix(ref Matrix3x3 matrix, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix3x3.html">Matrix3x3</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The rotation Matrix to create the Quaternion from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromRotationMatrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromRotationMatrix_VRageMath_MatrixD__" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix(VRageMath.MatrixD@)">CreateFromRotationMatrix(in MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromRotationMatrix(in MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromRotationMatrix_" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromRotationMatrix_VRageMath_MatrixD__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromRotationMatrix(VRageMath.MatrixD@,VRageMath.Quaternion@)">CreateFromRotationMatrix(ref MatrixD, out Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromRotationMatrix(ref MatrixD matrix, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromTwoVectors_" data-uid="VRageMath.Quaternion.CreateFromTwoVectors*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromTwoVectors_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Quaternion.CreateFromTwoVectors(VRageMath.Vector3,VRageMath.Vector3)">CreateFromTwoVectors(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromTwoVectors(Vector3 firstVector, Vector3 secondVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">firstVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">secondVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromTwoVectors_" data-uid="VRageMath.Quaternion.CreateFromTwoVectors*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromTwoVectors_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromTwoVectors(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Quaternion@)">CreateFromTwoVectors(ref Vector3, ref Vector3, out Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromTwoVectors(ref Vector3 firstVector, ref Vector3 secondVector, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">firstVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">secondVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromYawPitchRoll_" data-uid="VRageMath.Quaternion.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_" data-uid="VRageMath.Quaternion.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">CreateFromYawPitchRoll(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new Quaternion from specified yaw, pitch, and roll angles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion CreateFromYawPitchRoll(float yaw, float pitch, float roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>The yaw angle, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>The pitch angle, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>The roll angle, in radians, around the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_CreateFromYawPitchRoll_" data-uid="VRageMath.Quaternion.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_Quaternion_CreateFromYawPitchRoll_System_Single_System_Single_System_Single_VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.CreateFromYawPitchRoll(System.Single,System.Single,System.Single,VRageMath.Quaternion@)">CreateFromYawPitchRoll(Single, Single, Single, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates a new Quaternion from specified yaw, pitch, and roll angles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromYawPitchRoll(float yaw, float pitch, float roll, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>The yaw angle, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>The pitch angle, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>The roll angle, in radians, around the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Quaternion filled in to express the specified yaw, pitch, and roll angles.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Divide_" data-uid="VRageMath.Quaternion.Divide*"></a>
  <h4 id="VRageMath_Quaternion_Divide_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Divide(VRageMath.Quaternion,VRageMath.Quaternion)">Divide(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Divides a Quaternion by another Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Divide(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Divide_" data-uid="VRageMath.Quaternion.Divide*"></a>
  <h4 id="VRageMath_Quaternion_Divide_VRageMath_Quaternion__VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Divide(VRageMath.Quaternion@,VRageMath.Quaternion@,VRageMath.Quaternion@)">Divide(ref Quaternion, ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Divides a Quaternion by another Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Quaternion quaternion1, ref Quaternion quaternion2, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Dot_" data-uid="VRageMath.Quaternion.Dot*"></a>
  <h4 id="VRageMath_Quaternion_Dot_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Dot(VRageMath.Quaternion,VRageMath.Quaternion)">Dot(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Dot(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Dot_" data-uid="VRageMath.Quaternion.Dot*"></a>
  <h4 id="VRageMath_Quaternion_Dot_VRageMath_Quaternion__VRageMath_Quaternion__System_Single__" data-uid="VRageMath.Quaternion.Dot(VRageMath.Quaternion@,VRageMath.Quaternion@,System.Single@)">Dot(ref Quaternion, ref Quaternion, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Quaternion quaternion1, ref Quaternion quaternion2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Dot product of the Quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Equals_" data-uid="VRageMath.Quaternion.Equals*"></a>
  <h4 id="VRageMath_Quaternion_Equals_System_Object_" data-uid="VRageMath.Quaternion.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Equals_" data-uid="VRageMath.Quaternion.Equals*"></a>
  <h4 id="VRageMath_Quaternion_Equals_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Equals(VRageMath.Quaternion)">Equals(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Quaternion other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Quaternion to compare with the current Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Equals_" data-uid="VRageMath.Quaternion.Equals*"></a>
  <h4 id="VRageMath_Quaternion_Equals_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.Equals(VRageMath.Quaternion,System.Single)">Equals(Quaternion, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Quaternion value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_FindLargestIndex_" data-uid="VRageMath.Quaternion.FindLargestIndex*"></a>
  <h4 id="VRageMath_Quaternion_FindLargestIndex" data-uid="VRageMath.Quaternion.FindLargestIndex">FindLargestIndex()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int FindLargestIndex()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_FromVector4_" data-uid="VRageMath.Quaternion.FromVector4*"></a>
  <h4 id="VRageMath_Quaternion_FromVector4_VRageMath_Vector4_" data-uid="VRageMath.Quaternion.FromVector4(VRageMath.Vector4)">FromVector4(Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion FromVector4(Vector4 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetAxisAngle_" data-uid="VRageMath.Quaternion.GetAxisAngle*"></a>
  <h4 id="VRageMath_Quaternion_GetAxisAngle_VRageMath_Vector3__System_Single__" data-uid="VRageMath.Quaternion.GetAxisAngle(VRageMath.Vector3@,System.Single@)">GetAxisAngle(out Vector3, out Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAxisAngle(out Vector3 axis, out float angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">axis</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetComponent_" data-uid="VRageMath.Quaternion.GetComponent*"></a>
  <h4 id="VRageMath_Quaternion_GetComponent_System_Int32_" data-uid="VRageMath.Quaternion.GetComponent(System.Int32)">GetComponent(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float GetComponent(int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetForward_" data-uid="VRageMath.Quaternion.GetForward*"></a>
  <h4 id="VRageMath_Quaternion_GetForward_VRageMath_Quaternion__VRageMath_Vector3__" data-uid="VRageMath.Quaternion.GetForward(VRageMath.Quaternion@,VRageMath.Vector3@)">GetForward(ref Quaternion, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Gets forward vector (0,0,-1) transformed by quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetForward(ref Quaternion q, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">q</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetHashCode_" data-uid="VRageMath.Quaternion.GetHashCode*"></a>
  <h4 id="VRageMath_Quaternion_GetHashCode" data-uid="VRageMath.Quaternion.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Get the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetRight_" data-uid="VRageMath.Quaternion.GetRight*"></a>
  <h4 id="VRageMath_Quaternion_GetRight_VRageMath_Quaternion__VRageMath_Vector3__" data-uid="VRageMath.Quaternion.GetRight(VRageMath.Quaternion@,VRageMath.Vector3@)">GetRight(ref Quaternion, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Gets right vector (1,0,0) transformed by quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetRight(ref Quaternion q, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">q</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_GetUp_" data-uid="VRageMath.Quaternion.GetUp*"></a>
  <h4 id="VRageMath_Quaternion_GetUp_VRageMath_Quaternion__VRageMath_Vector3__" data-uid="VRageMath.Quaternion.GetUp(VRageMath.Quaternion@,VRageMath.Vector3@)">GetUp(ref Quaternion, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Gets up vector (0,1,0) transformed by quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetUp(ref Quaternion q, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">q</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Inverse_" data-uid="VRageMath.Quaternion.Inverse*"></a>
  <h4 id="VRageMath_Quaternion_Inverse_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Inverse(VRageMath.Quaternion)">Inverse(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Returns the inverse of a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Inverse(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Inverse_" data-uid="VRageMath.Quaternion.Inverse*"></a>
  <h4 id="VRageMath_Quaternion_Inverse_VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Inverse(VRageMath.Quaternion@,VRageMath.Quaternion@)">Inverse(ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Returns the inverse of a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Inverse(ref Quaternion quaternion, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The inverse of the Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_IsZero_" data-uid="VRageMath.Quaternion.IsZero*"></a>
  <h4 id="VRageMath_Quaternion_IsZero_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.IsZero(VRageMath.Quaternion)">IsZero(Quaternion)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Quaternion value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_IsZero_" data-uid="VRageMath.Quaternion.IsZero*"></a>
  <h4 id="VRageMath_Quaternion_IsZero_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.IsZero(VRageMath.Quaternion,System.Single)">IsZero(Quaternion, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Quaternion value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Length_" data-uid="VRageMath.Quaternion.Length*"></a>
  <h4 id="VRageMath_Quaternion_Length" data-uid="VRageMath.Quaternion.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_LengthSquared_" data-uid="VRageMath.Quaternion.LengthSquared*"></a>
  <h4 id="VRageMath_Quaternion_LengthSquared" data-uid="VRageMath.Quaternion.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length squared of a Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Lerp_" data-uid="VRageMath.Quaternion.Lerp*"></a>
  <h4 id="VRageMath_Quaternion_Lerp_VRageMath_Quaternion_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.Lerp(VRageMath.Quaternion,VRageMath.Quaternion,System.Single)">Lerp(Quaternion, Quaternion, Single)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Lerp(Quaternion quaternion1, Quaternion quaternion2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value indicating how far to interpolate between the quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Lerp_" data-uid="VRageMath.Quaternion.Lerp*"></a>
  <h4 id="VRageMath_Quaternion_Lerp_VRageMath_Quaternion__VRageMath_Quaternion__System_Single_VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Lerp(VRageMath.Quaternion@,VRageMath.Quaternion@,System.Single,VRageMath.Quaternion@)">Lerp(ref Quaternion, ref Quaternion, Single, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Quaternion quaternion1, ref Quaternion quaternion2, float amount, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value indicating how far to interpolate between the quaternions.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Multiply_" data-uid="VRageMath.Quaternion.Multiply*"></a>
  <h4 id="VRageMath_Quaternion_Multiply_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.Multiply(VRageMath.Quaternion,System.Single)">Multiply(Quaternion, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Multiply(Quaternion quaternion1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Multiply_" data-uid="VRageMath.Quaternion.Multiply*"></a>
  <h4 id="VRageMath_Quaternion_Multiply_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Multiply(VRageMath.Quaternion,VRageMath.Quaternion)">Multiply(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Multiply(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>The quaternion on the left of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The quaternion on the right of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Multiply_" data-uid="VRageMath.Quaternion.Multiply*"></a>
  <h4 id="VRageMath_Quaternion_Multiply_VRageMath_Quaternion__System_Single_VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Multiply(VRageMath.Quaternion@,System.Single,VRageMath.Quaternion@)">Multiply(ref Quaternion, Single, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Quaternion quaternion1, float scaleFactor, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Multiply_" data-uid="VRageMath.Quaternion.Multiply*"></a>
  <h4 id="VRageMath_Quaternion_Multiply_VRageMath_Quaternion__VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Multiply(VRageMath.Quaternion@,VRageMath.Quaternion@,VRageMath.Quaternion@)">Multiply(ref Quaternion, ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Quaternion quaternion1, ref Quaternion quaternion2, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>The quaternion on the left of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The quaternion on the right of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Negate_" data-uid="VRageMath.Quaternion.Negate*"></a>
  <h4 id="VRageMath_Quaternion_Negate_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Negate(VRageMath.Quaternion)">Negate(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Negate(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Negate_" data-uid="VRageMath.Quaternion.Negate*"></a>
  <h4 id="VRageMath_Quaternion_Negate_VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Negate(VRageMath.Quaternion@,VRageMath.Quaternion@)">Negate(ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Quaternion quaternion, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Negated quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Normalize_" data-uid="VRageMath.Quaternion.Normalize*"></a>
  <h4 id="VRageMath_Quaternion_Normalize" data-uid="VRageMath.Quaternion.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Quaternion_Normalize_" data-uid="VRageMath.Quaternion.Normalize*"></a>
  <h4 id="VRageMath_Quaternion_Normalize_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Normalize(VRageMath.Quaternion)">Normalize(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Normalize(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Normalize_" data-uid="VRageMath.Quaternion.Normalize*"></a>
  <h4 id="VRageMath_Quaternion_Normalize_VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Normalize(VRageMath.Quaternion@,VRageMath.Quaternion@)">Normalize(ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Quaternion quaternion, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Normalized quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_SetComponent_" data-uid="VRageMath.Quaternion.SetComponent*"></a>
  <h4 id="VRageMath_Quaternion_SetComponent_System_Int32_System_Single_" data-uid="VRageMath.Quaternion.SetComponent(System.Int32,System.Single)">SetComponent(Int32, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetComponent(int index, float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Slerp_" data-uid="VRageMath.Quaternion.Slerp*"></a>
  <h4 id="VRageMath_Quaternion_Slerp_VRageMath_Quaternion_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.Slerp(VRageMath.Quaternion,VRageMath.Quaternion,System.Single)">Slerp(Quaternion, Quaternion, Single)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two quaternions, using spherical linear interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Slerp(Quaternion quaternion1, Quaternion quaternion2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value that indicates how far to interpolate between the quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Slerp_" data-uid="VRageMath.Quaternion.Slerp*"></a>
  <h4 id="VRageMath_Quaternion_Slerp_VRageMath_Quaternion__VRageMath_Quaternion__System_Single_VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Slerp(VRageMath.Quaternion@,VRageMath.Quaternion@,System.Single,VRageMath.Quaternion@)">Slerp(ref Quaternion, ref Quaternion, Single, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two quaternions, using spherical linear interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(ref Quaternion quaternion1, ref Quaternion quaternion2, float amount, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value that indicates how far to interpolate between the quaternions.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Subtract_" data-uid="VRageMath.Quaternion.Subtract*"></a>
  <h4 id="VRageMath_Quaternion_Subtract_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.Subtract(VRageMath.Quaternion,VRageMath.Quaternion)">Subtract(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion Subtract(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_Subtract_" data-uid="VRageMath.Quaternion.Subtract*"></a>
  <h4 id="VRageMath_Quaternion_Subtract_VRageMath_Quaternion__VRageMath_Quaternion__VRageMath_Quaternion__" data-uid="VRageMath.Quaternion.Subtract(VRageMath.Quaternion@,VRageMath.Quaternion@,VRageMath.Quaternion@)">Subtract(ref Quaternion, ref Quaternion, out Quaternion)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Quaternion quaternion1, ref Quaternion quaternion2, out Quaternion result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_ToString_" data-uid="VRageMath.Quaternion.ToString*"></a>
  <h4 id="VRageMath_Quaternion_ToString" data-uid="VRageMath.Quaternion.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retireves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_ToString_" data-uid="VRageMath.Quaternion.ToString*"></a>
  <h4 id="VRageMath_Quaternion_ToString_System_String_" data-uid="VRageMath.Quaternion.ToString(System.String)">ToString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string ToString(string format)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">format</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_ToStringAxisAngle_" data-uid="VRageMath.Quaternion.ToStringAxisAngle*"></a>
  <h4 id="VRageMath_Quaternion_ToStringAxisAngle_System_String_" data-uid="VRageMath.Quaternion.ToStringAxisAngle(System.String)">ToStringAxisAngle(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string ToStringAxisAngle(string format = &quot;G&quot;)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">format</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_ToVector4_" data-uid="VRageMath.Quaternion.ToVector4*"></a>
  <h4 id="VRageMath_Quaternion_ToVector4" data-uid="VRageMath.Quaternion.ToVector4">ToVector4()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4 ToVector4()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Quaternion_op_Addition_" data-uid="VRageMath.Quaternion.op_Addition*"></a>
  <h4 id="VRageMath_Quaternion_op_Addition_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Addition(VRageMath.Quaternion,VRageMath.Quaternion)">Addition(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator +(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Quaternion to add.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Division_" data-uid="VRageMath.Quaternion.op_Division*"></a>
  <h4 id="VRageMath_Quaternion_op_Division_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Division(VRageMath.Quaternion,VRageMath.Quaternion)">Division(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Divides a Quaternion by another Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator /(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Equality_" data-uid="VRageMath.Quaternion.op_Equality*"></a>
  <h4 id="VRageMath_Quaternion_op_Equality_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Equality(VRageMath.Quaternion,VRageMath.Quaternion)">Equality(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Compares two Quaternions for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Inequality_" data-uid="VRageMath.Quaternion.op_Inequality*"></a>
  <h4 id="VRageMath_Quaternion_op_Inequality_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Inequality(VRageMath.Quaternion,VRageMath.Quaternion)">Inequality(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Compare two Quaternions for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source Quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Multiply_" data-uid="VRageMath.Quaternion.op_Multiply*"></a>
  <h4 id="VRageMath_Quaternion_op_Multiply_VRageMath_Quaternion_System_Single_" data-uid="VRageMath.Quaternion.op_Multiply(VRageMath.Quaternion,System.Single)">Multiply(Quaternion, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator *(Quaternion quaternion1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Multiply_" data-uid="VRageMath.Quaternion.op_Multiply*"></a>
  <h4 id="VRageMath_Quaternion_op_Multiply_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Multiply(VRageMath.Quaternion,VRageMath.Quaternion)">Multiply(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator *(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Multiply_" data-uid="VRageMath.Quaternion.op_Multiply*"></a>
  <h4 id="VRageMath_Quaternion_op_Multiply_VRageMath_Quaternion_VRageMath_Vector3_" data-uid="VRageMath.Quaternion.op_Multiply(VRageMath.Quaternion,VRageMath.Vector3)">Multiply(Quaternion, Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a quaternion. Resulting vector rotated by quaternion</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator *(Quaternion quaternion, Vector3 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Vector to be rotated.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_Subtraction_" data-uid="VRageMath.Quaternion.op_Subtraction*"></a>
  <h4 id="VRageMath_Quaternion_op_Subtraction_VRageMath_Quaternion_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_Subtraction(VRageMath.Quaternion,VRageMath.Quaternion)">Subtraction(Quaternion, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator -(Quaternion quaternion1, Quaternion quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Quaternion_op_UnaryNegation_" data-uid="VRageMath.Quaternion.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Quaternion_op_UnaryNegation_VRageMath_Quaternion_" data-uid="VRageMath.Quaternion.op_UnaryNegation(VRageMath.Quaternion)">UnaryNegation(Quaternion)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Quaternion operator -(Quaternion quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
