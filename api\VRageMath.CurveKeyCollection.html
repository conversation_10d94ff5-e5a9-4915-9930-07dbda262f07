﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class CurveKeyCollection
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class CurveKeyCollection
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.CurveKeyCollection">
  
  
  <h1 id="VRageMath_CurveKeyCollection" data-uid="VRageMath.CurveKeyCollection" class="text-break">Class CurveKeyCollection
  </h1>
  <div class="markdown level0 summary"><p>Contains the CurveKeys making up a Curve.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">CurveKeyCollection</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_CurveKeyCollection_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class CurveKeyCollection : Object, ICollection&lt;CurveKey&gt;, IEnumerable&lt;CurveKey&gt;, IEnumerable</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_CurveKeyCollection__ctor_" data-uid="VRageMath.CurveKeyCollection.#ctor*"></a>
  <h4 id="VRageMath_CurveKeyCollection__ctor" data-uid="VRageMath.CurveKeyCollection.#ctor">CurveKeyCollection()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKeyCollection()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_CurveKeyCollection_Count_" data-uid="VRageMath.CurveKeyCollection.Count*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Count" data-uid="VRageMath.CurveKeyCollection.Count">Count</h4>
  <div class="markdown level1 summary"><p>Gets the number of elements contained in the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Count { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_IsReadOnly_" data-uid="VRageMath.CurveKeyCollection.IsReadOnly*"></a>
  <h4 id="VRageMath_CurveKeyCollection_IsReadOnly" data-uid="VRageMath.CurveKeyCollection.IsReadOnly">IsReadOnly</h4>
  <div class="markdown level1 summary"><p>Returns a value indicating whether the CurveKeyCollection is read-only.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsReadOnly { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_Item_" data-uid="VRageMath.CurveKeyCollection.Item*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Item_System_Int32_" data-uid="VRageMath.CurveKeyCollection.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"><p>Gets or sets the element at the specified index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey this[int index] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td><p>The array index of the element.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_CurveKeyCollection_Add_" data-uid="VRageMath.CurveKeyCollection.Add*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Add_System_Object_" data-uid="VRageMath.CurveKeyCollection.Add(System.Object)">Add(Object)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Add(object tmp)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">tmp</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_Add_" data-uid="VRageMath.CurveKeyCollection.Add*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Add_VRageMath_CurveKey_" data-uid="VRageMath.CurveKeyCollection.Add(VRageMath.CurveKey)">Add(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Adds a CurveKey to the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Add(CurveKey item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">item</span></td>
        <td><p>The CurveKey to add.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_Clear_" data-uid="VRageMath.CurveKeyCollection.Clear*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Clear" data-uid="VRageMath.CurveKeyCollection.Clear">Clear()</h4>
  <div class="markdown level1 summary"><p>Removes all CurveKeys from the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRageMath_CurveKeyCollection_Clone_" data-uid="VRageMath.CurveKeyCollection.Clone*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Clone" data-uid="VRageMath.CurveKeyCollection.Clone">Clone()</h4>
  <div class="markdown level1 summary"><p>Creates a copy of the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKeyCollection Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKeyCollection.html">CurveKeyCollection</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_Contains_" data-uid="VRageMath.CurveKeyCollection.Contains*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Contains_VRageMath_CurveKey_" data-uid="VRageMath.CurveKeyCollection.Contains(VRageMath.CurveKey)">Contains(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Determines whether the CurveKeyCollection contains a specific CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(CurveKey item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">item</span></td>
        <td><p>true if the CurveKey is found in the CurveKeyCollection; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_CopyTo_" data-uid="VRageMath.CurveKeyCollection.CopyTo*"></a>
  <h4 id="VRageMath_CurveKeyCollection_CopyTo_VRageMath_CurveKey___System_Int32_" data-uid="VRageMath.CurveKeyCollection.CopyTo(VRageMath.CurveKey[],System.Int32)">CopyTo(CurveKey[], Int32)</h4>
  <div class="markdown level1 summary"><p>Copies the CurveKeys of the CurveKeyCollection to an array, starting at the array index provided.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CopyTo(CurveKey[] array, int arrayIndex)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a>[]</td>
        <td><span class="parametername">array</span></td>
        <td><p>The destination of the CurveKeys copied from CurveKeyCollection. The array must have zero-based indexing.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">arrayIndex</span></td>
        <td><p>The zero-based index in the array to start copying from.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_GetEnumerator_" data-uid="VRageMath.CurveKeyCollection.GetEnumerator*"></a>
  <h4 id="VRageMath_CurveKeyCollection_GetEnumerator" data-uid="VRageMath.CurveKeyCollection.GetEnumerator">GetEnumerator()</h4>
  <div class="markdown level1 summary"><p>Returns an enumerator that iterates through the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IEnumerator&lt;CurveKey&gt; GetEnumerator()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerator</span>&lt;<a class="xref" href="VRageMath.CurveKey.html">CurveKey</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_IndexOf_" data-uid="VRageMath.CurveKeyCollection.IndexOf*"></a>
  <h4 id="VRageMath_CurveKeyCollection_IndexOf_VRageMath_CurveKey_" data-uid="VRageMath.CurveKeyCollection.IndexOf(VRageMath.CurveKey)">IndexOf(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Determines the index of a CurveKey in the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int IndexOf(CurveKey item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">item</span></td>
        <td><p>CurveKey to locate in the CurveKeyCollection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_Remove_" data-uid="VRageMath.CurveKeyCollection.Remove*"></a>
  <h4 id="VRageMath_CurveKeyCollection_Remove_VRageMath_CurveKey_" data-uid="VRageMath.CurveKeyCollection.Remove(VRageMath.CurveKey)">Remove(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Removes the first occurrence of a specific CurveKey from the CurveKeyCollection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Remove(CurveKey item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">item</span></td>
        <td><p>The CurveKey to remove from the CurveKeyCollection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKeyCollection_RemoveAt_" data-uid="VRageMath.CurveKeyCollection.RemoveAt*"></a>
  <h4 id="VRageMath_CurveKeyCollection_RemoveAt_System_Int32_" data-uid="VRageMath.CurveKeyCollection.RemoveAt(System.Int32)">RemoveAt(Int32)</h4>
  <div class="markdown level1 summary"><p>Removes the CurveKey at the specified index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveAt(int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td><p>The zero-based index of the item to remove.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
