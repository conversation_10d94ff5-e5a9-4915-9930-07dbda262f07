﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyDebugHitCounter
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyDebugHitCounter
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Utils.MyDebugHitCounter">
  
  
  <h1 id="VRage_Utils_MyDebugHitCounter" data-uid="VRage.Utils.MyDebugHitCounter" class="text-break">Class MyDebugHitCounter
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyDebugHitCounter</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Utils.html">VRage.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Utils_MyDebugHitCounter_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyDebugHitCounter : Object, IEnumerable&lt;MyDebugHitCounter.Sample&gt;, IEnumerable</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Utils_MyDebugHitCounter__ctor_" data-uid="VRage.Utils.MyDebugHitCounter.#ctor*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter__ctor_System_UInt32_" data-uid="VRage.Utils.MyDebugHitCounter.#ctor(System.UInt32)">MyDebugHitCounter(UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyDebugHitCounter(uint cycleSize = 100000U)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">cycleSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Utils_MyDebugHitCounter_History" data-uid="VRage.Utils.MyDebugHitCounter.History">History</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly MyQueue&lt;MyDebugHitCounter.Sample&gt; History</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Collections.MyQueue-1.html">MyQueue</a>&lt;<a class="xref" href="VRage.Utils.MyDebugHitCounter.Sample.html">MyDebugHitCounter.Sample</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_CurrentHitRatio_" data-uid="VRage.Utils.MyDebugHitCounter.CurrentHitRatio*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_CurrentHitRatio" data-uid="VRage.Utils.MyDebugHitCounter.CurrentHitRatio">CurrentHitRatio</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float CurrentHitRatio { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_LastCycleHitRatio_" data-uid="VRage.Utils.MyDebugHitCounter.LastCycleHitRatio*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_LastCycleHitRatio" data-uid="VRage.Utils.MyDebugHitCounter.LastCycleHitRatio">LastCycleHitRatio</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LastCycleHitRatio { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_Cycle_" data-uid="VRage.Utils.MyDebugHitCounter.Cycle*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_Cycle" data-uid="VRage.Utils.MyDebugHitCounter.Cycle">Cycle()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Cycle()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_CycleWork_" data-uid="VRage.Utils.MyDebugHitCounter.CycleWork*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_CycleWork" data-uid="VRage.Utils.MyDebugHitCounter.CycleWork">CycleWork()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CycleWork()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_GetEnumerator_" data-uid="VRage.Utils.MyDebugHitCounter.GetEnumerator*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_GetEnumerator" data-uid="VRage.Utils.MyDebugHitCounter.GetEnumerator">GetEnumerator()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ConcurrentEnumerator&lt;SpinLockRef.Token, MyDebugHitCounter.Sample, IEnumerator&lt;MyDebugHitCounter.Sample&gt;&gt; GetEnumerator()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Library.Collections.ConcurrentEnumerator</span>&lt;<span class="xref">VRage.Library.Threading.SpinLockRef.Token</span>, <a class="xref" href="VRage.Utils.MyDebugHitCounter.Sample.html">MyDebugHitCounter.Sample</a>, <span class="xref">System.Collections.Generic.IEnumerator</span>&lt;<a class="xref" href="VRage.Utils.MyDebugHitCounter.Sample.html">MyDebugHitCounter.Sample</a>&gt;&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_Hit_" data-uid="VRage.Utils.MyDebugHitCounter.Hit*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_Hit" data-uid="VRage.Utils.MyDebugHitCounter.Hit">Hit()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Hit()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_Miss_" data-uid="VRage.Utils.MyDebugHitCounter.Miss*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_Miss" data-uid="VRage.Utils.MyDebugHitCounter.Miss">Miss()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Miss()</code></pre>
  </div>
  
  
  <a id="VRage_Utils_MyDebugHitCounter_ValueAndCycle_" data-uid="VRage.Utils.MyDebugHitCounter.ValueAndCycle*"></a>
  <h4 id="VRage_Utils_MyDebugHitCounter_ValueAndCycle" data-uid="VRage.Utils.MyDebugHitCounter.ValueAndCycle">ValueAndCycle()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ValueAndCycle()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
