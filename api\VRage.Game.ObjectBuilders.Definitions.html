﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.Game.ObjectBuilders.Definitions
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.Game.ObjectBuilders.Definitions
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.ObjectBuilders.Definitions">
  
  <h1 id="VRage_Game_ObjectBuilders_Definitions" data-uid="VRage.Game.ObjectBuilders.Definitions" class="text-break">Namespace VRage.Game.ObjectBuilders.Definitions
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ButtonAction.html">ButtonAction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.Condition.html">Condition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ConditionBase.html">ConditionBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ControlScheme.html">ControlScheme</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.FactionRelation.html">FactionRelation</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.FactionRelationVariant.html">FactionRelationVariant</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ItemTypes.html">ItemTypes</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyContractChancePair.html">MyContractChancePair</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyControlBindingType.html">MyControlBindingType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyFactionNameTypeEnum.html">MyFactionNameTypeEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyGuiOffset.html">MyGuiOffset</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_Action.html">MyObjectBuilder_Action</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_BlockBlueprintDefinition.html">MyObjectBuilder_BlockBlueprintDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_BlockVariantGroup.html">MyObjectBuilder_BlockVariantGroup</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_CircularProgressBarStatVisualStyle.html">MyObjectBuilder_CircularProgressBarStatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_CompositeTexture.html">MyObjectBuilder_CompositeTexture</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeDefinition.html">MyObjectBuilder_ContractTypeDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeDeliverDefinition.html">MyObjectBuilder_ContractTypeDeliverDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeEscortDefinition.html">MyObjectBuilder_ContractTypeEscortDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeFindDefinition.html">MyObjectBuilder_ContractTypeFindDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeHuntDefinition.html">MyObjectBuilder_ContractTypeHuntDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeObtainAndDeliverDefinition.html">MyObjectBuilder_ContractTypeObtainAndDeliverDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ContractTypeRepairDefinition.html">MyObjectBuilder_ContractTypeRepairDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControlBinding.html">MyObjectBuilder_ControlBinding</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControlBindingContext.html">MyObjectBuilder_ControlBindingContext</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControllerButtonAction.html">MyObjectBuilder_ControllerButtonAction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControllerSchemeDefinition.html">MyObjectBuilder_ControllerSchemeDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_CrosshairStyle.html">MyObjectBuilder_CrosshairStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DebugScreenSearchCacheDefinition.html">MyObjectBuilder_DebugScreenSearchCacheDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DlcDefinition.html">MyObjectBuilder_DlcDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DlcDefinition.PsProductIds.html">MyObjectBuilder_DlcDefinition.PsProductIds</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DPadControlVisualStyle.html">MyObjectBuilder_DPadControlVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DroneBehaviorDefinition.html">MyObjectBuilder_DroneBehaviorDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_EmoteDefinition.html">MyObjectBuilder_EmoteDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_EmoteDefinition.Animation.html">MyObjectBuilder_EmoteDefinition.Animation</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_EmoteDefinition.AnimOverrideDef.html">MyObjectBuilder_EmoteDefinition.AnimOverrideDef</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionDefinition.html">MyObjectBuilder_FactionDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionNameDefinition.html">MyObjectBuilder_FactionNameDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FactionTypeDefinition.html">MyObjectBuilder_FactionTypeDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_FakeAction.html">MyObjectBuilder_FakeAction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GameDefinition.html">MyObjectBuilder_GameDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GameDefinition.Comp.html">MyObjectBuilder_GameDefinition.Comp</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GasProperties.html">MyObjectBuilder_GasProperties</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GhostCharacterDefinition.html">MyObjectBuilder_GhostCharacterDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GpsCollectionDefinition.html">MyObjectBuilder_GpsCollectionDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GravityIndicatorVisualStyle.html">MyObjectBuilder_GravityIndicatorVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GuiTexture.html">MyObjectBuilder_GuiTexture</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GuiTextureAtlasDefinition.html">MyObjectBuilder_GuiTextureAtlasDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_HudDefinition.html">MyObjectBuilder_HudDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_HudIcon.html">MyObjectBuilder_HudIcon</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ImageStatVisualStyle.html">MyObjectBuilder_ImageStatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_KeyAction.html">MyObjectBuilder_KeyAction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_MainMenuInventorySceneDefinition.html">MyObjectBuilder_MainMenuInventorySceneDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_MouseButtonAction.html">MyObjectBuilder_MouseButtonAction</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_OffensiveWords.html">MyObjectBuilder_OffensiveWords</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_PirateAntennaDefinition.html">MyObjectBuilder_PirateAntennaDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_PirateAntennaDefinition.SpawnStrategyDefinition.html">MyObjectBuilder_PirateAntennaDefinition.SpawnStrategyDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ProgressBarCompositeTexture.html">MyObjectBuilder_ProgressBarCompositeTexture</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ProgressBarStatVisualStyle.html">MyObjectBuilder_ProgressBarStatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ProgressBarStatVisualStyle.NineTiledData.html">MyObjectBuilder_ProgressBarStatVisualStyle.NineTiledData</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ProgressBarStatVisualStyle.SimpleBarData.html">MyObjectBuilder_ProgressBarStatVisualStyle.SimpleBarData</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_QuickStartDefinition.html">MyObjectBuilder_QuickStartDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_RepairBlueprintDefinition.html">MyObjectBuilder_RepairBlueprintDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchBlockDefinition.html">MyObjectBuilder_ResearchBlockDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchDefinition.html">MyObjectBuilder_ResearchDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResearchGroupDefinition.html">MyObjectBuilder_ResearchGroupDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ResourceDistributionGroup.html">MyObjectBuilder_ResourceDistributionGroup</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SafeZoneSettingsDefinition.html">MyObjectBuilder_SafeZoneSettingsDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SafeZoneTexturesDefinition.html">MyObjectBuilder_SafeZoneTexturesDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ScenarioCategoryDefinition.html">MyObjectBuilder_ScenarioCategoryDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SchematicItemDefinition.html">MyObjectBuilder_SchematicItemDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectbuilder_SessionComponentAntiCheatDefinition.html">MyObjectbuilder_SessionComponentAntiCheatDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SessionComponentContractSystemDefinition.html">MyObjectBuilder_SessionComponentContractSystemDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SessionComponentDefinition.html">MyObjectBuilder_SessionComponentDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectbuilder_SessionComponentEconomyDefinition.html">MyObjectbuilder_SessionComponentEconomyDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SessionComponentResearchDefinition.html">MyObjectBuilder_SessionComponentResearchDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_SessionComponentSmartUpdaterDefinition.html">MyObjectBuilder_SessionComponentSmartUpdaterDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.html">MyObjectBuilder_ShipSoundsDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundSystemDefinition.html">MyObjectBuilder_ShipSoundSystemDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StatControls.html">MyObjectBuilder_StatControls</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StationsListDefinition.html">MyObjectBuilder_StationsListDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StatVisualStyle.html">MyObjectBuilder_StatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StoreItem.html">MyObjectBuilder_StoreItem</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_TargetingMarkersStyle.html">MyObjectBuilder_TargetingMarkersStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_TargetingProgressBarStatVisualStyle.html">MyObjectBuilder_TargetingProgressBarStatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_TextStatVisualStyle.html">MyObjectBuilder_TextStatVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ToolbarControlVisualStyle.html">MyObjectBuilder_ToolbarControlVisualStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ToolbarControlVisualStyle.ColorStyle.html">MyObjectBuilder_ToolbarControlVisualStyle.ColorStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ToolbarControlVisualStyle.ToolbarItemStyle.html">MyObjectBuilder_ToolbarControlVisualStyle.ToolbarItemStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ToolbarControlVisualStyle.ToolbarPageStyle.html">MyObjectBuilder_ToolbarControlVisualStyle.ToolbarPageStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_UpgradeModuleDefinition.html">MyObjectBuilder_UpgradeModuleDefinition</a></h4>
      <section><p>Upgrade module base definition</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_UsableItemDefinition.html">MyObjectBuilder_UsableItemDefinition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyPredefinedContitions.html">MyPredefinedContitions</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyUpgradeModifierType.html">MyUpgradeModifierType</a></h4>
      <section><p>Upgrade modifier type</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyUpgradeModuleInfo.html">MyUpgradeModuleInfo</a></h4>
      <section><p>Module upgrade information</p>
</section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyWeaponBehavior.html">MyWeaponBehavior</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyWeaponRule.html">MyWeaponRule</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSound.html">ShipSound</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSoundVolumePair.html">ShipSoundVolumePair</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSystemSoundsEnum.html">ShipSystemSoundsEnum</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.StatCondition.html">StatCondition</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.StatConditionOperator.html">StatConditionOperator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.StatLogicOperator.html">StatLogicOperator</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.StoreItemTypes.html">StoreItemTypes</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.VisualStyleCategory.html">VisualStyleCategory</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
