﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace VRage.ObjectBuilders
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace VRage.ObjectBuilders
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders">
  
  <h1 id="VRage_ObjectBuilders" data-uid="VRage.ObjectBuilders" class="text-break">Namespace VRage.ObjectBuilders
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
  </h3>
      <h4><a class="xref" href="VRage.ObjectBuilders.DynamicNullableObjectBuilderItemAttribute.html">DynamicNullableObjectBuilderItemAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.DynamicObjectBuilderAttribute.html">DynamicObjectBuilderAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.DynamicObjectBuilderItemAttribute.html">DynamicObjectBuilderItemAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_ReplicableEntity.html">MyObjectBuilder_ReplicableEntity</a></h4>
      <section><p>This object builder is old and is for &quot;MyInventoryBagEntity&quot;. Do not use it as base class or for anything. It is here only for backward compatibility.</p>
</section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderDefinitionAttribute.html">MyObjectBuilderDefinitionAttribute</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderDynamicSerializer.html">MyObjectBuilderDynamicSerializer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderSerializer.html">MyObjectBuilderSerializer</a></h4>
      <section><p>Mod API</p>
</section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.ComparerType.html">MyObjectBuilderType.ComparerType</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyObjectFactory-2.html">MyObjectFactory&lt;TAttribute, TCreatedObjectBase&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyPersistentEntityFlags2.html">MyPersistentEntityFlags2</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyRuntimeObjectBuilderId.html">MyRuntimeObjectBuilderId</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.MyRuntimeObjectBuilderIdComparer.html">MyRuntimeObjectBuilderIdComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="VRage.ObjectBuilders.SerializableDefinitionId.html">SerializableDefinitionId</a></h4>
      <section></section>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
