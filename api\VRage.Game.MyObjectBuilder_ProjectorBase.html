﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_ProjectorBase
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_ProjectorBase
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_ProjectorBase" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase" class="text-break">Class MyObjectBuilder_ProjectorBase
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html">MyObjectBuilder_CubeBlock</a></div>
    <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html">MyObjectBuilder_TerminalBlock</a></div>
    <div class="level4"><a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlock.html">MyObjectBuilder_FunctionalBlock</a></div>
    <div class="level5"><span class="xref">MyObjectBuilder_ProjectorBase</span></div>
      <div class="level6"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Projector.html">MyObjectBuilder_Projector</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlock.html#VRage_Game_MyObjectBuilder_FunctionalBlock_Enabled">MyObjectBuilder_FunctionalBlock.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlock.html#VRage_Game_MyObjectBuilder_FunctionalBlock_TextPanelsNew">MyObjectBuilder_FunctionalBlock.TextPanelsNew</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_CustomName">MyObjectBuilder_TerminalBlock.CustomName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_ShowOnHUD">MyObjectBuilder_TerminalBlock.ShowOnHUD</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_ShowInTerminal">MyObjectBuilder_TerminalBlock.ShowInTerminal</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_ShowInToolbarConfig">MyObjectBuilder_TerminalBlock.ShowInToolbarConfig</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_ShowInInventory">MyObjectBuilder_TerminalBlock.ShowInInventory</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_NumberInGrid">MyObjectBuilder_TerminalBlock.NumberInGrid</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html#VRage_Game_MyObjectBuilder_TerminalBlock_IsSurvivalModeForced">MyObjectBuilder_TerminalBlock.IsSurvivalModeForced</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_EntityId">MyObjectBuilder_CubeBlock.EntityId</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Name">MyObjectBuilder_CubeBlock.Name</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Min">MyObjectBuilder_CubeBlock.Min</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_IntegrityPercent">MyObjectBuilder_CubeBlock.IntegrityPercent</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_BuildPercent">MyObjectBuilder_CubeBlock.BuildPercent</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_BlockOrientation">MyObjectBuilder_CubeBlock.BlockOrientation</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ConstructionInventory">MyObjectBuilder_CubeBlock.ConstructionInventory</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ColorMaskHSV">MyObjectBuilder_CubeBlock.ColorMaskHSV</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_SkinSubtypeId">MyObjectBuilder_CubeBlock.SkinSubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ConstructionStockpile">MyObjectBuilder_CubeBlock.ConstructionStockpile</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Owner">MyObjectBuilder_CubeBlock.Owner</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_BuiltBy">MyObjectBuilder_CubeBlock.BuiltBy</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShareMode">MyObjectBuilder_CubeBlock.ShareMode</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_DeformationRatio">MyObjectBuilder_CubeBlock.DeformationRatio</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_SubBlocks">MyObjectBuilder_CubeBlock.SubBlocks</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockId">MyObjectBuilder_CubeBlock.MultiBlockId</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockDefinition">MyObjectBuilder_CubeBlock.MultiBlockDefinition</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockIndex">MyObjectBuilder_CubeBlock.MultiBlockIndex</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_BlockGeneralDamageModifier">MyObjectBuilder_CubeBlock.BlockGeneralDamageModifier</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ComponentContainer">MyObjectBuilder_CubeBlock.ComponentContainer</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeEntityId">MyObjectBuilder_CubeBlock.ShouldSerializeEntityId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMin">MyObjectBuilder_CubeBlock.ShouldSerializeMin()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeOrientation">MyObjectBuilder_CubeBlock.ShouldSerializeOrientation()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeBlockOrientation">MyObjectBuilder_CubeBlock.ShouldSerializeBlockOrientation()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionInventory">MyObjectBuilder_CubeBlock.ShouldSerializeConstructionInventory()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeColorMaskHSV">MyObjectBuilder_CubeBlock.ShouldSerializeColorMaskHSV()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeSkinSubtypeId">MyObjectBuilder_CubeBlock.ShouldSerializeSkinSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Upgrade_VRage_Game_MyObjectBuilder_CubeBlock_VRage_ObjectBuilders_MyObjectBuilderType_System_String_">MyObjectBuilder_CubeBlock.Upgrade(MyObjectBuilder_CubeBlock, MyObjectBuilderType, String)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionStockpile">MyObjectBuilder_CubeBlock.ShouldSerializeConstructionStockpile()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockId">MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockDefinition">MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockDefinition()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeComponentContainer">MyObjectBuilder_CubeBlock.ShouldSerializeComponentContainer()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_SetupForGridPaste">MyObjectBuilder_CubeBlock.SetupForGridPaste()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Orientation">MyObjectBuilder_CubeBlock.Orientation</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_ProjectorBase_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public abstract class MyObjectBuilder_ProjectorBase : MyObjectBuilder_FunctionalBlock</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_ProjectorBase__ctor_" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase__ctor" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.#ctor">MyObjectBuilder_ProjectorBase()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected MyObjectBuilder_ProjectorBase()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_GetOwnershipFromProjector" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.GetOwnershipFromProjector">GetOwnershipFromProjector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool GetOwnershipFromProjector</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_InstantBuildingEnabled" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.InstantBuildingEnabled">InstantBuildingEnabled</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool InstantBuildingEnabled</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_KeepProjection" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.KeepProjection">KeepProjection</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool KeepProjection</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_MarkMissingBlocks" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.MarkMissingBlocks">MarkMissingBlocks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool MarkMissingBlocks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_MarkUnfinishedBlocks" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.MarkUnfinishedBlocks">MarkUnfinishedBlocks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool MarkUnfinishedBlocks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_MaxNumberOfBlocks" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.MaxNumberOfBlocks">MaxNumberOfBlocks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int MaxNumberOfBlocks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_MaxNumberOfProjections" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.MaxNumberOfProjections">MaxNumberOfProjections</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int MaxNumberOfProjections</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ProjectedGrid" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ProjectedGrid">ProjectedGrid</h4>
  <div class="markdown level1 summary"><p>This property is obsolete. Do not use it any more. Use Projected grids. In case of single projected grid, select index 0.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public MyObjectBuilder_CubeGrid ProjectedGrid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_CubeGrid.html">MyObjectBuilder_CubeGrid</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ProjectedGrids" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ProjectedGrids">ProjectedGrids</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public List&lt;MyObjectBuilder_CubeGrid&gt; ProjectedGrids</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.MyObjectBuilder_CubeGrid.html">MyObjectBuilder_CubeGrid</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ProjectionOffset" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ProjectionOffset">ProjectionOffset</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I ProjectionOffset</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ProjectionRotation" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ProjectionRotation">ProjectionRotation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I ProjectionRotation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ProjectionsRemaining" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ProjectionsRemaining">ProjectionsRemaining</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ProjectionsRemaining</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_Scale" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.Scale">Scale</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Scale</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_ShowOnlyBuildable" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.ShowOnlyBuildable">ShowOnlyBuildable</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShowOnlyBuildable</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_TextPanels" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.TextPanels">TextPanels</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public List&lt;MySerializedTextPanelData&gt; TextPanels</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.MySerializedTextPanelData.html">MySerializedTextPanelData</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_ProjectorBase_GetTextPanelsData_" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.GetTextPanelsData*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_GetTextPanelsData" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.GetTextPanelsData">GetTextPanelsData()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override List&lt;MySerializedTextPanelData&gt; GetTextPanelsData()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.MySerializedTextPanelData.html">MySerializedTextPanelData</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyObjectBuilder_FunctionalBlock.html#VRage_Game_MyObjectBuilder_FunctionalBlock_GetTextPanelsData">MyObjectBuilder_FunctionalBlock.GetTextPanelsData()</a></div>
  
  
  <a id="VRage_Game_MyObjectBuilder_ProjectorBase_Remap_" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.Remap*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_Remap_VRage_ModAPI_IMyRemapHelper_" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.Remap(VRage.ModAPI.IMyRemapHelper)">Remap(IMyRemapHelper)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Remap(IMyRemapHelper remapHelper)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyRemapHelper.html">IMyRemapHelper</a></td>
        <td><span class="parametername">remapHelper</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_Remap_VRage_ModAPI_IMyRemapHelper_">MyObjectBuilder_CubeBlock.Remap(IMyRemapHelper)</a></div>
  
  
  <a id="VRage_Game_MyObjectBuilder_ProjectorBase_SetupForProjector_" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.SetupForProjector*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_ProjectorBase_SetupForProjector" data-uid="VRage.Game.MyObjectBuilder_ProjectorBase.SetupForProjector">SetupForProjector()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void SetupForProjector()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html#VRage_Game_MyObjectBuilder_CubeBlock_SetupForProjector">MyObjectBuilder_CubeBlock.SetupForProjector()</a></div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
