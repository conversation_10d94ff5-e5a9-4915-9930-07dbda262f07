﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyStorageDataProvider
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyStorageDataProvider
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Voxels.IMyStorageDataProvider">
  
  
  <h1 id="VRage_Voxels_IMyStorageDataProvider" data-uid="VRage.Voxels.IMyStorageDataProvider" class="text-break">Interface IMyStorageDataProvider
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Voxels.html">VRage.Voxels</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Voxels_IMyStorageDataProvider_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyStorageDataProvider</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_SerializedSize_" data-uid="VRage.Voxels.IMyStorageDataProvider.SerializedSize*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_SerializedSize" data-uid="VRage.Voxels.IMyStorageDataProvider.SerializedSize">SerializedSize</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int SerializedSize { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_Close_" data-uid="VRage.Voxels.IMyStorageDataProvider.Close*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_Close" data-uid="VRage.Voxels.IMyStorageDataProvider.Close">Close()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Close()</code></pre>
  </div>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_DebugDraw_" data-uid="VRage.Voxels.IMyStorageDataProvider.DebugDraw*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_DebugDraw_VRageMath_MatrixD__" data-uid="VRage.Voxels.IMyStorageDataProvider.DebugDraw(VRageMath.MatrixD@)">DebugDraw(ref MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void DebugDraw(ref MatrixD worldMatrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_Intersect_" data-uid="VRage.Voxels.IMyStorageDataProvider.Intersect*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_Intersect_VRageMath_BoundingBoxI_System_Int32_" data-uid="VRage.Voxels.IMyStorageDataProvider.Intersect(VRageMath.BoundingBoxI,System.Int32)">Intersect(BoundingBoxI, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">ContainmentType Intersect(BoundingBoxI box, int lod)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxI.html">BoundingBoxI</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lod</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_Intersect_" data-uid="VRage.Voxels.IMyStorageDataProvider.Intersect*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_Intersect_VRageMath_LineD__System_Double__System_Double__" data-uid="VRage.Voxels.IMyStorageDataProvider.Intersect(VRageMath.LineD@,System.Double@,System.Double@)">Intersect(ref LineD, out Double, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Intersect(ref LineD line, out double startOffset, out double endOffset)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">startOffset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">endOffset</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_PostProcess_" data-uid="VRage.Voxels.IMyStorageDataProvider.PostProcess*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_PostProcess_VRage_Voxels_VrVoxelMesh_VRage_Voxels_MyStorageDataTypeFlags_" data-uid="VRage.Voxels.IMyStorageDataProvider.PostProcess(VRage.Voxels.VrVoxelMesh,VRage.Voxels.MyStorageDataTypeFlags)">PostProcess(VrVoxelMesh, MyStorageDataTypeFlags)</h4>
  <div class="markdown level1 summary"><p>Post-process the mesh generated from the data in this storage.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void PostProcess(VrVoxelMesh mesh, MyStorageDataTypeFlags dataTypes)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Voxels.VrVoxelMesh</span></td>
        <td><span class="parametername">mesh</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataTypes</span></td>
        <td><p>The types of data requested for the mesh.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_ReadFrom_" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadFrom*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_ReadFrom_System_Int32_System_IO_Stream_System_Int32_System_Boolean__" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadFrom(System.Int32,System.IO.Stream,System.Int32,System.Boolean@)">ReadFrom(Int32, Stream, Int32, ref Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReadFrom(int storageVersion, Stream stream, int size, ref bool isOldFormat)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">storageVersion</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.IO.Stream</span></td>
        <td><span class="parametername">stream</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">isOldFormat</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_ReadRange_" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadRange*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_ReadRange_VRage_Voxels_MyStorageData_VRage_Voxels_MyStorageDataTypeFlags_VRageMath_Vector3I__System_Int32_VRageMath_Vector3I__VRageMath_Vector3I__" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadRange(VRage.Voxels.MyStorageData,VRage.Voxels.MyStorageDataTypeFlags,VRageMath.Vector3I@,System.Int32,VRageMath.Vector3I@,VRageMath.Vector3I@)">ReadRange(MyStorageData, MyStorageDataTypeFlags, ref Vector3I, Int32, ref Vector3I, ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReadRange(MyStorageData target, MyStorageDataTypeFlags dataType, ref Vector3I writeOffset, int lodIndex, ref Vector3I minInLod, ref Vector3I maxInLod)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageData.html">MyStorageData</a></td>
        <td><span class="parametername">target</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyStorageDataTypeFlags.html">MyStorageDataTypeFlags</a></td>
        <td><span class="parametername">dataType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">writeOffset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">lodIndex</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">minInLod</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">maxInLod</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_ReadRange_" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadRange*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_ReadRange_VRage_Voxels_MyVoxelDataRequest__System_Boolean_" data-uid="VRage.Voxels.IMyStorageDataProvider.ReadRange(VRage.Voxels.MyVoxelDataRequest@,System.Boolean)">ReadRange(ref MyVoxelDataRequest, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReadRange(ref MyVoxelDataRequest request, bool detectOnly = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Voxels.MyVoxelDataRequest.html">MyVoxelDataRequest</a></td>
        <td><span class="parametername">request</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">detectOnly</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_ReindexMaterials_" data-uid="VRage.Voxels.IMyStorageDataProvider.ReindexMaterials*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_ReindexMaterials_System_Collections_Generic_Dictionary_System_Byte_System_Byte__" data-uid="VRage.Voxels.IMyStorageDataProvider.ReindexMaterials(System.Collections.Generic.Dictionary{System.Byte,System.Byte})">ReindexMaterials(Dictionary&lt;Byte, Byte&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void ReindexMaterials(Dictionary&lt;byte, byte&gt; oldToNewIndexMap)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.Dictionary</span>&lt;<span class="xref">System.Byte</span>, <span class="xref">System.Byte</span>&gt;</td>
        <td><span class="parametername">oldToNewIndexMap</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Voxels_IMyStorageDataProvider_WriteTo_" data-uid="VRage.Voxels.IMyStorageDataProvider.WriteTo*"></a>
  <h4 id="VRage_Voxels_IMyStorageDataProvider_WriteTo_System_IO_Stream_" data-uid="VRage.Voxels.IMyStorageDataProvider.WriteTo(System.IO.Stream)">WriteTo(Stream)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void WriteTo(Stream stream)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.IO.Stream</span></td>
        <td><span class="parametername">stream</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
