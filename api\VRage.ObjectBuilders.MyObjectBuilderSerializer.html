﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilderSerializer
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilderSerializer
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer">
  
  
  <h1 id="VRage_ObjectBuilders_MyObjectBuilderSerializer" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer" class="text-break">Class MyObjectBuilderSerializer
  </h1>
  <div class="markdown level0 summary"><p>Mod API</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectBuilderSerializer</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ObjectBuilders.html">VRage.ObjectBuilders</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyObjectBuilderSerializer : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer__ctor_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.#ctor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer__ctor" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.#ctor">MyObjectBuilderSerializer()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilderSerializer()</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_Clone_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.Clone*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_Clone_VRage_ObjectBuilders_MyObjectBuilder_Base_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.Clone(VRage.ObjectBuilders.MyObjectBuilder_Base)">Clone(MyObjectBuilder_Base)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilder_Base Clone(MyObjectBuilder_Base toClone)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">toClone</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_VRage_ObjectBuilders_MyObjectBuilderType_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject(VRage.ObjectBuilders.MyObjectBuilderType)">CreateNewObject(MyObjectBuilderType)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilder_Base CreateNewObject(MyObjectBuilderType type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_VRage_ObjectBuilders_MyObjectBuilderType_System_String_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject(VRage.ObjectBuilders.MyObjectBuilderType,System.String)">CreateNewObject(MyObjectBuilderType, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilder_Base CreateNewObject(MyObjectBuilderType type, string subtypeName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">subtypeName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_VRage_ObjectBuilders_SerializableDefinitionId_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject(VRage.ObjectBuilders.SerializableDefinitionId)">CreateNewObject(SerializableDefinitionId)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilder_Base CreateNewObject(SerializableDefinitionId id)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.SerializableDefinitionId.html">SerializableDefinitionId</a></td>
        <td><span class="parametername">id</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject__1" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject``1">CreateNewObject&lt;T&gt;()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T CreateNewObject&lt;T&gt;()
    where T : MyObjectBuilder_Base, new()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_CreateNewObject__1_System_String_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.CreateNewObject``1(System.String)">CreateNewObject&lt;T&gt;(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T CreateNewObject&lt;T&gt;(string subtypeName)
    where T : MyObjectBuilder_Base, new()</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">subtypeName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB__1_System_Byte_____0__System_UInt64__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB``1(System.Byte[],``0@,System.UInt64@)">DeserializePB&lt;T&gt;(Byte[], out T, out UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializePB&lt;T&gt;(byte[] data, out T objectBuilder, out ulong fileSize)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td><span class="parametername">data</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">fileSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB__1_System_String___0__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB``1(System.String,``0@)">DeserializePB&lt;T&gt;(String, out T)</h4>
  <div class="markdown level1 summary"><p>Deserialize data by protobuf</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializePB&lt;T&gt;(string path, out T objectBuilder)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td><p>path</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>instance</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>false when fails</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p>object builder type</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializePB__1_System_String___0__System_UInt64__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializePB``1(System.String,``0@,System.UInt64@)">DeserializePB&lt;T&gt;(String, out T, out UInt64)</h4>
  <div class="markdown level1 summary"><p>Deserialize data by protobuf</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializePB&lt;T&gt;(string path, out T objectBuilder, out ulong fileSize)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td><p>path</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>instance</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">fileSize</span></td>
        <td><p>size of the file</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>false when fails</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p>object builder type</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML_System_String_VRage_ObjectBuilders_MyObjectBuilder_Base__System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML(System.String,VRage.ObjectBuilders.MyObjectBuilder_Base@,System.Type)">DeserializeXML(String, out MyObjectBuilder_Base, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializeXML(string path, out MyObjectBuilder_Base objectBuilder, Type builderType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">builderType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML__1_System_Byte_____0__System_UInt64__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML``1(System.Byte[],``0@,System.UInt64@)">DeserializeXML&lt;T&gt;(Byte[], out T, out UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializeXML&lt;T&gt;(byte[] xmlData, out T objectBuilder, out ulong fileSize)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span>[]</td>
        <td><span class="parametername">xmlData</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">fileSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML__1_System_String___0__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML``1(System.String,``0@)">DeserializeXML&lt;T&gt;(String, out T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializeXML&lt;T&gt;(string path, out T objectBuilder)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_DeserializeXML__1_System_String___0__System_UInt64__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.DeserializeXML``1(System.String,``0@,System.UInt64@)">DeserializeXML&lt;T&gt;(String, out T, out UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool DeserializeXML&lt;T&gt;(string path, out T objectBuilder, out ulong fileSize)
    where T : MyObjectBuilder_Base</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">fileSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializePB_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializePB*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializePB_System_String_System_Boolean_VRage_ObjectBuilders_MyObjectBuilder_Base_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializePB(System.String,System.Boolean,VRage.ObjectBuilders.MyObjectBuilder_Base)">SerializePB(String, Boolean, MyObjectBuilder_Base)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool SerializePB(string path, bool compress, MyObjectBuilder_Base objectBuilder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">compress</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializePB_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializePB*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializePB_System_String_System_Boolean_VRage_ObjectBuilders_MyObjectBuilder_Base_System_UInt64__" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializePB(System.String,System.Boolean,VRage.ObjectBuilders.MyObjectBuilder_Base,System.UInt64@)">SerializePB(String, Boolean, MyObjectBuilder_Base, out UInt64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool SerializePB(string path, bool compress, MyObjectBuilder_Base objectBuilder, out ulong sizeInBytes)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">compress</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">sizeInBytes</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializeXML_System_String_System_Boolean_VRage_ObjectBuilders_MyObjectBuilder_Base_System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializeXML(System.String,System.Boolean,VRage.ObjectBuilders.MyObjectBuilder_Base,System.Type)">SerializeXML(String, Boolean, MyObjectBuilder_Base, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool SerializeXML(string path, bool compress, MyObjectBuilder_Base objectBuilder, Type serializeAsType = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">compress</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">serializeAsType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializeXML_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializeXML*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilderSerializer_SerializeXML_System_String_System_Boolean_VRage_ObjectBuilders_MyObjectBuilder_Base_System_UInt64__System_Type_" data-uid="VRage.ObjectBuilders.MyObjectBuilderSerializer.SerializeXML(System.String,System.Boolean,VRage.ObjectBuilders.MyObjectBuilder_Base,System.UInt64@,System.Type)">SerializeXML(String, Boolean, MyObjectBuilder_Base, out UInt64, Type)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool SerializeXML(string path, bool compress, MyObjectBuilder_Base objectBuilder, out ulong sizeInBytes, Type serializeAsType = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">path</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">compress</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt64</span></td>
        <td><span class="parametername">sizeInBytes</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">serializeAsType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
