﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class CurveKey
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class CurveKey
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.CurveKey">
  
  
  <h1 id="VRageMath_CurveKey" data-uid="VRageMath.CurveKey" class="text-break">Class CurveKey
  </h1>
  <div class="markdown level0 summary"><p>Represents a point in a multi-point curve.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">CurveKey</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_CurveKey_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class CurveKey : Object, IEquatable&lt;CurveKey&gt;, IComparable&lt;CurveKey&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_CurveKey__ctor_" data-uid="VRageMath.CurveKey.#ctor*"></a>
  <h4 id="VRageMath_CurveKey__ctor" data-uid="VRageMath.CurveKey.#ctor">CurveKey()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey()</code></pre>
  </div>
  
  
  <a id="VRageMath_CurveKey__ctor_" data-uid="VRageMath.CurveKey.#ctor*"></a>
  <h4 id="VRageMath_CurveKey__ctor_System_Single_System_Single_" data-uid="VRageMath.CurveKey.#ctor(System.Single,System.Single)">CurveKey(Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey(float position, float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position in the curve.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value of the control point.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey__ctor_" data-uid="VRageMath.CurveKey.#ctor*"></a>
  <h4 id="VRageMath_CurveKey__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.CurveKey.#ctor(System.Single,System.Single,System.Single,System.Single)">CurveKey(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey(float position, float value, float tangentIn, float tangentOut)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position in the curve.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value of the control point.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tangentIn</span></td>
        <td><p>Tangent approaching point from the previous point in the curve.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tangentOut</span></td>
        <td><p>Tangent leaving point toward next point in the curve.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey__ctor_" data-uid="VRageMath.CurveKey.#ctor*"></a>
  <h4 id="VRageMath_CurveKey__ctor_System_Single_System_Single_System_Single_System_Single_VRageMath_CurveContinuity_" data-uid="VRageMath.CurveKey.#ctor(System.Single,System.Single,System.Single,System.Single,VRageMath.CurveContinuity)">CurveKey(Single, Single, Single, Single, CurveContinuity)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey(float position, float value, float tangentIn, float tangentOut, CurveContinuity continuity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>Position in the curve.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value of the control point.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tangentIn</span></td>
        <td><p>Tangent approaching point from the previous point in the curve.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tangentOut</span></td>
        <td><p>Tangent leaving point toward next point in the curve.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveContinuity.html">CurveContinuity</a></td>
        <td><span class="parametername">continuity</span></td>
        <td><p>Enum indicating whether the curve is discrete or continuous.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_CurveKey_Continuity_" data-uid="VRageMath.CurveKey.Continuity*"></a>
  <h4 id="VRageMath_CurveKey_Continuity" data-uid="VRageMath.CurveKey.Continuity">Continuity</h4>
  <div class="markdown level1 summary"><p>Describes whether the segment between this point and the next point in the curve is discrete or continuous.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveContinuity Continuity { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveContinuity.html">CurveContinuity</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_Position_" data-uid="VRageMath.CurveKey.Position*"></a>
  <h4 id="VRageMath_CurveKey_Position" data-uid="VRageMath.CurveKey.Position">Position</h4>
  <div class="markdown level1 summary"><p>Position of the CurveKey in the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Position { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_TangentIn_" data-uid="VRageMath.CurveKey.TangentIn*"></a>
  <h4 id="VRageMath_CurveKey_TangentIn" data-uid="VRageMath.CurveKey.TangentIn">TangentIn</h4>
  <div class="markdown level1 summary"><p>Describes the tangent when approaching this point from the previous point in the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float TangentIn { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_TangentOut_" data-uid="VRageMath.CurveKey.TangentOut*"></a>
  <h4 id="VRageMath_CurveKey_TangentOut" data-uid="VRageMath.CurveKey.TangentOut">TangentOut</h4>
  <div class="markdown level1 summary"><p>Describes the tangent when leaving this point to the next point in the curve.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float TangentOut { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_Value_" data-uid="VRageMath.CurveKey.Value*"></a>
  <h4 id="VRageMath_CurveKey_Value" data-uid="VRageMath.CurveKey.Value">Value</h4>
  <div class="markdown level1 summary"><p>Describes the value of this point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Value { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_CurveKey_Clone_" data-uid="VRageMath.CurveKey.Clone*"></a>
  <h4 id="VRageMath_CurveKey_Clone" data-uid="VRageMath.CurveKey.Clone">Clone()</h4>
  <div class="markdown level1 summary"><p>Creates a copy of the CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public CurveKey Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_CompareTo_" data-uid="VRageMath.CurveKey.CompareTo*"></a>
  <h4 id="VRageMath_CurveKey_CompareTo_VRageMath_CurveKey_" data-uid="VRageMath.CurveKey.CompareTo(VRageMath.CurveKey)">CompareTo(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Compares this instance to another CurveKey and returns an indication of their relative values.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int CompareTo(CurveKey other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>CurveKey to compare to.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_Equals_" data-uid="VRageMath.CurveKey.Equals*"></a>
  <h4 id="VRageMath_CurveKey_Equals_System_Object_" data-uid="VRageMath.CurveKey.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_Equals_" data-uid="VRageMath.CurveKey.Equals*"></a>
  <h4 id="VRageMath_CurveKey_Equals_VRageMath_CurveKey_" data-uid="VRageMath.CurveKey.Equals(VRageMath.CurveKey)">Equals(CurveKey)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the CurveKey.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(CurveKey other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current CurveKey.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_GetHashCode_" data-uid="VRageMath.CurveKey.GetHashCode*"></a>
  <h4 id="VRageMath_CurveKey_GetHashCode" data-uid="VRageMath.CurveKey.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Returns the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_CurveKey_op_Equality_" data-uid="VRageMath.CurveKey.op_Equality*"></a>
  <h4 id="VRageMath_CurveKey_op_Equality_VRageMath_CurveKey_VRageMath_CurveKey_" data-uid="VRageMath.CurveKey.op_Equality(VRageMath.CurveKey,VRageMath.CurveKey)">Equality(CurveKey, CurveKey)</h4>
  <div class="markdown level1 summary"><p>Determines whether two CurveKey instances are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(CurveKey a, CurveKey b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>CurveKey on the left of the equal sign.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>CurveKey on the right of the equal sign.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_CurveKey_op_Inequality_" data-uid="VRageMath.CurveKey.op_Inequality*"></a>
  <h4 id="VRageMath_CurveKey_op_Inequality_VRageMath_CurveKey_VRageMath_CurveKey_" data-uid="VRageMath.CurveKey.op_Inequality(VRageMath.CurveKey,VRageMath.CurveKey)">Inequality(CurveKey, CurveKey)</h4>
  <div class="markdown level1 summary"><p>Determines whether two CurveKey instances are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(CurveKey a, CurveKey b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>CurveKey on the left of the equal sign.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.CurveKey.html">CurveKey</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>CurveKey on the right of the equal sign.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
