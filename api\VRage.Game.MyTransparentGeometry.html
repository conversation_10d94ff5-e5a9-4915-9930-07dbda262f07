﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyTransparentGeometry
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyTransparentGeometry
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyTransparentGeometry">
  
  
  <h1 id="VRage_Game_MyTransparentGeometry" data-uid="VRage.Game.MyTransparentGeometry" class="text-break">Class MyTransparentGeometry
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyTransparentGeometry</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyTransparentGeometry_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyTransparentGeometry : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyTransparentGeometry__ctor_" data-uid="VRage.Game.MyTransparentGeometry.#ctor*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry__ctor" data-uid="VRage.Game.MyTransparentGeometry.#ctor">MyTransparentGeometry()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyTransparentGeometry()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyTransparentGeometry_Camera_" data-uid="VRage.Game.MyTransparentGeometry.Camera*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_Camera" data-uid="VRage.Game.MyTransparentGeometry.Camera">Camera</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD Camera { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_CameraView_" data-uid="VRage.Game.MyTransparentGeometry.CameraView*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_CameraView" data-uid="VRage.Game.MyTransparentGeometry.CameraView">CameraView</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD CameraView { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_HasCamera_" data-uid="VRage.Game.MyTransparentGeometry.HasCamera*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_HasCamera" data-uid="VRage.Game.MyTransparentGeometry.HasCamera">HasCamera</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool HasCamera { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddAttachedQuad_" data-uid="VRage.Game.MyTransparentGeometry.AddAttachedQuad*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddAttachedQuad_VRage_Utils_MyStringId_VRageMath_MyQuadD__VRageMath_Vector4_VRageMath_Vector3D__System_UInt32_VRageRender_MyBillboard_BlendTypeEnum_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddAttachedQuad(VRage.Utils.MyStringId,VRageMath.MyQuadD@,VRageMath.Vector4,VRageMath.Vector3D@,System.UInt32,VRageRender.MyBillboard.BlendTypeEnum,System.Collections.Generic.List{VRageRender.MyBillboard})">AddAttachedQuad(MyStringId, ref MyQuadD, Vector4, ref Vector3D, UInt32, MyBillboard.BlendTypeEnum, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool AddAttachedQuad(MyStringId material, ref MyQuadD quad, Vector4 color, ref Vector3D vctPos, uint renderObjectID, MyBillboard.BlendTypeEnum blendType, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vctPos</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboard_VRageRender_MyBillboard_System_Boolean_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboard(VRageRender.MyBillboard,System.Boolean)">AddBillboard(MyBillboard, Boolean)</h4>
  <div class="markdown level1 summary"><p>Adds billboard to render.
Remember, that added persistent billboard you can change ONLY with <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_VRageRender_MyBillboard__">ApplyActionOnPersistentBillboards(Action&lt;MyBillboard&gt;)</a> and <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_">ApplyActionOnPersistentBillboards(Action)</a> methods
FOR MODDERS: If you have more than 100 billboards to remove you should use another <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_AddBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_">AddBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</a> function</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboard(MyBillboard billboard, bool isPersistent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard</span></td>
        <td><span class="parametername">billboard</span></td>
        <td><p>Billboard to render</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">isPersistent</span></td>
        <td><p>When true - billboard will continue rendering until <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_RemovePersistentBillboard_VRageRender_MyBillboard_System_Boolean_">RemovePersistentBillboard(MyBillboard, Boolean)</a> or <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_RemovePersistentBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_">RemovePersistentBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</a> is called</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_System_Single_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,System.Single,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Collections.Generic.List{VRageRender.MyBillboard})">AddBillboardOriented(MyStringId, Vector4, Vector3D, Vector3, Vector3, Single, Int32, MyBillboard.BlendTypeEnum, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboardOriented(MyStringId material, Vector4 color, Vector3D origin, Vector3 leftVector, Vector3 upVector, float radius, int customProjection, MyBillboard.BlendTypeEnum blendType, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_System_Single_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,System.Single,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddBillboardOriented(MyStringId, Vector4, Vector3D, Vector3, Vector3, Single, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboardOriented(MyStringId material, Vector4 color, Vector3D origin, Vector3 leftVector, Vector3 upVector, float width, float height, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_System_Single_System_Single_VRageMath_Vector2_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,System.Single,System.Single,VRageMath.Vector2,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddBillboardOriented(MyStringId, Vector4, Vector3D, Vector3, Vector3, Single, Single, Vector2, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboardOriented(MyStringId material, Vector4 color, Vector3D origin, Vector3 leftVector, Vector3 upVector, float width, float height, Vector2 uvOffset, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float reflection = 0F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">width</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uvOffset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">reflection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboardOriented_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_System_Single_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOriented(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,System.Single,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddBillboardOriented(MyStringId, Vector4, Vector3D, Vector3, Vector3, Single, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboardOriented(MyStringId material, Vector4 color, Vector3D origin, Vector3 leftVector, Vector3 upVector, float radius, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float reflection = 0F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">reflection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboardOrientedCull_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOrientedCull*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboardOrientedCull_VRageMath_Vector3_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Single_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddBillboardOrientedCull(VRageMath.Vector3,VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Single,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddBillboardOrientedCull(Vector3, MyStringId, Vector4, Vector3, Vector3, Vector3, Single, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboardOrientedCull(Vector3 cameraPos, MyStringId material, Vector4 color, Vector3 origin, Vector3 leftVector, Vector3 upVector, float radius, int customViewProjection = -1, float reflection = 0F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">cameraPos</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">leftVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">upVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">reflection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddBillboards_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboards*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_" data-uid="VRage.Game.MyTransparentGeometry.AddBillboards(System.Collections.Generic.IEnumerable{VRageRender.MyBillboard},System.Boolean)">AddBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</h4>
  <div class="markdown level1 summary"><p>Adds billboards to render.
Remember, that added persistent billboards you can change ONLY with <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_VRageRender_MyBillboard__">ApplyActionOnPersistentBillboards(Action&lt;MyBillboard&gt;)</a> and <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_">ApplyActionOnPersistentBillboards(Action)</a> methods</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddBillboards(IEnumerable&lt;MyBillboard&gt; billboards, bool isPersistent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">billboards</span></td>
        <td><p>Billboards to render</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">isPersistent</span></td>
        <td><p>When true - billboard will continue rendering until <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_RemovePersistentBillboard_VRageRender_MyBillboard_System_Boolean_">RemovePersistentBillboard(MyBillboard, Boolean)</a> or <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_RemovePersistentBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_">RemovePersistentBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</a> is called</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddLineBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddLineBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddLineBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_System_UInt32_VRageMath_MatrixD__VRageMath_Vector3_System_Single_System_Single_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddLineBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,System.UInt32,VRageMath.MatrixD@,VRageMath.Vector3,System.Single,System.Single,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddLineBillboard(MyStringId, Vector4, Vector3D, UInt32, ref MatrixD, Vector3, Single, Single, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddLineBillboard(MyStringId material, Vector4 color, Vector3D origin, uint renderObjectID, ref MatrixD worldToLocal, Vector3 directionNormalized, float length, float thickness, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldToLocal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">directionNormalized</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddLineBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddLineBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddLineBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_VRageMath_Vector3_System_Single_System_Single_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddLineBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,VRageMath.Vector3,System.Single,System.Single,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddLineBillboard(MyStringId, Vector4, Vector3D, Vector3, Single, Single, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddLineBillboard(MyStringId material, Vector4 color, Vector3D origin, Vector3 directionNormalized, float length, float thickness, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">directionNormalized</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddLocalLineBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddLocalLineBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddLocalLineBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_System_UInt32_VRageMath_Vector3_System_Single_System_Single_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddLocalLineBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,System.UInt32,VRageMath.Vector3,System.Single,System.Single,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddLocalLineBillboard(MyStringId, Vector4, Vector3D, UInt32, Vector3, Single, Single, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddLocalLineBillboard(MyStringId material, Vector4 color, Vector3D origin, uint renderObjectID, Vector3 directionNormalized, float length, float thickness, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">directionNormalized</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">length</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">thickness</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddLocalPointBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddLocalPointBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddLocalPointBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_System_UInt32_System_Single_System_Single_VRageRender_MyBillboard_BlendTypeEnum_System_Int32_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddLocalPointBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,System.UInt32,System.Single,System.Single,VRageRender.MyBillboard.BlendTypeEnum,System.Int32,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddLocalPointBillboard(MyStringId, Vector4, Vector3D, UInt32, Single, Single, MyBillboard.BlendTypeEnum, Int32, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddLocalPointBillboard(MyStringId material, Vector4 color, Vector3D origin, uint renderObjectID, float radius, float angle, MyBillboard.BlendTypeEnum blendType, int customViewProjection = -1, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddPointBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddPointBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddPointBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_System_Single_System_Single_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddPointBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,System.Single,System.Single,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Collections.Generic.List{VRageRender.MyBillboard})">AddPointBillboard(MyStringId, Vector4, Vector3D, Single, Single, Int32, MyBillboard.BlendTypeEnum, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddPointBillboard(MyStringId material, Vector4 color, Vector3D origin, float radius, float angle, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddPointBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddPointBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddPointBillboard_VRage_Utils_MyStringId_VRageMath_Vector4_VRageMath_Vector3D_System_UInt32_VRageMath_MatrixD__System_Single_System_Single_System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Single_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddPointBillboard(VRage.Utils.MyStringId,VRageMath.Vector4,VRageMath.Vector3D,System.UInt32,VRageMath.MatrixD@,System.Single,System.Single,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Single,System.Collections.Generic.List{VRageRender.MyBillboard})">AddPointBillboard(MyStringId, Vector4, Vector3D, UInt32, ref MatrixD, Single, Single, Int32, MyBillboard.BlendTypeEnum, Single, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddPointBillboard(MyStringId material, Vector4 color, Vector3D origin, uint renderObjectID, ref MatrixD worldToLocal, float radius, float angle, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, float intensity = 1F, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">renderObjectID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldToLocal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">intensity</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddQuad_" data-uid="VRage.Game.MyTransparentGeometry.AddQuad*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddQuad_VRage_Utils_MyStringId_VRageMath_MyQuadD__VRageMath_Vector4_VRageMath_Vector3D__System_Int32_VRageRender_MyBillboard_BlendTypeEnum_System_Collections_Generic_List_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.AddQuad(VRage.Utils.MyStringId,VRageMath.MyQuadD@,VRageMath.Vector4,VRageMath.Vector3D@,System.Int32,VRageRender.MyBillboard.BlendTypeEnum,System.Collections.Generic.List{VRageRender.MyBillboard})">AddQuad(MyStringId, ref MyQuadD, Vector4, ref Vector3D, Int32, MyBillboard.BlendTypeEnum, List&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool AddQuad(MyStringId material, ref MyQuadD quad, Vector4 color, ref Vector3D vctPos, int customViewProjection = -1, MyBillboard.BlendTypeEnum blendType, List&lt;MyBillboard&gt; persistentBillboards = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vctPos</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">persistentBillboards</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddTriangleBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddTriangleBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddTriangleBillboard_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyStringId_System_UInt32_VRageMath_Vector3D_VRageMath_Vector4_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MyTransparentGeometry.AddTriangleBillboard(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyStringId,System.UInt32,VRageMath.Vector3D,VRageMath.Vector4,VRageRender.MyBillboard.BlendTypeEnum)">AddTriangleBillboard(Vector3D, Vector3D, Vector3D, Vector3, Vector3, Vector3, Vector2, Vector2, Vector2, MyStringId, UInt32, Vector3D, Vector4, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddTriangleBillboard(Vector3D p0, Vector3D p1, Vector3D p2, Vector3 n0, Vector3 n1, Vector3 n2, Vector2 uv0, Vector2 uv1, Vector2 uv2, MyStringId material, uint parentID, Vector3D worldPosition, Vector4 color, MyBillboard.BlendTypeEnum blendType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">parentID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_AddTriangleBillboard_" data-uid="VRage.Game.MyTransparentGeometry.AddTriangleBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_AddTriangleBillboard_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_VRage_Utils_MyStringId_System_UInt32_VRageMath_Vector3D_VRageRender_MyBillboard_BlendTypeEnum_" data-uid="VRage.Game.MyTransparentGeometry.AddTriangleBillboard(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,VRage.Utils.MyStringId,System.UInt32,VRageMath.Vector3D,VRageRender.MyBillboard.BlendTypeEnum)">AddTriangleBillboard(Vector3D, Vector3D, Vector3D, Vector3, Vector3, Vector3, Vector2, Vector2, Vector2, MyStringId, UInt32, Vector3D, MyBillboard.BlendTypeEnum)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AddTriangleBillboard(Vector3D p0, Vector3D p1, Vector3D p2, Vector3 n0, Vector3 n1, Vector3 n2, Vector2 uv0, Vector2 uv1, Vector2 uv2, MyStringId material, uint parentID, Vector3D worldPosition, MyBillboard.BlendTypeEnum blendType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">n2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uv2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">parentID</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">worldPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard.BlendTypeEnum</span></td>
        <td><span class="parametername">blendType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_" data-uid="VRage.Game.MyTransparentGeometry.ApplyActionOnPersistentBillboards*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_" data-uid="VRage.Game.MyTransparentGeometry.ApplyActionOnPersistentBillboards(System.Action)">ApplyActionOnPersistentBillboards(Action)</h4>
  <div class="markdown level1 summary"><p>Used to update billboards. You can change any values of MyBillboard inside of this action call. Used to iterate over your own set of Billboards, instead of all, that currently drawing.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ApplyActionOnPersistentBillboards(Action action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span></td>
        <td><span class="parametername">action</span></td>
        <td><p>Action, which should be executed</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_" data-uid="VRage.Game.MyTransparentGeometry.ApplyActionOnPersistentBillboards*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_VRageRender_MyBillboard__" data-uid="VRage.Game.MyTransparentGeometry.ApplyActionOnPersistentBillboards(System.Action{VRageRender.MyBillboard})">ApplyActionOnPersistentBillboards(Action&lt;MyBillboard&gt;)</h4>
  <div class="markdown level1 summary"><p>Used to update billboards. It iterates over each MyBillBoard. Use <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_ApplyActionOnPersistentBillboards_System_Action_">ApplyActionOnPersistentBillboards(Action)</a> if you need iterate over specific billboards.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ApplyActionOnPersistentBillboards(Action&lt;MyBillboard&gt; action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p>action, which should be executed</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_CreateBillboard_" data-uid="VRage.Game.MyTransparentGeometry.CreateBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_CreateBillboard_VRageRender_MyBillboard_VRageMath_MyQuadD__VRage_Utils_MyStringId_VRageMath_Vector4__VRageMath_Vector3D__System_Int32_System_Single_" data-uid="VRage.Game.MyTransparentGeometry.CreateBillboard(VRageRender.MyBillboard,VRageMath.MyQuadD@,VRage.Utils.MyStringId,VRageMath.Vector4@,VRageMath.Vector3D@,System.Int32,System.Single)">CreateBillboard(MyBillboard, ref MyQuadD, MyStringId, ref Vector4, ref Vector3D, Int32, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateBillboard(MyBillboard billboard, ref MyQuadD quad, MyStringId material, ref Vector4 color, ref Vector3D origin, int customViewProjection = -1, float reflection = 0F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard</span></td>
        <td><span class="parametername">billboard</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">reflection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_CreateBillboard_" data-uid="VRage.Game.MyTransparentGeometry.CreateBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_CreateBillboard_VRageRender_MyBillboard_VRageMath_MyQuadD__VRage_Utils_MyStringId_VRageMath_Vector4__VRageMath_Vector3D__VRageMath_Vector2_System_Int32_System_Single_" data-uid="VRage.Game.MyTransparentGeometry.CreateBillboard(VRageRender.MyBillboard,VRageMath.MyQuadD@,VRage.Utils.MyStringId,VRageMath.Vector4@,VRageMath.Vector3D@,VRageMath.Vector2,System.Int32,System.Single)">CreateBillboard(MyBillboard, ref MyQuadD, MyStringId, ref Vector4, ref Vector3D, Vector2, Int32, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateBillboard(MyBillboard billboard, ref MyQuadD quad, MyStringId material, ref Vector4 color, ref Vector3D origin, Vector2 uvOffset, int customViewProjection = -1, float reflectivity = 0F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard</span></td>
        <td><span class="parametername">billboard</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyQuadD.html">MyQuadD</a></td>
        <td><span class="parametername">quad</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a></td>
        <td><span class="parametername">material</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">origin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">uvOffset</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">customViewProjection</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">reflectivity</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_EndParticleProfilingBlock_" data-uid="VRage.Game.MyTransparentGeometry.EndParticleProfilingBlock*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_EndParticleProfilingBlock" data-uid="VRage.Game.MyTransparentGeometry.EndParticleProfilingBlock">EndParticleProfilingBlock()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void EndParticleProfilingBlock()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyTransparentGeometry_RemovePersistentBillboard_" data-uid="VRage.Game.MyTransparentGeometry.RemovePersistentBillboard*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_RemovePersistentBillboard_VRageRender_MyBillboard_System_Boolean_" data-uid="VRage.Game.MyTransparentGeometry.RemovePersistentBillboard(VRageRender.MyBillboard,System.Boolean)">RemovePersistentBillboard(MyBillboard, Boolean)</h4>
  <div class="markdown level1 summary"><p>Removes one persistent billboard from draw
FOR MODDERS: If you have more than 100 billboards to remove you should use another <a class="xref" href="VRage.Game.MyTransparentGeometry.html#VRage_Game_MyTransparentGeometry_RemovePersistentBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_">RemovePersistentBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</a> function</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RemovePersistentBillboard(MyBillboard billboard, bool immediate = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyBillboard</span></td>
        <td><span class="parametername">billboard</span></td>
        <td><p>Billboard to remove</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">immediate</span></td>
        <td><p>When true, instantly removes (causes crash if you call it from render draw thread), when false adds to remove list first</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_RemovePersistentBillboards_" data-uid="VRage.Game.MyTransparentGeometry.RemovePersistentBillboards*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_RemovePersistentBillboards_System_Collections_Generic_IEnumerable_VRageRender_MyBillboard__System_Boolean_" data-uid="VRage.Game.MyTransparentGeometry.RemovePersistentBillboards(System.Collections.Generic.IEnumerable{VRageRender.MyBillboard},System.Boolean)">RemovePersistentBillboards(IEnumerable&lt;MyBillboard&gt;, Boolean)</h4>
  <div class="markdown level1 summary"><p>Removes multiple persistent billboard from draw</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RemovePersistentBillboards(IEnumerable&lt;MyBillboard&gt; billboards, bool immediate = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<span class="xref">VRageRender.MyBillboard</span>&gt;</td>
        <td><span class="parametername">billboards</span></td>
        <td><p>Billboards to remove</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">immediate</span></td>
        <td><p>When true, instantly removes (causes crash if you call it from render draw thread), when false adds to remove list first</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_SetCamera_" data-uid="VRage.Game.MyTransparentGeometry.SetCamera*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_SetCamera_VRage_Game_Utils_MyCamera_" data-uid="VRage.Game.MyTransparentGeometry.SetCamera(VRage.Game.Utils.MyCamera)">SetCamera(MyCamera)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SetCamera(MyCamera camera)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.Game.Utils.MyCamera</span></td>
        <td><span class="parametername">camera</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyTransparentGeometry_StartParticleProfilingBlock_" data-uid="VRage.Game.MyTransparentGeometry.StartParticleProfilingBlock*"></a>
  <h4 id="VRage_Game_MyTransparentGeometry_StartParticleProfilingBlock_System_String_" data-uid="VRage.Game.MyTransparentGeometry.StartParticleProfilingBlock(System.String)">StartParticleProfilingBlock(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void StartParticleProfilingBlock(string name)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
