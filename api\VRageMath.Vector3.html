﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Vector3
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Vector3
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Vector3">
  
  
  <h1 id="VRageMath_Vector3" data-uid="VRageMath.Vector3" class="text-break">Class Vector3
  </h1>
  <div class="markdown level0 summary"><p>Defines a vector with three components.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Vector3</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Vector3_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Vector3 : ValueType, IEquatable&lt;Vector3&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_System_Double_System_Double_System_Double_" data-uid="VRageMath.Vector3.#ctor(System.Double,System.Double,System.Double)">Vector3(Double, Double, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(double x, double y, double z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">x</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">y</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">z</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_System_Single_" data-uid="VRageMath.Vector3.#ctor(System.Single)">Vector3(Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Vector3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value to initialize each component to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_System_Single_System_Single_System_Single_" data-uid="VRageMath.Vector3.#ctor(System.Single,System.Single,System.Single)">Vector3(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(float x, float y, float z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>Initial value for the x-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>Initial value for the y-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector3.#ctor(VRageMath.Vector2,System.Single)">Vector3(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(Vector2 value, float z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A vector containing the values to initialize x and y components with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_VRageMath_Vector3I_" data-uid="VRageMath.Vector3.#ctor(VRageMath.Vector3I)">Vector3(Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(Vector3I value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_VRageMath_Vector3I__" data-uid="VRageMath.Vector3.#ctor(VRageMath.Vector3I@)">Vector3(ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(ref Vector3I value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3__ctor_" data-uid="VRageMath.Vector3.#ctor*"></a>
  <h4 id="VRageMath_Vector3__ctor_VRageMath_Vector4_" data-uid="VRageMath.Vector3.#ctor(VRageMath.Vector4)">Vector3(Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3(Vector4 xyz)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">xyz</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Vector3_Backward" data-uid="VRageMath.Vector3.Backward">Backward</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Backward</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Down" data-uid="VRageMath.Vector3.Down">Down</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Down</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Forward" data-uid="VRageMath.Vector3.Forward">Forward</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Forward</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Half" data-uid="VRageMath.Vector3.Half">Half</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Half</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Invalid" data-uid="VRageMath.Vector3.Invalid">Invalid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Invalid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Left" data-uid="VRageMath.Vector3.Left">Left</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Left</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_MaxValue" data-uid="VRageMath.Vector3.MaxValue">MaxValue</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 MaxValue</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_MinusOne" data-uid="VRageMath.Vector3.MinusOne">MinusOne</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 MinusOne</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_MinValue" data-uid="VRageMath.Vector3.MinValue">MinValue</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 MinValue</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_NegativeInfinity" data-uid="VRageMath.Vector3.NegativeInfinity">NegativeInfinity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 NegativeInfinity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_One" data-uid="VRageMath.Vector3.One">One</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 One</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_PositiveInfinity" data-uid="VRageMath.Vector3.PositiveInfinity">PositiveInfinity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 PositiveInfinity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Right" data-uid="VRageMath.Vector3.Right">Right</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Right</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_UnitX" data-uid="VRageMath.Vector3.UnitX">UnitX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 UnitX</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_UnitY" data-uid="VRageMath.Vector3.UnitY">UnitY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 UnitY</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_UnitZ" data-uid="VRageMath.Vector3.UnitZ">UnitZ</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 UnitZ</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Up" data-uid="VRageMath.Vector3.Up">Up</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Up</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_X" data-uid="VRageMath.Vector3.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the x-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Y" data-uid="VRageMath.Vector3.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the y-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Z" data-uid="VRageMath.Vector3.Z">Z</h4>
  <div class="markdown level1 summary"><p>Gets or sets the z-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Z</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector3_Zero" data-uid="VRageMath.Vector3.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Vector3_Sum_" data-uid="VRageMath.Vector3.Sum*"></a>
  <h4 id="VRageMath_Vector3_Sum" data-uid="VRageMath.Vector3.Sum">Sum</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Sum { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Volume_" data-uid="VRageMath.Vector3.Volume*"></a>
  <h4 id="VRageMath_Vector3_Volume" data-uid="VRageMath.Vector3.Volume">Volume</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Volume { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Vector3_Abs_" data-uid="VRageMath.Vector3.Abs*"></a>
  <h4 id="VRageMath_Vector3_Abs_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Abs(VRageMath.Vector3)">Abs(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Abs(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_AbsMax_" data-uid="VRageMath.Vector3.AbsMax*"></a>
  <h4 id="VRageMath_Vector3_AbsMax" data-uid="VRageMath.Vector3.AbsMax">AbsMax()</h4>
  <div class="markdown level1 summary"><p>Returns the component of the vector, whose absolute value is largest of all the three components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float AbsMax()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_AbsMin_" data-uid="VRageMath.Vector3.AbsMin*"></a>
  <h4 id="VRageMath_Vector3_AbsMin" data-uid="VRageMath.Vector3.AbsMin">AbsMin()</h4>
  <div class="markdown level1 summary"><p>Returns the component of the vector, whose absolute value is smallest of all the three components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float AbsMin()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Add_" data-uid="VRageMath.Vector3.Add*"></a>
  <h4 id="VRageMath_Vector3_Add_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Add(VRageMath.Vector3)">Add(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Add(Vector3 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Add_" data-uid="VRageMath.Vector3.Add*"></a>
  <h4 id="VRageMath_Vector3_Add_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Add(VRageMath.Vector3,VRageMath.Vector3)">Add(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Add(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Add_" data-uid="VRageMath.Vector3.Add*"></a>
  <h4 id="VRageMath_Vector3_Add_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Add(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Add(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Sum of the source vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Angle_" data-uid="VRageMath.Vector3.Angle*"></a>
  <h4 id="VRageMath_Vector3_Angle_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Angle(VRageMath.Vector3,VRageMath.Vector3)">Angle(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Gets angle between 2 vectors in radians</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Angle(Vector3 a, Vector3 b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>Vector A</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>Vector B</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><p>Angle in radians</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ArePerpendicular_" data-uid="VRageMath.Vector3.ArePerpendicular*"></a>
  <h4 id="VRageMath_Vector3_ArePerpendicular_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.ArePerpendicular(VRageMath.Vector3,VRageMath.Vector3)">ArePerpendicular(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ArePerpendicular(Vector3 a, Vector3 b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ArePerpendicular_" data-uid="VRageMath.Vector3.ArePerpendicular*"></a>
  <h4 id="VRageMath_Vector3_ArePerpendicular_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.ArePerpendicular(VRageMath.Vector3@,VRageMath.Vector3@)">ArePerpendicular(in Vector3, in Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool ArePerpendicular(in Vector3 a, in Vector3 b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_AssertIsValid_" data-uid="VRageMath.Vector3.AssertIsValid*"></a>
  <h4 id="VRageMath_Vector3_AssertIsValid" data-uid="VRageMath.Vector3.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector3_Barycentric_" data-uid="VRageMath.Vector3.Barycentric*"></a>
  <h4 id="VRageMath_Vector3_Barycentric_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Single_System_Single_" data-uid="VRageMath.Vector3.Barycentric(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Single,System.Single)">Barycentric(Vector3, Vector3, Vector3, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector3 containing the 3D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 3D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Barycentric(Vector3 value1, Vector3 value2, Vector3 value3, float amount1, float amount2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Barycentric_" data-uid="VRageMath.Vector3.Barycentric*"></a>
  <h4 id="VRageMath_Vector3_Barycentric_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Single__System_Single__System_Single__" data-uid="VRageMath.Vector3.Barycentric(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Single@,System.Single@,System.Single@)">Barycentric(Vector3, Vector3, Vector3, Vector3, out Single, out Single, out Single)</h4>
  <div class="markdown level1 summary"><p>Compute barycentric coordinates (u, v, w) for point p with respect to triangle (a, b, c)
From : Real-Time Collision Detection, Christer Ericson, CRC Press
3.4 Barycentric Coordinates</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(Vector3 p, Vector3 a, Vector3 b, Vector3 c, out float u, out float v, out float w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">p</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">c</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">u</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">w</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Barycentric_" data-uid="VRageMath.Vector3.Barycentric*"></a>
  <h4 id="VRageMath_Vector3_Barycentric_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Single_System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Barycentric(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Single,System.Single,VRageMath.Vector3@)">Barycentric(ref Vector3, ref Vector3, ref Vector3, Single, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector3 containing the 3D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 3D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(ref Vector3 value1, ref Vector3 value2, ref Vector3 value3, float amount1, float amount2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector3 containing the 3D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The 3D Cartesian coordinates of the specified point are placed in this Vector3 on exit.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_CalculatePerpendicularVector_" data-uid="VRageMath.Vector3.CalculatePerpendicularVector*"></a>
  <h4 id="VRageMath_Vector3_CalculatePerpendicularVector_VRageMath_Vector3_" data-uid="VRageMath.Vector3.CalculatePerpendicularVector(VRageMath.Vector3)">CalculatePerpendicularVector(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 CalculatePerpendicularVector(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_CalculatePerpendicularVector_" data-uid="VRageMath.Vector3.CalculatePerpendicularVector*"></a>
  <h4 id="VRageMath_Vector3_CalculatePerpendicularVector_VRageMath_Vector3__" data-uid="VRageMath.Vector3.CalculatePerpendicularVector(VRageMath.Vector3@)">CalculatePerpendicularVector(out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CalculatePerpendicularVector(out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_CatmullRom_" data-uid="VRageMath.Vector3.CatmullRom*"></a>
  <h4 id="VRageMath_Vector3_CatmullRom_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.CatmullRom(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Single)">CatmullRom(Vector3, Vector3, Vector3, Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 CatmullRom(Vector3 value1, Vector3 value2, Vector3 value3, Vector3 value4, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_CatmullRom_" data-uid="VRageMath.Vector3.CatmullRom*"></a>
  <h4 id="VRageMath_Vector3_CatmullRom_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.CatmullRom(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">CatmullRom(ref Vector3, ref Vector3, ref Vector3, ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CatmullRom(ref Vector3 value1, ref Vector3 value2, ref Vector3 value3, ref Vector3 value4, float amount, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A vector that is the result of the Catmull-Rom interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Ceiling_" data-uid="VRageMath.Vector3.Ceiling*"></a>
  <h4 id="VRageMath_Vector3_Ceiling_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Ceiling(VRageMath.Vector3)">Ceiling(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Ceiling(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Clamp_" data-uid="VRageMath.Vector3.Clamp*"></a>
  <h4 id="VRageMath_Vector3_Clamp_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Clamp(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">Clamp(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Clamp(Vector3 value1, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Clamp_" data-uid="VRageMath.Vector3.Clamp*"></a>
  <h4 id="VRageMath_Vector3_Clamp_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Clamp(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Clamp(Vector3, Vector3, Vector3, out Vector3, out Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Clamp(Vector3 value1, Vector3 min, Vector3 max, out Vector3 clampedBy, out Vector3 minRemaining, out Vector3 maxRemaining)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">clampedBy</span></td>
        <td><p>Value was clamped by this amount</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">minRemaining</span></td>
        <td><p>Remaining min tolerance</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">maxRemaining</span></td>
        <td><p>Remaining max tolerance</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Clamp_" data-uid="VRageMath.Vector3.Clamp*"></a>
  <h4 id="VRageMath_Vector3_Clamp_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Clamp(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Clamp(ref Vector3, ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Clamp(ref Vector3 value1, ref Vector3 min, ref Vector3 max, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The clamped value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ClampToSphere_" data-uid="VRageMath.Vector3.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector3_ClampToSphere_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.ClampToSphere(VRageMath.Vector3,System.Single)">ClampToSphere(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 ClampToSphere(Vector3 vector, float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ClampToSphere_" data-uid="VRageMath.Vector3.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector3_ClampToSphere_VRageMath_Vector3__System_Single_" data-uid="VRageMath.Vector3.ClampToSphere(VRageMath.Vector3@,System.Single)">ClampToSphere(ref Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ClampToSphere(ref Vector3 vector, float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_CreateFromAzimuthAndElevation_" data-uid="VRageMath.Vector3.CreateFromAzimuthAndElevation*"></a>
  <h4 id="VRageMath_Vector3_CreateFromAzimuthAndElevation_System_Single_System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.CreateFromAzimuthAndElevation(System.Single,System.Single,VRageMath.Vector3@)">CreateFromAzimuthAndElevation(Single, Single, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAzimuthAndElevation(float azimuth, float elevation, out Vector3 direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">azimuth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">elevation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Cross_" data-uid="VRageMath.Vector3.Cross*"></a>
  <h4 id="VRageMath_Vector3_Cross_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Cross(VRageMath.Vector3)">Cross(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Cross(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Cross_" data-uid="VRageMath.Vector3.Cross*"></a>
  <h4 id="VRageMath_Vector3_Cross_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Cross(VRageMath.Vector3,VRageMath.Vector3)">Cross(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates the cross product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Cross(Vector3 vector1, Vector3 vector2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Cross_" data-uid="VRageMath.Vector3.Cross*"></a>
  <h4 id="VRageMath_Vector3_Cross_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Cross(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Cross(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates the cross product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Cross(ref Vector3 vector1, ref Vector3 vector2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The cross product of the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Distance_" data-uid="VRageMath.Vector3.Distance*"></a>
  <h4 id="VRageMath_Vector3_Distance_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Distance(VRageMath.Vector3,VRageMath.Vector3)">Distance(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Distance(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Distance_" data-uid="VRageMath.Vector3.Distance*"></a>
  <h4 id="VRageMath_Vector3_Distance_VRageMath_Vector3__VRageMath_Vector3__System_Single__" data-uid="VRageMath.Vector3.Distance(VRageMath.Vector3@,VRageMath.Vector3@,System.Single@)">Distance(ref Vector3, ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Distance(ref Vector3 value1, ref Vector3 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_DistanceSquared_" data-uid="VRageMath.Vector3.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector3_DistanceSquared_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.DistanceSquared(VRageMath.Vector3,VRageMath.Vector3)">DistanceSquared(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float DistanceSquared(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_DistanceSquared_" data-uid="VRageMath.Vector3.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector3_DistanceSquared_VRageMath_Vector3__VRageMath_Vector3__System_Single__" data-uid="VRageMath.Vector3.DistanceSquared(VRageMath.Vector3@,VRageMath.Vector3@,System.Single@)">DistanceSquared(ref Vector3, ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DistanceSquared(ref Vector3 value1, ref Vector3 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the two vectors squared.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Divide_" data-uid="VRageMath.Vector3.Divide*"></a>
  <h4 id="VRageMath_Vector3_Divide_System_Single_" data-uid="VRageMath.Vector3.Divide(System.Single)">Divide(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Divide(float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Divide_" data-uid="VRageMath.Vector3.Divide*"></a>
  <h4 id="VRageMath_Vector3_Divide_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Divide(VRageMath.Vector3,System.Single)">Divide(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Divide(Vector3 value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Divide_" data-uid="VRageMath.Vector3.Divide*"></a>
  <h4 id="VRageMath_Vector3_Divide_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Divide(VRageMath.Vector3,VRageMath.Vector3)">Divide(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Divide(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Divide_" data-uid="VRageMath.Vector3.Divide*"></a>
  <h4 id="VRageMath_Vector3_Divide_VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Divide(VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">Divide(ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector3 value1, float value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Divide_" data-uid="VRageMath.Vector3.Divide*"></a>
  <h4 id="VRageMath_Vector3_Divide_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Divide(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Divide(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_DominantAxisProjection_" data-uid="VRageMath.Vector3.DominantAxisProjection*"></a>
  <h4 id="VRageMath_Vector3_DominantAxisProjection_VRageMath_Vector3_" data-uid="VRageMath.Vector3.DominantAxisProjection(VRageMath.Vector3)">DominantAxisProjection(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that is equal to the projection of the input vector to the coordinate axis that corresponds
to the original vector's largest value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 DominantAxisProjection(Vector3 value1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_DominantAxisProjection_" data-uid="VRageMath.Vector3.DominantAxisProjection*"></a>
  <h4 id="VRageMath_Vector3_DominantAxisProjection_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.DominantAxisProjection(VRageMath.Vector3@,VRageMath.Vector3@)">DominantAxisProjection(ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates a vector that is equal to the projection of the input vector to the coordinate axis that corresponds
to the original vector's largest value. The result is saved into a user-specified variable.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DominantAxisProjection(ref Vector3 value1, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The projected vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Dot_" data-uid="VRageMath.Vector3.Dot*"></a>
  <h4 id="VRageMath_Vector3_Dot_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Dot(VRageMath.Vector3)">Dot(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Dot(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Dot_" data-uid="VRageMath.Vector3.Dot*"></a>
  <h4 id="VRageMath_Vector3_Dot_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Dot(VRageMath.Vector3,VRageMath.Vector3)">Dot(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors. If the two vectors are unit vectors, the dot product returns a floating point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Dot(Vector3 vector1, Vector3 vector2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Dot_" data-uid="VRageMath.Vector3.Dot*"></a>
  <h4 id="VRageMath_Vector3_Dot_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Dot(VRageMath.Vector3@)">Dot(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Dot(ref Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Dot_" data-uid="VRageMath.Vector3.Dot*"></a>
  <h4 id="VRageMath_Vector3_Dot_VRageMath_Vector3__VRageMath_Vector3__System_Single__" data-uid="VRageMath.Vector3.Dot(VRageMath.Vector3@,VRageMath.Vector3@,System.Single@)">Dot(ref Vector3, ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors and writes the result to a user-specified variable. If the two vectors are unit vectors, the dot product returns a floating point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Vector3 vector1, ref Vector3 vector2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the two vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Equals_" data-uid="VRageMath.Vector3.Equals*"></a>
  <h4 id="VRageMath_Vector3_Equals_System_Object_" data-uid="VRageMath.Vector3.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Equals_" data-uid="VRageMath.Vector3.Equals*"></a>
  <h4 id="VRageMath_Vector3_Equals_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Equals(VRageMath.Vector3)">Equals(Vector3)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Vector3.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector3 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Vector3 to compare with the current Vector3.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Equals_" data-uid="VRageMath.Vector3.Equals*"></a>
  <h4 id="VRageMath_Vector3_Equals_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Equals(VRageMath.Vector3,System.Single)">Equals(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector3 other, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Floor_" data-uid="VRageMath.Vector3.Floor*"></a>
  <h4 id="VRageMath_Vector3_Floor_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Floor(VRageMath.Vector3)">Floor(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Floor(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_GetAzimuthAndElevation_" data-uid="VRageMath.Vector3.GetAzimuthAndElevation*"></a>
  <h4 id="VRageMath_Vector3_GetAzimuthAndElevation_VRageMath_Vector3_System_Single__System_Single__" data-uid="VRageMath.Vector3.GetAzimuthAndElevation(VRageMath.Vector3,System.Single@,System.Single@)">GetAzimuthAndElevation(Vector3, out Single, out Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GetAzimuthAndElevation(Vector3 v, out float azimuth, out float elevation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">azimuth</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">elevation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_GetDim_" data-uid="VRageMath.Vector3.GetDim*"></a>
  <h4 id="VRageMath_Vector3_GetDim_System_Int32_" data-uid="VRageMath.Vector3.GetDim(System.Int32)">GetDim(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float GetDim(int i)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">i</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_GetHash_" data-uid="VRageMath.Vector3.GetHash*"></a>
  <h4 id="VRageMath_Vector3_GetHash" data-uid="VRageMath.Vector3.GetHash">GetHash()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of the vector object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long GetHash()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_GetHashCode_" data-uid="VRageMath.Vector3.GetHashCode*"></a>
  <h4 id="VRageMath_Vector3_GetHashCode" data-uid="VRageMath.Vector3.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_GetNormalized_" data-uid="VRageMath.Vector3.GetNormalized*"></a>
  <h4 id="VRageMath_Vector3_GetNormalized_VRageMath_Vector3__" data-uid="VRageMath.Vector3.GetNormalized(VRageMath.Vector3@)">GetNormalized(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool GetNormalized(ref Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Hermite_" data-uid="VRageMath.Vector3.Hermite*"></a>
  <h4 id="VRageMath_Vector3_Hermite_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Hermite(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3,System.Single)">Hermite(Vector3, Vector3, Vector3, Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Hermite(Vector3 value1, Vector3 tangent1, Vector3 value2, Vector3 tangent2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Hermite_" data-uid="VRageMath.Vector3.Hermite*"></a>
  <h4 id="VRageMath_Vector3_Hermite_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Hermite(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">Hermite(ref Vector3, ref Vector3, ref Vector3, ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Hermite(ref Vector3 value1, ref Vector3 tangent1, ref Vector3 value2, ref Vector3 tangent2, float amount, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the Hermite spline interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Interpolate3_" data-uid="VRageMath.Vector3.Interpolate3*"></a>
  <h4 id="VRageMath_Vector3_Interpolate3_VRageMath_Vector3_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Interpolate3(VRageMath.Vector3,VRageMath.Vector3,System.Single)">Interpolate3(Vector3, Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Interpolate3(Vector3 v0, Vector3 v1, float rt)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">rt</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsInsideInclusive_" data-uid="VRageMath.Vector3.IsInsideInclusive*"></a>
  <h4 id="VRageMath_Vector3_IsInsideInclusive_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.IsInsideInclusive(VRageMath.Vector3@,VRageMath.Vector3@)">IsInsideInclusive(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsInsideInclusive(ref Vector3 min, ref Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsUnit_" data-uid="VRageMath.Vector3.IsUnit*"></a>
  <h4 id="VRageMath_Vector3_IsUnit_VRageMath_Vector3__" data-uid="VRageMath.Vector3.IsUnit(VRageMath.Vector3@)">IsUnit(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsUnit(ref Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsValid_" data-uid="VRageMath.Vector3.IsValid*"></a>
  <h4 id="VRageMath_Vector3_IsValid" data-uid="VRageMath.Vector3.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsZero_" data-uid="VRageMath.Vector3.IsZero*"></a>
  <h4 id="VRageMath_Vector3_IsZero_VRageMath_Vector3_" data-uid="VRageMath.Vector3.IsZero(VRageMath.Vector3)">IsZero(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsZero_" data-uid="VRageMath.Vector3.IsZero*"></a>
  <h4 id="VRageMath_Vector3_IsZero_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.IsZero(VRageMath.Vector3,System.Single)">IsZero(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector3 value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsZero_" data-uid="VRageMath.Vector3.IsZero*"></a>
  <h4 id="VRageMath_Vector3_IsZero_VRageMath_Vector3__" data-uid="VRageMath.Vector3.IsZero(VRageMath.Vector3@)">IsZero(ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Vector3 vec)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsZeroVector_" data-uid="VRageMath.Vector3.IsZeroVector*"></a>
  <h4 id="VRageMath_Vector3_IsZeroVector_VRageMath_Vector3_" data-uid="VRageMath.Vector3.IsZeroVector(VRageMath.Vector3)">IsZeroVector(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 IsZeroVector(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_IsZeroVector_" data-uid="VRageMath.Vector3.IsZeroVector*"></a>
  <h4 id="VRageMath_Vector3_IsZeroVector_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.IsZeroVector(VRageMath.Vector3,System.Single)">IsZeroVector(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 IsZeroVector(Vector3 value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Length_" data-uid="VRageMath.Vector3.Length*"></a>
  <h4 id="VRageMath_Vector3_Length" data-uid="VRageMath.Vector3.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_LengthSquared_" data-uid="VRageMath.Vector3.LengthSquared*"></a>
  <h4 id="VRageMath_Vector3_LengthSquared" data-uid="VRageMath.Vector3.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Lerp_" data-uid="VRageMath.Vector3.Lerp*"></a>
  <h4 id="VRageMath_Vector3_Lerp_VRageMath_Vector3_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Lerp(VRageMath.Vector3,VRageMath.Vector3,System.Single)">Lerp(Vector3, Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Lerp(Vector3 value1, Vector3 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Lerp_" data-uid="VRageMath.Vector3.Lerp*"></a>
  <h4 id="VRageMath_Vector3_Lerp_VRageMath_Vector3__VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Lerp(VRageMath.Vector3@,VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">Lerp(ref Vector3, ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Vector3 value1, ref Vector3 value2, float amount, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Max_" data-uid="VRageMath.Vector3.Max*"></a>
  <h4 id="VRageMath_Vector3_Max" data-uid="VRageMath.Vector3.Max">Max()</h4>
  <div class="markdown level1 summary"><p>Returns the component of the vector that is largest of all the three components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Max()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Max_" data-uid="VRageMath.Vector3.Max*"></a>
  <h4 id="VRageMath_Vector3_Max_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Max(VRageMath.Vector3,VRageMath.Vector3)">Max(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Max(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Max_" data-uid="VRageMath.Vector3.Max*"></a>
  <h4 id="VRageMath_Vector3_Max_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Max(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Max(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Max(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The maximized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_MaxAbsComponent_" data-uid="VRageMath.Vector3.MaxAbsComponent*"></a>
  <h4 id="VRageMath_Vector3_MaxAbsComponent" data-uid="VRageMath.Vector3.MaxAbsComponent">MaxAbsComponent()</h4>
  <div class="markdown level1 summary"><p>Keeps only component with maximal absolute, others are set to zero.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 MaxAbsComponent()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Min_" data-uid="VRageMath.Vector3.Min*"></a>
  <h4 id="VRageMath_Vector3_Min" data-uid="VRageMath.Vector3.Min">Min()</h4>
  <div class="markdown level1 summary"><p>Returns the component of the vector that is smallest of all the three components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Min()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Min_" data-uid="VRageMath.Vector3.Min*"></a>
  <h4 id="VRageMath_Vector3_Min_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Min(VRageMath.Vector3,VRageMath.Vector3)">Min(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Min(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Min_" data-uid="VRageMath.Vector3.Min*"></a>
  <h4 id="VRageMath_Vector3_Min_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Min(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Min(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Min(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The minimized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_MinMax_" data-uid="VRageMath.Vector3.MinMax*"></a>
  <h4 id="VRageMath_Vector3_MinMax_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.MinMax(VRageMath.Vector3@,VRageMath.Vector3@)">MinMax(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Separates minimal and maximal values of any two input vectors</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void MinMax(ref Vector3 min, ref Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>minimal values of the two vectors</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>maximal values of the two vectors</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Multiply_" data-uid="VRageMath.Vector3.Multiply*"></a>
  <h4 id="VRageMath_Vector3_Multiply_System_Single_" data-uid="VRageMath.Vector3.Multiply(System.Single)">Multiply(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Multiply(float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Multiply_" data-uid="VRageMath.Vector3.Multiply*"></a>
  <h4 id="VRageMath_Vector3_Multiply_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Multiply(VRageMath.Vector3,System.Single)">Multiply(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Multiply(Vector3 value1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Multiply_" data-uid="VRageMath.Vector3.Multiply*"></a>
  <h4 id="VRageMath_Vector3_Multiply_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Multiply(VRageMath.Vector3,VRageMath.Vector3)">Multiply(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Multiply(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Multiply_" data-uid="VRageMath.Vector3.Multiply*"></a>
  <h4 id="VRageMath_Vector3_Multiply_VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.Multiply(VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">Multiply(ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector3 value1, float scaleFactor, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Multiply_" data-uid="VRageMath.Vector3.Multiply*"></a>
  <h4 id="VRageMath_Vector3_Multiply_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Multiply(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Multiply(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Negate_" data-uid="VRageMath.Vector3.Negate*"></a>
  <h4 id="VRageMath_Vector3_Negate_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Negate(VRageMath.Vector3)">Negate(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Negate(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Negate_" data-uid="VRageMath.Vector3.Negate*"></a>
  <h4 id="VRageMath_Vector3_Negate_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Negate(VRageMath.Vector3@,VRageMath.Vector3@)">Negate(ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Vector3 value, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Vector pointing in the opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Normalize_" data-uid="VRageMath.Vector3.Normalize*"></a>
  <h4 id="VRageMath_Vector3_Normalize" data-uid="VRageMath.Vector3.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Turns the current vector into a unit vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Normalize()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Normalize_" data-uid="VRageMath.Vector3.Normalize*"></a>
  <h4 id="VRageMath_Vector3_Normalize_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Normalize(VRageMath.Vector3)">Normalize(Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Normalize(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Normalize_" data-uid="VRageMath.Vector3.Normalize*"></a>
  <h4 id="VRageMath_Vector3_Normalize_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Normalize(VRageMath.Vector3@,VRageMath.Vector3@)">Normalize(ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector, writing the result to a user-specified variable. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector3 value, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The normalized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Normalize_" data-uid="VRageMath.Vector3.Normalize*"></a>
  <h4 id="VRageMath_Vector3_Normalize_VRageMath_Vector3D_" data-uid="VRageMath.Vector3.Normalize(VRageMath.Vector3D)">Normalize(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Normalize(Vector3D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Normalized_" data-uid="VRageMath.Vector3.Normalized*"></a>
  <h4 id="VRageMath_Vector3_Normalized" data-uid="VRageMath.Vector3.Normalized">Normalized()</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Normalized()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><p>Normalized vector</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ProjectOnPlane_" data-uid="VRageMath.Vector3.ProjectOnPlane*"></a>
  <h4 id="VRageMath_Vector3_ProjectOnPlane_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.ProjectOnPlane(VRageMath.Vector3@,VRageMath.Vector3@)">ProjectOnPlane(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Projects given vector on plane specified by it's normal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 ProjectOnPlane(ref Vector3 vec, ref Vector3 planeNormal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td><p>Vector which is to be projected</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">planeNormal</span></td>
        <td><p>Plane normal (may or may not be normalized)</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><p>Vector projected on plane</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ProjectOnVector_" data-uid="VRageMath.Vector3.ProjectOnVector*"></a>
  <h4 id="VRageMath_Vector3_ProjectOnVector_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.ProjectOnVector(VRageMath.Vector3@,VRageMath.Vector3@)">ProjectOnVector(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Projects vector on another vector resulting in new vector in guided vector's direction with different length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 ProjectOnVector(ref Vector3 vec, ref Vector3 guideVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vec</span></td>
        <td><p>Vector which is to be projected</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">guideVector</span></td>
        <td><p>Guide vector (may or may not be normalized)</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><p>Vector projected on guide vector</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_RectangularDistance_" data-uid="VRageMath.Vector3.RectangularDistance*"></a>
  <h4 id="VRageMath_Vector3_RectangularDistance_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.RectangularDistance(VRageMath.Vector3,VRageMath.Vector3)">RectangularDistance(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float RectangularDistance(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_RectangularDistance_" data-uid="VRageMath.Vector3.RectangularDistance*"></a>
  <h4 id="VRageMath_Vector3_RectangularDistance_VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.RectangularDistance(VRageMath.Vector3@,VRageMath.Vector3@)">RectangularDistance(ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"><p>Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float RectangularDistance(ref Vector3 value1, ref Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Reflect_" data-uid="VRageMath.Vector3.Reflect*"></a>
  <h4 id="VRageMath_Vector3_Reflect_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Reflect(VRageMath.Vector3,VRageMath.Vector3)">Reflect(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns the reflection of a vector off a surface that has the specified normal.  Reference page contains code sample.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Reflect(Vector3 vector, Vector3 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of the surface.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Reflect_" data-uid="VRageMath.Vector3.Reflect*"></a>
  <h4 id="VRageMath_Vector3_Reflect_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Reflect(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Reflect(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns the reflection of a vector off a surface that has the specified normal.  Reference page contains code sample.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Reflect(ref Vector3 vector, ref Vector3 normal, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of the surface.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The reflected vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Reject_" data-uid="VRageMath.Vector3.Reject*"></a>
  <h4 id="VRageMath_Vector3_Reject_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Reject(VRageMath.Vector3,VRageMath.Vector3)">Reject(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Reject(Vector3 vector, Vector3 direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Vector which is to be rejected</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>Direction from which the input vector will be rejected</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><p>Rejection of the vector from the given direction</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Reject_" data-uid="VRageMath.Vector3.Reject*"></a>
  <h4 id="VRageMath_Vector3_Reject_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Reject(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Reject(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Reject(ref Vector3 vector, ref Vector3 direction, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Vector which is to be rejected</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>Direction from which the input vector will be rejected</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>Rejection of the vector from the given direction</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_RotateAndScale_" data-uid="VRageMath.Vector3.RotateAndScale*"></a>
  <h4 id="VRageMath_Vector3_RotateAndScale_VRageMath_Vector3_VRageMath_Matrix_" data-uid="VRageMath.Vector3.RotateAndScale(VRageMath.Vector3,VRageMath.Matrix)">RotateAndScale(Vector3, Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 RotateAndScale(Vector3 vector, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_RotateAndScale_" data-uid="VRageMath.Vector3.RotateAndScale*"></a>
  <h4 id="VRageMath_Vector3_RotateAndScale_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Vector3.RotateAndScale(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector3@)">RotateAndScale(ref Vector3, ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void RotateAndScale(ref Vector3 vector, ref Matrix matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Round_" data-uid="VRageMath.Vector3.Round*"></a>
  <h4 id="VRageMath_Vector3_Round_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Round(VRageMath.Vector3)">Round(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Round(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Round_" data-uid="VRageMath.Vector3.Round*"></a>
  <h4 id="VRageMath_Vector3_Round_VRageMath_Vector3_System_Int32_" data-uid="VRageMath.Vector3.Round(VRageMath.Vector3,System.Int32)">Round(Vector3, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Round(Vector3 v, int numDecimals)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">numDecimals</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_SetDim_" data-uid="VRageMath.Vector3.SetDim*"></a>
  <h4 id="VRageMath_Vector3_SetDim_System_Int32_System_Single_" data-uid="VRageMath.Vector3.SetDim(System.Int32,System.Single)">SetDim(Int32, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDim(int i, float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">i</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Sign_" data-uid="VRageMath.Vector3.Sign*"></a>
  <h4 id="VRageMath_Vector3_Sign_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Sign(VRageMath.Vector3)">Sign(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Sign(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Sign_" data-uid="VRageMath.Vector3.Sign*"></a>
  <h4 id="VRageMath_Vector3_Sign_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.Sign(VRageMath.Vector3,System.Single)">Sign(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Sign(Vector3 value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_SignNonZero_" data-uid="VRageMath.Vector3.SignNonZero*"></a>
  <h4 id="VRageMath_Vector3_SignNonZero_VRageMath_Vector3_" data-uid="VRageMath.Vector3.SignNonZero(VRageMath.Vector3)">SignNonZero(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns per component sign, never returns zero.
For zero component returns sign 1.
Faster than Sign.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 SignNonZero(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_SmoothStep_" data-uid="VRageMath.Vector3.SmoothStep*"></a>
  <h4 id="VRageMath_Vector3_SmoothStep_VRageMath_Vector3_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.SmoothStep(VRageMath.Vector3,VRageMath.Vector3,System.Single)">SmoothStep(Vector3, Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 SmoothStep(Vector3 value1, Vector3 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_SmoothStep_" data-uid="VRageMath.Vector3.SmoothStep*"></a>
  <h4 id="VRageMath_Vector3_SmoothStep_VRageMath_Vector3__VRageMath_Vector3__System_Single_VRageMath_Vector3__" data-uid="VRageMath.Vector3.SmoothStep(VRageMath.Vector3@,VRageMath.Vector3@,System.Single,VRageMath.Vector3@)">SmoothStep(ref Vector3, ref Vector3, Single, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SmoothStep(ref Vector3 value1, ref Vector3 value2, float amount, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The interpolated value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Step_" data-uid="VRageMath.Vector3.Step*"></a>
  <h4 id="VRageMath_Vector3_Step_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Step(VRageMath.Vector3)">Step(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Step(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Subtract_" data-uid="VRageMath.Vector3.Subtract*"></a>
  <h4 id="VRageMath_Vector3_Subtract_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.Subtract(VRageMath.Vector3,VRageMath.Vector3)">Subtract(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Subtract(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Subtract_" data-uid="VRageMath.Vector3.Subtract*"></a>
  <h4 id="VRageMath_Vector3_Subtract_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Subtract(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Subtract(ref Vector3, ref Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Vector3 value1, ref Vector3 value2, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_SwapYZCoordinates_" data-uid="VRageMath.Vector3.SwapYZCoordinates*"></a>
  <h4 id="VRageMath_Vector3_SwapYZCoordinates_VRageMath_Vector3_" data-uid="VRageMath.Vector3.SwapYZCoordinates(VRageMath.Vector3)">SwapYZCoordinates(Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 SwapYZCoordinates(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ToString_" data-uid="VRageMath.Vector3.ToString*"></a>
  <h4 id="VRageMath_Vector3_ToString" data-uid="VRageMath.Vector3.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_ToString_" data-uid="VRageMath.Vector3.ToString*"></a>
  <h4 id="VRageMath_Vector3_ToString_System_String_" data-uid="VRageMath.Vector3.ToString(System.String)">ToString(String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string ToString(string format)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">format</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3_VRageMath_Matrix_" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3,VRageMath.Matrix)">Transform(Vector3, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a 3D vector by the given matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Transform(Vector3 position, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3_VRageMath_Matrix__" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3,VRageMath.Matrix@)">Transform(Vector3, ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Transform(Vector3 position, ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3_VRageMath_MatrixD_" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3,VRageMath.MatrixD)">Transform(Vector3, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a 3D vector by the given matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D Transform(Vector3 position, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3_VRageMath_Quaternion_" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3,VRageMath.Quaternion)">Transform(Vector3, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Transform(Vector3 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector3@)">Transform(ref Vector3, ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3 position, ref Matrix matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The transformed vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3__VRageMath_MatrixI__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3@,VRageMath.MatrixI@,VRageMath.Vector3@)">Transform(ref Vector3, ref MatrixI, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3 position, ref MatrixI matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3__VRageMath_Quaternion__VRageMath_Vector3__" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3@,VRageMath.Quaternion@,VRageMath.Vector3@)">Transform(ref Vector3, ref Quaternion, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3 value, ref Quaternion rotation, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Vector3 filled in with the results of the rotation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3___System_Int32_VRageMath_Matrix__VRageMath_Vector3___System_Int32_System_Int32_" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3[],System.Int32,VRageMath.Matrix@,VRageMath.Vector3[],System.Int32,System.Int32)">Transform(Vector3[], Int32, ref Matrix, Vector3[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Applies a specified transform Matrix to a specified range of an array of Vector3s and writes the results into a specified range of a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector3[] sourceArray, int sourceIndex, ref Matrix matrix, Vector3[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array at which to start.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array at which to start.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector3s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3___System_Int32_VRageMath_Quaternion__VRageMath_Vector3___System_Int32_System_Int32_" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3[],System.Int32,VRageMath.Quaternion@,VRageMath.Vector3[],System.Int32,System.Int32)">Transform(Vector3[], Int32, ref Quaternion, Vector3[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Applies a specified Quaternion rotation to a specified range of an array of Vector3s and writes the results into a specified range of a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector3[] sourceArray, int sourceIndex, ref Quaternion rotation, Vector3[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array at which to start.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array at which to start.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector3s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3___VRageMath_Matrix__VRageMath_Vector3___" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3[],VRageMath.Matrix@,VRageMath.Vector3[])">Transform(Vector3[], ref Matrix, Vector3[])</h4>
  <div class="markdown level1 summary"><p>Transforms a source array of Vector3s by a specified Matrix and writes the results to an existing destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector3[] sourceArray, ref Matrix matrix, Vector3[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing destination array into which the transformed Vector3s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_Transform_" data-uid="VRageMath.Vector3.Transform*"></a>
  <h4 id="VRageMath_Vector3_Transform_VRageMath_Vector3___VRageMath_Quaternion__VRageMath_Vector3___" data-uid="VRageMath.Vector3.Transform(VRageMath.Vector3[],VRageMath.Quaternion@,VRageMath.Vector3[])">Transform(Vector3[], ref Quaternion, Vector3[])</h4>
  <div class="markdown level1 summary"><p>Transforms a source array of Vector3s by a specified Quaternion rotation and writes the results to an existing destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector3[] sourceArray, ref Quaternion rotation, Vector3[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing destination array into which the transformed Vector3s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNoProjection_" data-uid="VRageMath.Vector3.TransformNoProjection*"></a>
  <h4 id="VRageMath_Vector3_TransformNoProjection_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformNoProjection(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector3@)">TransformNoProjection(ref Vector3, ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNoProjection(ref Vector3 vector, ref Matrix matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3_VRageMath_Matrix_" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3,VRageMath.Matrix)">TransformNormal(Vector3, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a 3D vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 TransformNormal(Vector3 normal, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3_VRageMath_Matrix__" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3,VRageMath.Matrix@)">TransformNormal(Vector3, ref Matrix)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 TransformNormal(Vector3 normal, ref Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3_VRageMath_MatrixD_" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3,VRageMath.MatrixD)">TransformNormal(Vector3, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a 3D vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 TransformNormal(Vector3 normal, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3_VRageMath_MyBlockOrientation_" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3,VRageMath.MyBlockOrientation)">TransformNormal(Vector3, MyBlockOrientation)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 TransformNormal(Vector3 normal, MyBlockOrientation orientation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector3@)">TransformNormal(ref Vector3, ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Transforms a vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector3 normal, ref Matrix matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector3 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3__VRageMath_MatrixD__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3@,VRageMath.MatrixD@,VRageMath.Vector3@)">TransformNormal(ref Vector3, ref MatrixD, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector3 normal, ref MatrixD matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3__VRageMath_MatrixI__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3@,VRageMath.MatrixI@,VRageMath.Vector3@)">TransformNormal(ref Vector3, ref MatrixI, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector3 normal, ref MatrixI matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3__VRageMath_MyBlockOrientation_VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3@,VRageMath.MyBlockOrientation,VRageMath.Vector3@)">TransformNormal(ref Vector3, MyBlockOrientation, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector3 normal, MyBlockOrientation orientation, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3___System_Int32_VRageMath_Matrix__VRageMath_Vector3___System_Int32_System_Int32_" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3[],System.Int32,VRageMath.Matrix@,VRageMath.Vector3[],System.Int32,System.Int32)">TransformNormal(Vector3[], Int32, ref Matrix, Vector3[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of 3D vector normals by a specified Matrix and writes the results to a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector3[] sourceArray, int sourceIndex, ref Matrix matrix, Vector3[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array of Vector3 normals.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The starting index in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination Vector3 array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The starting index in the destination array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of vectors to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3___VRageMath_Matrix__VRageMath_Vector3___" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3[],VRageMath.Matrix@,VRageMath.Vector3[])">TransformNormal(Vector3[], ref Matrix, Vector3[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of 3D vector normals by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector3[] sourceArray, ref Matrix matrix, Vector3[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector3 normals to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing Vector3 array into which the results of the transforms are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformNormal_" data-uid="VRageMath.Vector3.TransformNormal*"></a>
  <h4 id="VRageMath_Vector3_TransformNormal_VRageMath_Vector3D_VRageMath_Matrix_" data-uid="VRageMath.Vector3.TransformNormal(VRageMath.Vector3D,VRageMath.Matrix)">TransformNormal(Vector3D, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a 3D vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 TransformNormal(Vector3D normal, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TransformProjection_" data-uid="VRageMath.Vector3.TransformProjection*"></a>
  <h4 id="VRageMath_Vector3_TransformProjection_VRageMath_Vector3__VRageMath_Matrix__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TransformProjection(VRageMath.Vector3@,VRageMath.Matrix@,VRageMath.Vector3@)">TransformProjection(ref Vector3, ref Matrix, out Vector3)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given projection matrix (both ortho and perspective are supported)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformProjection(ref Vector3 position, ref Matrix matrix, out Vector3 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The projection Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The transformed vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TriangleArea_" data-uid="VRageMath.Vector3.TriangleArea*"></a>
  <h4 id="VRageMath_Vector3_TriangleArea_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.TriangleArea(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">TriangleArea(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float TriangleArea(Vector3 v1, Vector3 v2, Vector3 v3)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_TriangleArea_" data-uid="VRageMath.Vector3.TriangleArea*"></a>
  <h4 id="VRageMath_Vector3_TriangleArea_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Vector3.TriangleArea(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">TriangleArea(ref Vector3, ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float TriangleArea(ref Vector3 v1, ref Vector3 v2, ref Vector3 v3)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_VolumeInt_" data-uid="VRageMath.Vector3.VolumeInt*"></a>
  <h4 id="VRageMath_Vector3_VolumeInt_System_Single_" data-uid="VRageMath.Vector3.VolumeInt(System.Single)">VolumeInt(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public long VolumeInt(float multiplier)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">multiplier</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Vector3_op_Addition_" data-uid="VRageMath.Vector3.op_Addition*"></a>
  <h4 id="VRageMath_Vector3_op_Addition_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.op_Addition(VRageMath.Vector3,System.Single)">Addition(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator +(Vector3 value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Addition_" data-uid="VRageMath.Vector3.op_Addition*"></a>
  <h4 id="VRageMath_Vector3_op_Addition_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Addition(VRageMath.Vector3,VRageMath.Vector3)">Addition(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator +(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Division_" data-uid="VRageMath.Vector3.op_Division*"></a>
  <h4 id="VRageMath_Vector3_op_Division_System_Single_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Division(System.Single,VRageMath.Vector3)">Division(Single, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator /(float value, Vector3 divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">divider</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Division_" data-uid="VRageMath.Vector3.op_Division*"></a>
  <h4 id="VRageMath_Vector3_op_Division_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.op_Division(VRageMath.Vector3,System.Single)">Division(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator /(Vector3 value, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Division_" data-uid="VRageMath.Vector3.op_Division*"></a>
  <h4 id="VRageMath_Vector3_op_Division_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Division(VRageMath.Vector3,VRageMath.Vector3)">Division(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator /(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Equality_" data-uid="VRageMath.Vector3.op_Equality*"></a>
  <h4 id="VRageMath_Vector3_op_Equality_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Equality(VRageMath.Vector3,VRageMath.Vector3)">Equality(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Inequality_" data-uid="VRageMath.Vector3.op_Inequality*"></a>
  <h4 id="VRageMath_Vector3_op_Inequality_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Inequality(VRageMath.Vector3,VRageMath.Vector3)">Inequality(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Multiply_" data-uid="VRageMath.Vector3.op_Multiply*"></a>
  <h4 id="VRageMath_Vector3_op_Multiply_System_Single_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Multiply(System.Single,VRageMath.Vector3)">Multiply(Single, Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator *(float scaleFactor, Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Multiply_" data-uid="VRageMath.Vector3.op_Multiply*"></a>
  <h4 id="VRageMath_Vector3_op_Multiply_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.op_Multiply(VRageMath.Vector3,System.Single)">Multiply(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator *(Vector3 value, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Multiply_" data-uid="VRageMath.Vector3.op_Multiply*"></a>
  <h4 id="VRageMath_Vector3_op_Multiply_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Multiply(VRageMath.Vector3,VRageMath.Vector3)">Multiply(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator *(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Subtraction_" data-uid="VRageMath.Vector3.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector3_op_Subtraction_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Vector3.op_Subtraction(VRageMath.Vector3,System.Single)">Subtraction(Vector3, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator -(Vector3 value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_Subtraction_" data-uid="VRageMath.Vector3.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector3_op_Subtraction_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_Subtraction(VRageMath.Vector3,VRageMath.Vector3)">Subtraction(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator -(Vector3 value1, Vector3 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector3_op_UnaryNegation_" data-uid="VRageMath.Vector3.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Vector3_op_UnaryNegation_VRageMath_Vector3_" data-uid="VRageMath.Vector3.op_UnaryNegation(VRageMath.Vector3)">UnaryNegation(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 operator -(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_HSVtoColor_VRageMath_Vector3_">ColorExtensions.HSVtoColor(Vector3)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_PackHSVToUint_VRageMath_Vector3_">ColorExtensions.PackHSVToUint(Vector3)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToLinearRGB_VRageMath_Vector3_">ColorExtensions.ToLinearRGB(Vector3)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToSRGB_VRageMath_Vector3_">ColorExtensions.ToSRGB(Vector3)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ToGray_VRageMath_Vector3_">ColorExtensions.ToGray(Vector3)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.Vector3Extensions.html#VRageMath_Vector3Extensions_Project_VRageMath_Vector3_VRageMath_Vector3_">Vector3Extensions.Project(Vector3, Vector3)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
