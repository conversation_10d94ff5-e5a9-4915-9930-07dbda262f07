﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyAPIGatewayShortcuts.GetWorldBoundariesCallback
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyAPIGatewayShortcuts.GetWorldBoundariesCallback
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback">
  
  
  <h1 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback" class="text-break">Class MyAPIGatewayShortcuts.GetWorldBoundariesCallback
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyAPIGatewayShortcuts.GetWorldBoundariesCallback</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class GetWorldBoundariesCallback : MulticastDelegate</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback__ctor_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.#ctor*"></a>
  <h4 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback__ctor_System_Object_System_IntPtr_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.#ctor(System.Object,System.IntPtr)">GetWorldBoundariesCallback(Object, IntPtr)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public GetWorldBoundariesCallback(object object, IntPtr method)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">object</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.IntPtr</span></td>
        <td><span class="parametername">method</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_BeginInvoke_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.BeginInvoke*"></a>
  <h4 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_BeginInvoke_System_AsyncCallback_System_Object_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.BeginInvoke(System.AsyncCallback,System.Object)">BeginInvoke(AsyncCallback, Object)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual IAsyncResult BeginInvoke(AsyncCallback callback, object object)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.AsyncCallback</span></td>
        <td><span class="parametername">callback</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">object</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.IAsyncResult</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_EndInvoke_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.EndInvoke*"></a>
  <h4 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_EndInvoke_System_IAsyncResult_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.EndInvoke(System.IAsyncResult)">EndInvoke(IAsyncResult)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual BoundingBoxD EndInvoke(IAsyncResult result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.IAsyncResult</span></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_Invoke_" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.Invoke*"></a>
  <h4 id="VRage_ModAPI_MyAPIGatewayShortcuts_GetWorldBoundariesCallback_Invoke" data-uid="VRage.ModAPI.MyAPIGatewayShortcuts.GetWorldBoundariesCallback.Invoke">Invoke()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual BoundingBoxD Invoke()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
