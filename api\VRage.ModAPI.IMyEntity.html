﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyEntity
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyEntity
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyEntity">
  
  
  <h1 id="VRage_ModAPI_IMyEntity" data-uid="VRage.ModAPI.IMyEntity" class="text-break">Interface IMyEntity
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_GetPosition">IMyEntity.GetPosition()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_Components">IMyEntity.Components</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_HasInventory">IMyEntity.HasInventory</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_InventoryCount">IMyEntity.InventoryCount</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_Closed">IMyEntity.Closed</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldAABB">IMyEntity.WorldAABB</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldAABBHr">IMyEntity.WorldAABBHr</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldVolume">IMyEntity.WorldVolume</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldVolumeHr">IMyEntity.WorldVolumeHr</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyEntity_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyEntity : IMyEntity</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ModAPI_IMyEntity_CastShadows_" data-uid="VRage.ModAPI.IMyEntity.CastShadows*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_CastShadows" data-uid="VRage.ModAPI.IMyEntity.CastShadows">CastShadows</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ObjectBuilders.MyPersistentEntityFlags2.html#VRage_ObjectBuilders_MyPersistentEntityFlags2_CastShadows">CastShadows</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool CastShadows { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_DebugAsyncLoading_" data-uid="VRage.ModAPI.IMyEntity.DebugAsyncLoading*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_DebugAsyncLoading" data-uid="VRage.ModAPI.IMyEntity.DebugAsyncLoading">DebugAsyncLoading</h4>
  <div class="markdown level1 summary"><p>Used for internal usage. Modders should not use it. Will be eventually removed</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool DebugAsyncLoading { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_DisplayName_" data-uid="VRage.ModAPI.IMyEntity.DisplayName*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_DisplayName" data-uid="VRage.ModAPI.IMyEntity.DisplayName">DisplayName</h4>
  <div class="markdown level1 summary"><p>Gets or sets user friendly name of entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">string DisplayName { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_EntityId_" data-uid="VRage.ModAPI.IMyEntity.EntityId*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_EntityId" data-uid="VRage.ModAPI.IMyEntity.EntityId">EntityId</h4>
  <div class="markdown level1 summary"><p>Uniq id of entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">long EntityId { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_EntityId_seealso">See Also</h5>
  <div class="seealso">
      <div><a class="xref" href="VRage.ModAPI.IMyEntities.html">IMyEntities</a></div>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_FastCastShadowResolve_" data-uid="VRage.ModAPI.IMyEntity.FastCastShadowResolve*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_FastCastShadowResolve" data-uid="VRage.ModAPI.IMyEntity.FastCastShadowResolve">FastCastShadowResolve</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ObjectBuilders.MyPersistentEntityFlags2.html#VRage_ObjectBuilders_MyPersistentEntityFlags2_CastShadows">CastShadows</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool FastCastShadowResolve { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Flags_" data-uid="VRage.ModAPI.IMyEntity.Flags*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Flags" data-uid="VRage.ModAPI.IMyEntity.Flags">Flags</h4>
  <div class="markdown level1 summary"><p>Gets or set some behavior of entity. <a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">EntityFlags Flags { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.EntityFlags.html">EntityFlags</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GameLogic_" data-uid="VRage.ModAPI.IMyEntity.GameLogic*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GameLogic" data-uid="VRage.ModAPI.IMyEntity.GameLogic">GameLogic</h4>
  <div class="markdown level1 summary"><p>Gets or sets game logic for object.
If there is more than 1 game logic attached it should be wrapped into <strong>MyCompositeGameLogicComponent</strong>.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyEntityComponentBase GameLogic { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyEntityComponentBase.html">MyEntityComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_GameLogic_seealso">See Also</h5>
  <div class="seealso">
      <div><a class="xref" href="VRage.Game.Components.MyGameLogicComponent.html">MyGameLogicComponent</a></div>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_Hierarchy_" data-uid="VRage.ModAPI.IMyEntity.Hierarchy*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Hierarchy" data-uid="VRage.ModAPI.IMyEntity.Hierarchy">Hierarchy</h4>
  <div class="markdown level1 summary"><p>Gets or sets Hierarchy component</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyHierarchyComponentBase Hierarchy { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyHierarchyComponentBase.html">MyHierarchyComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_InScene_" data-uid="VRage.ModAPI.IMyEntity.InScene*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_InScene" data-uid="VRage.ModAPI.IMyEntity.InScene">InScene</h4>
  <div class="markdown level1 summary"><p>Gets or set if grid is InScene. Objects that are not in scene are not updated and drawn.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool InScene { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_InvalidateOnMove_" data-uid="VRage.ModAPI.IMyEntity.InvalidateOnMove*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_InvalidateOnMove" data-uid="VRage.ModAPI.IMyEntity.InvalidateOnMove">InvalidateOnMove</h4>
  <div class="markdown level1 summary"><p>Gets if entity is invalidated on move
When visual look of entity depends on position - then InvalidateOnMove should be true</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool InvalidateOnMove { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_IsCCDForProjectiles_" data-uid="VRage.ModAPI.IMyEntity.IsCCDForProjectiles*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_IsCCDForProjectiles" data-uid="VRage.ModAPI.IMyEntity.IsCCDForProjectiles">IsCCDForProjectiles</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsCCDForProjectiles { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_IsVolumetric_" data-uid="VRage.ModAPI.IMyEntity.IsVolumetric*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_IsVolumetric" data-uid="VRage.ModAPI.IMyEntity.IsVolumetric">IsVolumetric</h4>
  <div class="markdown level1 summary"><p>Always returns false</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsVolumetric { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocalAABB_" data-uid="VRage.ModAPI.IMyEntity.LocalAABB*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocalAABB" data-uid="VRage.ModAPI.IMyEntity.LocalAABB">LocalAABB</h4>
  <div class="markdown level1 summary"><p>Gets or sets local axis aligned bounding box. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalAABBHr">LocalAABBHr</a>, <a class="xref" href="VRage.Game.Components.MyPositionComponentBase.html#VRage_Game_Components_MyPositionComponentBase_LocalAABB">LocalAABB</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">BoundingBox LocalAABB { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocalAABBHr_" data-uid="VRage.ModAPI.IMyEntity.LocalAABBHr*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocalAABBHr" data-uid="VRage.ModAPI.IMyEntity.LocalAABBHr">LocalAABBHr</h4>
  <div class="markdown level1 summary"><p>Gets local axis aligned bounding box. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalAABB">LocalAABB</a>, <a class="xref" href="VRage.Game.Components.MyPositionComponentBase.html#VRage_Game_Components_MyPositionComponentBase_LocalAABB">LocalAABB</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">BoundingBox LocalAABBHr { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocalMatrix_" data-uid="VRage.ModAPI.IMyEntity.LocalMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocalMatrix" data-uid="VRage.ModAPI.IMyEntity.LocalMatrix">LocalMatrix</h4>
  <div class="markdown level1 summary"><p>Gets or sets local matrix.
When entity, has parent, it's world coordinates are calculated from localMatrix and parent world matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Matrix LocalMatrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocalVolume_" data-uid="VRage.ModAPI.IMyEntity.LocalVolume*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocalVolume" data-uid="VRage.ModAPI.IMyEntity.LocalVolume">LocalVolume</h4>
  <div class="markdown level1 summary"><p>Gets or sets local volume. Same as <a class="xref" href="VRage.Game.Components.MyPositionComponentBase.html#VRage_Game_Components_MyPositionComponentBase_LocalVolume">LocalVolume</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">BoundingSphere LocalVolume { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocalVolumeOffset_" data-uid="VRage.ModAPI.IMyEntity.LocalVolumeOffset*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocalVolumeOffset" data-uid="VRage.ModAPI.IMyEntity.LocalVolumeOffset">LocalVolumeOffset</h4>
  <div class="markdown level1 summary"><p>Gets or sets local volume offset. Same as <a class="xref" href="VRage.Game.Components.MyPositionComponentBase.html#VRage_Game_Components_MyPositionComponentBase_LocalVolumeOffset">LocalVolumeOffset</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3 LocalVolumeOffset { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_LocationForHudMarker_" data-uid="VRage.ModAPI.IMyEntity.LocationForHudMarker*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_LocationForHudMarker" data-uid="VRage.ModAPI.IMyEntity.LocationForHudMarker">LocationForHudMarker</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3D LocationForHudMarker { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_MarkedForClose_" data-uid="VRage.ModAPI.IMyEntity.MarkedForClose*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_MarkedForClose" data-uid="VRage.ModAPI.IMyEntity.MarkedForClose">MarkedForClose</h4>
  <div class="markdown level1 summary"><p>Checked if <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a> was called</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool MarkedForClose { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_MaxGlassDistSq_" data-uid="VRage.ModAPI.IMyEntity.MaxGlassDistSq*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_MaxGlassDistSq" data-uid="VRage.ModAPI.IMyEntity.MaxGlassDistSq">MaxGlassDistSq</h4>
  <div class="markdown level1 summary"><p>Not used in game anymore</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float MaxGlassDistSq { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Model_" data-uid="VRage.ModAPI.IMyEntity.Model*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Model" data-uid="VRage.ModAPI.IMyEntity.Model">Model</h4>
  <div class="markdown level1 summary"><p>Gets model of block</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyModel Model { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyModel.html">IMyModel</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_ModelCollision_" data-uid="VRage.ModAPI.IMyEntity.ModelCollision*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_ModelCollision" data-uid="VRage.ModAPI.IMyEntity.ModelCollision">ModelCollision</h4>
  <div class="markdown level1 summary"><p>Gets collision model of block</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyModel ModelCollision { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyModel.html">IMyModel</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Name_" data-uid="VRage.ModAPI.IMyEntity.Name*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Name" data-uid="VRage.ModAPI.IMyEntity.Name">Name</h4>
  <div class="markdown level1 summary"><p>Uniq name of entity. Can be used to find entity by name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">string Name { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NearFlag_" data-uid="VRage.ModAPI.IMyEntity.NearFlag*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NearFlag" data-uid="VRage.ModAPI.IMyEntity.NearFlag">NearFlag</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_Near">Near</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NearFlag { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NeedsDraw_" data-uid="VRage.ModAPI.IMyEntity.NeedsDraw*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NeedsDraw" data-uid="VRage.ModAPI.IMyEntity.NeedsDraw">NeedsDraw</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_NeedsDraw">NeedsDraw</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedsDraw { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NeedsDrawFromParent_" data-uid="VRage.ModAPI.IMyEntity.NeedsDrawFromParent*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NeedsDrawFromParent" data-uid="VRage.ModAPI.IMyEntity.NeedsDrawFromParent">NeedsDrawFromParent</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_NeedsDrawFromParent">NeedsDrawFromParent</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedsDrawFromParent { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NeedsResolveCastShadow_" data-uid="VRage.ModAPI.IMyEntity.NeedsResolveCastShadow*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NeedsResolveCastShadow" data-uid="VRage.ModAPI.IMyEntity.NeedsResolveCastShadow">NeedsResolveCastShadow</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_NeedsResolveCastShadow">NeedsResolveCastShadow</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedsResolveCastShadow { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NeedsUpdate_" data-uid="VRage.ModAPI.IMyEntity.NeedsUpdate*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NeedsUpdate" data-uid="VRage.ModAPI.IMyEntity.NeedsUpdate">NeedsUpdate</h4>
  <div class="markdown level1 summary"><p>Gets or sets how often the entity should be updated.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyEntityUpdateEnum NeedsUpdate { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.MyEntityUpdateEnum.html">MyEntityUpdateEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_NeedsWorldMatrix_" data-uid="VRage.ModAPI.IMyEntity.NeedsWorldMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_NeedsWorldMatrix" data-uid="VRage.ModAPI.IMyEntity.NeedsWorldMatrix">NeedsWorldMatrix</h4>
  <div class="markdown level1 summary"><p>Gets or sets if <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrix">WorldMatrix</a> should be calculated when parent WorldMatrix is changed.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedsWorldMatrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_NeedsWorldMatrix_remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Enabling it on big amount entities may lower simulation speed</p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntity_Parent_" data-uid="VRage.ModAPI.IMyEntity.Parent*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Parent" data-uid="VRage.ModAPI.IMyEntity.Parent">Parent</h4>
  <div class="markdown level1 summary"><p>Gets parent of entity or null, if this entity doesn't have parent.
Ex: character sitting in cockpit, has parent - cockpit, cockpit has parent - cube grid, connected grids also has main grid, which would be parent for other grids.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity Parent { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_PersistentFlags_" data-uid="VRage.ModAPI.IMyEntity.PersistentFlags*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_PersistentFlags" data-uid="VRage.ModAPI.IMyEntity.PersistentFlags">PersistentFlags</h4>
  <div class="markdown level1 summary"><p>Gets or sets persistent flags that are used in rendering.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyPersistentEntityFlags2 PersistentFlags { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyPersistentEntityFlags2.html">MyPersistentEntityFlags2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Physics_" data-uid="VRage.ModAPI.IMyEntity.Physics*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Physics" data-uid="VRage.ModAPI.IMyEntity.Physics">Physics</h4>
  <div class="markdown level1 summary"><p>Gets or sets physics for object</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyPhysicsComponentBase Physics { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyPhysicsComponentBase.html">MyPhysicsComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_PositionComp_" data-uid="VRage.ModAPI.IMyEntity.PositionComp*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_PositionComp" data-uid="VRage.ModAPI.IMyEntity.PositionComp">PositionComp</h4>
  <div class="markdown level1 summary"><p>Gets or sets position provider logic</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyPositionComponentBase PositionComp { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyPositionComponentBase.html">MyPositionComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Render_" data-uid="VRage.ModAPI.IMyEntity.Render*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Render" data-uid="VRage.ModAPI.IMyEntity.Render">Render</h4>
  <div class="markdown level1 summary"><p>Gets or sets render logic</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyRenderComponentBase Render { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html">MyRenderComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Save_" data-uid="VRage.ModAPI.IMyEntity.Save*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Save" data-uid="VRage.ModAPI.IMyEntity.Save">Save</h4>
  <div class="markdown level1 summary"><p>Gets or sets <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_Save">Save</a>. Entity won't be saved if <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Save">Save</a> is false</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Save { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_ShadowBoxLod_" data-uid="VRage.ModAPI.IMyEntity.ShadowBoxLod*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_ShadowBoxLod" data-uid="VRage.ModAPI.IMyEntity.ShadowBoxLod">ShadowBoxLod</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_ShadowBoxLod">ShadowBoxLod</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool ShadowBoxLod { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SkipIfTooSmall_" data-uid="VRage.ModAPI.IMyEntity.SkipIfTooSmall*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SkipIfTooSmall" data-uid="VRage.ModAPI.IMyEntity.SkipIfTooSmall">SkipIfTooSmall</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.ModAPI.EntityFlags.html#VRage_ModAPI_EntityFlags_SkipIfTooSmall">SkipIfTooSmall</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool SkipIfTooSmall { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_StopPhysicsActivation_" data-uid="VRage.ModAPI.IMyEntity.StopPhysicsActivation*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_StopPhysicsActivation" data-uid="VRage.ModAPI.IMyEntity.StopPhysicsActivation">StopPhysicsActivation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool StopPhysicsActivation { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Storage_" data-uid="VRage.ModAPI.IMyEntity.Storage*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Storage" data-uid="VRage.ModAPI.IMyEntity.Storage">Storage</h4>
  <div class="markdown level1 summary"><p>Custom storage for mods. Shared with all mods.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyModStorageComponentBase Storage { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MyModStorageComponentBase.html">MyModStorageComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_Storage_remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Not synced, but saved with blueprints.
Only use set accessor if value is null.</p>
<pre><code> Entity.Storage = new MyModStorageComponent(); </code></pre>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntity_Synchronized_" data-uid="VRage.ModAPI.IMyEntity.Synchronized*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Synchronized" data-uid="VRage.ModAPI.IMyEntity.Synchronized">Synchronized</h4>
  <div class="markdown level1 summary"><p>Gets or sets if the entity should be synced.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Synchronized { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SyncObject_" data-uid="VRage.ModAPI.IMyEntity.SyncObject*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SyncObject" data-uid="VRage.ModAPI.IMyEntity.SyncObject">SyncObject</h4>
  <div class="markdown level1 summary"><p>Gets SyncObject used for synchronizing data over network with <strong>VRage.Sync.Sync</strong></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MySyncComponentBase SyncObject { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.MySyncComponentBase.html">MySyncComponentBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Transparent_" data-uid="VRage.ModAPI.IMyEntity.Transparent*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Transparent" data-uid="VRage.ModAPI.IMyEntity.Transparent">Transparent</h4>
  <div class="markdown level1 summary"><p>Gets or sets <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_Transparency">Transparency</a>. When setting true entity would be 25% transparent</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Transparent { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Visible_" data-uid="VRage.ModAPI.IMyEntity.Visible*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Visible" data-uid="VRage.ModAPI.IMyEntity.Visible">Visible</h4>
  <div class="markdown level1 summary"><p>Gets or sets flag <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_Visible">Visible</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Visible { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_WorldMatrix_" data-uid="VRage.ModAPI.IMyEntity.WorldMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_WorldMatrix" data-uid="VRage.ModAPI.IMyEntity.WorldMatrix">WorldMatrix</h4>
  <div class="markdown level1 summary"><p>Gets or sets world matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD WorldMatrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_WorldMatrix_seealso">See Also</h5>
  <div class="seealso">
      <div><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></div>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_WorldMatrixInvScaled_" data-uid="VRage.ModAPI.IMyEntity.WorldMatrixInvScaled*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_WorldMatrixInvScaled" data-uid="VRage.ModAPI.IMyEntity.WorldMatrixInvScaled">WorldMatrixInvScaled</h4>
  <div class="markdown level1 summary"><p>Get scaled, inverted world matrix. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetViewMatrix">GetViewMatrix()</a>, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetWorldMatrixNormalizedInv">GetWorldMatrixNormalizedInv()</a>, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv">WorldMatrixNormalizedInv</a>, but not normalized</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD WorldMatrixInvScaled { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><p>Matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv_" data-uid="VRage.ModAPI.IMyEntity.WorldMatrixNormalizedInv*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv" data-uid="VRage.ModAPI.IMyEntity.WorldMatrixNormalizedInv">WorldMatrixNormalizedInv</h4>
  <div class="markdown level1 summary"><p>Get normalized, inverted world matrix. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetViewMatrix">GetViewMatrix()</a>, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv">WorldMatrixNormalizedInv</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD WorldMatrixNormalizedInv { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><p>Matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyEntity_AddToGamePruningStructure_" data-uid="VRage.ModAPI.IMyEntity.AddToGamePruningStructure*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_AddToGamePruningStructure" data-uid="VRage.ModAPI.IMyEntity.AddToGamePruningStructure">AddToGamePruningStructure()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void AddToGamePruningStructure()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_BeforeSave_" data-uid="VRage.ModAPI.IMyEntity.BeforeSave*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_BeforeSave" data-uid="VRage.ModAPI.IMyEntity.BeforeSave">BeforeSave()</h4>
  <div class="markdown level1 summary"><p>Called before method GetObjectBuilder, when saving sector</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void BeforeSave()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_Close_" data-uid="VRage.ModAPI.IMyEntity.Close*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Close" data-uid="VRage.ModAPI.IMyEntity.Close">Close()</h4>
  <div class="markdown level1 summary"><p>This method marks this entity for close which means, that Close
will be called after all entities are updated</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Close()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_DebugDraw_" data-uid="VRage.ModAPI.IMyEntity.DebugDraw*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_DebugDraw" data-uid="VRage.ModAPI.IMyEntity.DebugDraw">DebugDraw()</h4>
  <div class="markdown level1 summary"><p>Calls debug draw of entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void DebugDraw()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_DebugDrawInvalidTriangles_" data-uid="VRage.ModAPI.IMyEntity.DebugDrawInvalidTriangles*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_DebugDrawInvalidTriangles" data-uid="VRage.ModAPI.IMyEntity.DebugDrawInvalidTriangles">DebugDrawInvalidTriangles()</h4>
  <div class="markdown level1 summary"><p>Calls special debug draw, that highlighting invalid triangles in model</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void DebugDrawInvalidTriangles()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_Delete_" data-uid="VRage.ModAPI.IMyEntity.Delete*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Delete" data-uid="VRage.ModAPI.IMyEntity.Delete">Delete()</h4>
  <div class="markdown level1 summary"><p>Performs real cleaning of entity. Should be called by game. Modders should prefer <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a> method.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Delete()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_DoOverlapSphereTest_" data-uid="VRage.ModAPI.IMyEntity.DoOverlapSphereTest*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_DoOverlapSphereTest_System_Single_VRageMath_Vector3D_" data-uid="VRage.ModAPI.IMyEntity.DoOverlapSphereTest(System.Single,VRageMath.Vector3D)">DoOverlapSphereTest(Single, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Checks if intersects Works only with <a class="xref" href="VRage.ModAPI.IMyVoxelBase.html">IMyVoxelBase</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool DoOverlapSphereTest(float sphereRadius, Vector3D spherePos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">sphereRadius</span></td>
        <td><p>Radius of sphere</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">spherePos</span></td>
        <td><p>World position</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if intersects</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_EnableColorMaskForSubparts_" data-uid="VRage.ModAPI.IMyEntity.EnableColorMaskForSubparts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_EnableColorMaskForSubparts_System_Boolean_" data-uid="VRage.ModAPI.IMyEntity.EnableColorMaskForSubparts(System.Boolean)">EnableColorMaskForSubparts(Boolean)</h4>
  <div class="markdown level1 summary"><p>Allows subparts have different color than their parent</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void EnableColorMaskForSubparts(bool enable)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">enable</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetChildren_" data-uid="VRage.ModAPI.IMyEntity.GetChildren*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetChildren_System_Collections_Generic_List_VRage_ModAPI_IMyEntity__System_Func_VRage_ModAPI_IMyEntity_System_Boolean__" data-uid="VRage.ModAPI.IMyEntity.GetChildren(System.Collections.Generic.List{VRage.ModAPI.IMyEntity},System.Func{VRage.ModAPI.IMyEntity,System.Boolean})">GetChildren(List&lt;IMyEntity&gt;, Func&lt;IMyEntity, Boolean&gt;)</h4>
  <div class="markdown level1 summary"><p>Gets children of entity. Child - entity, who's <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Parent">Parent</a> is this entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void GetChildren(List&lt;IMyEntity&gt; children, Func&lt;IMyEntity, bool&gt; collect = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><span class="parametername">children</span></td>
        <td><p>List, that would receive results</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">collect</span></td>
        <td><p>When returns true - child added to list</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetDiffuseColor_" data-uid="VRage.ModAPI.IMyEntity.GetDiffuseColor*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetDiffuseColor" data-uid="VRage.ModAPI.IMyEntity.GetDiffuseColor">GetDiffuseColor()</h4>
  <div class="markdown level1 summary"><p>Gets render diffuse color</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3 GetDiffuseColor()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><p>Diffuse color</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndBoundingSphere_" data-uid="VRage.ModAPI.IMyEntity.GetDistanceBetweenCameraAndBoundingSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndBoundingSphere" data-uid="VRage.ModAPI.IMyEntity.GetDistanceBetweenCameraAndBoundingSphere">GetDistanceBetweenCameraAndBoundingSphere()</h4>
  <div class="markdown level1 summary"><p>Distance from camera to bounding sphere of this phys object. Result is always positive, even if camera is inside the sphere. (in meters)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float GetDistanceBetweenCameraAndBoundingSphere()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><p>Distance in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndPosition_" data-uid="VRage.ModAPI.IMyEntity.GetDistanceBetweenCameraAndPosition*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndPosition" data-uid="VRage.ModAPI.IMyEntity.GetDistanceBetweenCameraAndPosition">GetDistanceBetweenCameraAndPosition()</h4>
  <div class="markdown level1 summary"><p>Distance from camera to position of entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float GetDistanceBetweenCameraAndPosition()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><p>Distance in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetFriendlyName_" data-uid="VRage.ModAPI.IMyEntity.GetFriendlyName*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetFriendlyName" data-uid="VRage.ModAPI.IMyEntity.GetFriendlyName">GetFriendlyName()</h4>
  <div class="markdown level1 summary"><p>Not used. Actually not a friendly name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">string GetFriendlyName()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><p>String.Empty or &quot;MyVoxelMap&quot;</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetIntersectionWithAABB_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithAABB*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetIntersectionWithAABB_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithAABB(VRageMath.BoundingBoxD@)">GetIntersectionWithAABB(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Return true if object intersects specified bounding box.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool GetIntersectionWithAABB(ref BoundingBoxD aabb)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">aabb</span></td>
        <td><p>Bounding box to check</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if intersects</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetIntersectionWithLine_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithLine*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetIntersectionWithLine_VRageMath_LineD__System_Nullable_VRage_Game_Models_MyIntersectionResultLineTriangleEx___VRage_Game_Components_IntersectionFlags_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithLine(VRageMath.LineD@,System.Nullable{VRage.Game.Models.MyIntersectionResultLineTriangleEx}@,VRage.Game.Components.IntersectionFlags)">GetIntersectionWithLine(ref LineD, out Nullable&lt;MyIntersectionResultLineTriangleEx&gt;, IntersectionFlags)</h4>
  <div class="markdown level1 summary"><p>Get intersection of model with provided line</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool GetIntersectionWithLine(ref LineD line, out Nullable&lt;MyIntersectionResultLineTriangleEx&gt; tri, IntersectionFlags flags)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td><p>Line that should intersect model</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">VRage.Game.Models.MyIntersectionResultLineTriangleEx</span>&gt;</td>
        <td><span class="parametername">tri</span></td>
        <td><p>Returns model first triangle that intersects</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.Components.IntersectionFlags.html">IntersectionFlags</a></td>
        <td><span class="parametername">flags</span></td>
        <td><p>Mode of work</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True when line intersects models</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetIntersectionWithLineAndBoundingSphere_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithLineAndBoundingSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetIntersectionWithLineAndBoundingSphere_VRageMath_LineD__System_Single_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithLineAndBoundingSphere(VRageMath.LineD@,System.Single)">GetIntersectionWithLineAndBoundingSphere(ref LineD, Single)</h4>
  <div class="markdown level1 summary"><p>Calculates intersection of line with any bounding sphere in this model instance. Center of the bounding sphere will be returned.
It takes boundingSphereRadiusMultiplier argument which serves for extending the influence (radius) for interaction with line.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Nullable&lt;Vector3D&gt; GetIntersectionWithLineAndBoundingSphere(ref LineD line, float boundingSphereRadiusMultiplier)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td><p>Line to check</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">boundingSphereRadiusMultiplier</span></td>
        <td><p>Bounding sphere radius would be multiplied by this value</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><p>Position of intersection if of line and bounding sphere</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetIntersectionWithSphere_" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetIntersectionWithSphere_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyEntity.GetIntersectionWithSphere(VRageMath.BoundingSphereD@)">GetIntersectionWithSphere(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Return true if object intersects specified sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool GetIntersectionWithSphere(ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to check</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if intersects</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetInventory_" data-uid="VRage.ModAPI.IMyEntity.GetInventory*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetInventory" data-uid="VRage.ModAPI.IMyEntity.GetInventory">GetInventory()</h4>
  <div class="markdown level1 summary"><p>Simply get the MyInventoryBase component stored in this entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyInventory GetInventory()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyInventory.html">IMyInventory</a></td>
        <td><p>Null, or first inventory</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetInventory_" data-uid="VRage.ModAPI.IMyEntity.GetInventory*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetInventory_System_Int32_" data-uid="VRage.ModAPI.IMyEntity.GetInventory(System.Int32)">GetInventory(Int32)</h4>
  <div class="markdown level1 summary"><p>Search for inventory component with matching index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyInventory GetInventory(int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td><p>Index of inventory, starting from 0</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyInventory.html">IMyInventory</a></td>
        <td><p>Null, or inventory at matching index</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetLargestDistanceBetweenCameraAndBoundingSphere_" data-uid="VRage.ModAPI.IMyEntity.GetLargestDistanceBetweenCameraAndBoundingSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetLargestDistanceBetweenCameraAndBoundingSphere" data-uid="VRage.ModAPI.IMyEntity.GetLargestDistanceBetweenCameraAndBoundingSphere">GetLargestDistanceBetweenCameraAndBoundingSphere()</h4>
  <div class="markdown level1 summary"><p>Largest distance from camera to bounding sphere of this phys object. Result is always positive, even if camera is inside the sphere.
It's actually distance between camera and opposite side of the sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float GetLargestDistanceBetweenCameraAndBoundingSphere()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><p>Distance in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetObjectBuilder_" data-uid="VRage.ModAPI.IMyEntity.GetObjectBuilder*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetObjectBuilder_System_Boolean_" data-uid="VRage.ModAPI.IMyEntity.GetObjectBuilder(System.Boolean)">GetObjectBuilder(Boolean)</h4>
  <div class="markdown level1 summary"><p>Returns object builder - object representing block state, and allows restore it. Used in game save, or syncing over network.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyObjectBuilder_EntityBase GetObjectBuilder(bool copy = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">copy</span></td>
        <td><p>When true, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a> won't be saved. Copy true comes only from MyGridClipboard/MyFloatingObjectClipboard/MyVoxelClipboard</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><p>Object builder</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetSmallestDistanceBetweenCameraAndBoundingSphere_" data-uid="VRage.ModAPI.IMyEntity.GetSmallestDistanceBetweenCameraAndBoundingSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetSmallestDistanceBetweenCameraAndBoundingSphere" data-uid="VRage.ModAPI.IMyEntity.GetSmallestDistanceBetweenCameraAndBoundingSphere">GetSmallestDistanceBetweenCameraAndBoundingSphere()</h4>
  <div class="markdown level1 summary"><p>Smallest distance between camera and bounding sphere of this phys object. Result is always positive, even if camera is inside the sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float GetSmallestDistanceBetweenCameraAndBoundingSphere()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><p>Distance in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetSubpart_" data-uid="VRage.ModAPI.IMyEntity.GetSubpart*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetSubpart_System_String_" data-uid="VRage.ModAPI.IMyEntity.GetSubpart(System.String)">GetSubpart(String)</h4>
  <div class="markdown level1 summary"><p>Gets subpart by subpart name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyEntitySubpart GetSubpart(string name)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p>Name of subpart. Keep in mind that subpart names, should not start with <code>subpart_</code></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.Entity.MyEntitySubpart.html">MyEntitySubpart</a></td>
        <td><p>Subpart if it is found, or crashes if subpart not found</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_GetSubpart_System_String__remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>If you press Alt+11, enable <code>Debug draw</code> and <code>Model dummies</code> then you'll see all subpart names.</p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetTopMostParent_" data-uid="VRage.ModAPI.IMyEntity.GetTopMostParent*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetTopMostParent_System_Type_" data-uid="VRage.ModAPI.IMyEntity.GetTopMostParent(System.Type)">GetTopMostParent(Type)</h4>
  <div class="markdown level1 summary"><p>Gets top most <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Parent">Parent</a> of specified type.
Top most is entity that doesn't have parent (of specified type)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetTopMostParent(Type type = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td><p>Type of parent. When type is null, type check disabled</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetTrianglesIntersectingSphere_" data-uid="VRage.ModAPI.IMyEntity.GetTrianglesIntersectingSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetTrianglesIntersectingSphere_VRageMath_BoundingSphere__System_Nullable_VRageMath_Vector3__System_Nullable_System_Single__System_Collections_Generic_List_VRage_Utils_MyTriangle_Vertex_Normals__System_Int32_" data-uid="VRage.ModAPI.IMyEntity.GetTrianglesIntersectingSphere(VRageMath.BoundingSphere@,System.Nullable{VRageMath.Vector3},System.Nullable{System.Single},System.Collections.Generic.List{VRage.Utils.MyTriangle_Vertex_Normals},System.Int32)">GetTrianglesIntersectingSphere(ref BoundingSphere, Nullable&lt;Vector3&gt;, Nullable&lt;Single&gt;, List&lt;MyTriangle_Vertex_Normals&gt;, Int32)</h4>
  <div class="markdown level1 summary"><p>Return list of triangles intersecting specified sphere. Angle between every triangleVertexes normal vector and 'referenceNormalVector'
is calculated, and if more than 'maxAngle', we ignore such triangleVertexes.
Triangles are returned in 'retTriangles', and this list must be preallocated!
IMPORTANT: Sphere must be in model space, so don't transform it!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void GetTrianglesIntersectingSphere(ref BoundingSphere sphere, Nullable&lt;Vector3&gt; referenceNormalVector, Nullable&lt;float&gt; maxAngle, List&lt;MyTriangle_Vertex_Normals&gt; retTriangles, int maxNeighbourTriangles)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to check</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">referenceNormalVector</span></td>
        <td><p>Used in filtering triangles</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">maxAngle</span></td>
        <td><p>Max angle between referenceNormalVector and every triangleVertex of model</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Utils.MyTriangle_Vertex_Normals.html">MyTriangle_Vertex_Normals</a>&gt;</td>
        <td><span class="parametername">retTriangles</span></td>
        <td><p>Triangles would be added here</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">maxNeighbourTriangles</span></td>
        <td><p>Limit of added triangles</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetViewMatrix_" data-uid="VRage.ModAPI.IMyEntity.GetViewMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetViewMatrix" data-uid="VRage.ModAPI.IMyEntity.GetViewMatrix">GetViewMatrix()</h4>
  <div class="markdown level1 summary"><p>Get normalized, inverted world matrix. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetWorldMatrixNormalizedInv">GetWorldMatrixNormalizedInv()</a>, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv">WorldMatrixNormalizedInv</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD GetViewMatrix()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><p>Matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_GetWorldMatrixNormalizedInv_" data-uid="VRage.ModAPI.IMyEntity.GetWorldMatrixNormalizedInv*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_GetWorldMatrixNormalizedInv" data-uid="VRage.ModAPI.IMyEntity.GetWorldMatrixNormalizedInv">GetWorldMatrixNormalizedInv()</h4>
  <div class="markdown level1 summary"><p>Get normalized, inverted world matrix. Same as <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetViewMatrix">GetViewMatrix()</a>, <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv">WorldMatrixNormalizedInv</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MatrixD GetWorldMatrixNormalizedInv()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><p>Matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_IsVisible_" data-uid="VRage.ModAPI.IMyEntity.IsVisible*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_IsVisible" data-uid="VRage.ModAPI.IMyEntity.IsVisible">IsVisible()</h4>
  <div class="markdown level1 summary"><p>Gets or result of function <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_IsVisible">IsVisible()</a>. Function inside check for <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_IsVisible_VRage_ModAPI_IMyEntity_">IsVisible(IMyEntity)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsVisible()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity should be drawn</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_OnAddedToScene_" data-uid="VRage.ModAPI.IMyEntity.OnAddedToScene*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_OnAddedToScene_System_Object_" data-uid="VRage.ModAPI.IMyEntity.OnAddedToScene(System.Object)">OnAddedToScene(Object)</h4>
  <div class="markdown level1 summary"><p>Adds entity to scene: init updates, render physics</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void OnAddedToScene(object source)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">source</span></td>
        <td><p>Object that triggered adding from scene</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_OnRemovedFromScene_" data-uid="VRage.ModAPI.IMyEntity.OnRemovedFromScene*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_OnRemovedFromScene_System_Object_" data-uid="VRage.ModAPI.IMyEntity.OnRemovedFromScene(System.Object)">OnRemovedFromScene(Object)</h4>
  <div class="markdown level1 summary"><p>Remove entity and it's children from scene: stops updates and render, deactivates physics. Usually called when entity deleted</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void OnRemovedFromScene(object source)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">source</span></td>
        <td><p>Object that triggered removing from scene</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_RemoveFromGamePruningStructure_" data-uid="VRage.ModAPI.IMyEntity.RemoveFromGamePruningStructure*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_RemoveFromGamePruningStructure" data-uid="VRage.ModAPI.IMyEntity.RemoveFromGamePruningStructure">RemoveFromGamePruningStructure()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemoveFromGamePruningStructure()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetColorMaskForSubparts_" data-uid="VRage.ModAPI.IMyEntity.SetColorMaskForSubparts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetColorMaskForSubparts_VRageMath_Vector3_" data-uid="VRage.ModAPI.IMyEntity.SetColorMaskForSubparts(VRageMath.Vector3)">SetColorMaskForSubparts(Vector3)</h4>
  <div class="markdown level1 summary"><p>Sets subparts custom col</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetColorMaskForSubparts(Vector3 colorMaskHsv)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">colorMaskHsv</span></td>
        <td><p>Color</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetEmissiveParts_" data-uid="VRage.ModAPI.IMyEntity.SetEmissiveParts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetEmissiveParts_System_String_VRageMath_Color_System_Single_" data-uid="VRage.ModAPI.IMyEntity.SetEmissiveParts(System.String,VRageMath.Color,System.Single)">SetEmissiveParts(String, Color, Single)</h4>
  <div class="markdown level1 summary"><p>Sets the emissive value of a specific emissive material on entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetEmissiveParts(string emissiveName, Color emissivePartColor, float emissivity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">emissiveName</span></td>
        <td><p>The name of the emissive material (ie. &quot;Emissive0&quot;)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">emissivePartColor</span></td>
        <td><p>Color to emit</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">emissivity</span></td>
        <td><p>Level of emissivity (0 is off, 1 is full brightness)</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetEmissivePartsForSubparts_" data-uid="VRage.ModAPI.IMyEntity.SetEmissivePartsForSubparts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetEmissivePartsForSubparts_System_String_VRageMath_Color_System_Single_" data-uid="VRage.ModAPI.IMyEntity.SetEmissivePartsForSubparts(System.String,VRageMath.Color,System.Single)">SetEmissivePartsForSubparts(String, Color, Single)</h4>
  <div class="markdown level1 summary"><p>Sets the emissive value of a specific emissive material on all entity subparts.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetEmissivePartsForSubparts(string emissiveName, Color emissivePartColor, float emissivity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">emissiveName</span></td>
        <td><p>The name of the emissive material (ie. &quot;Emissive0&quot;)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">emissivePartColor</span></td>
        <td><p>Color to emit</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">emissivity</span></td>
        <td><p>Level of emissivity (0 is off, 1 is full brightness).</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetLocalMatrix_" data-uid="VRage.ModAPI.IMyEntity.SetLocalMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetLocalMatrix_VRageMath_Matrix_System_Object_" data-uid="VRage.ModAPI.IMyEntity.SetLocalMatrix(VRageMath.Matrix,System.Object)">SetLocalMatrix(Matrix, Object)</h4>
  <div class="markdown level1 summary"><p>Sets local matrix.
When entity, has parent, it's world coordinates are calculated from localMatrix and parent world matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetLocalMatrix(Matrix localMatrix, object source = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">localMatrix</span></td>
        <td><p>New matrix</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">source</span></td>
        <td><p>Object that caused this event. Not used in anyway</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetPosition_" data-uid="VRage.ModAPI.IMyEntity.SetPosition*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetPosition_VRageMath_Vector3D_" data-uid="VRage.ModAPI.IMyEntity.SetPosition(VRageMath.Vector3D)">SetPosition(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Set WorldMatrix's <a class="xref" href="VRageMath.MatrixD.html#VRageMath_MatrixD_Translation">Translation</a>. Moves entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetPosition(Vector3D pos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">pos</span></td>
        <td><p>New position of entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_" data-uid="VRage.ModAPI.IMyEntity.SetTextureChangesForSubparts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_System_Collections_Generic_Dictionary_System_String_VRageRender_Messages_MyTextureChange__" data-uid="VRage.ModAPI.IMyEntity.SetTextureChangesForSubparts(System.Collections.Generic.Dictionary{System.String,VRageRender.Messages.MyTextureChange})">SetTextureChangesForSubparts(Dictionary&lt;String, MyTextureChange&gt;)</h4>
  <div class="markdown level1 summary"><p>This calling is obsolete, use another version.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetTextureChangesForSubparts(Dictionary&lt;string, MyTextureChange&gt; value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.Dictionary</span>&lt;<span class="xref">System.String</span>, <span class="xref">VRageRender.Messages.MyTextureChange</span>&gt;</td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_" data-uid="VRage.ModAPI.IMyEntity.SetTextureChangesForSubparts*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_System_Collections_Generic_Dictionary_VRage_Utils_MyStringId_VRageRender_Messages_MyTextureChange__" data-uid="VRage.ModAPI.IMyEntity.SetTextureChangesForSubparts(System.Collections.Generic.Dictionary{VRage.Utils.MyStringId,VRageRender.Messages.MyTextureChange})">SetTextureChangesForSubparts(Dictionary&lt;MyStringId, MyTextureChange&gt;)</h4>
  <div class="markdown level1 summary"><p>Sets subparts custom skinning. Copy values from <a class="xref" href="VRage.Game.Components.MyRenderComponentBase.html#VRage_Game_Components_MyRenderComponentBase_TextureChanges">TextureChanges</a>, then change needed keys.
You can retrieve values for exact skin with following code: <pre><code>MyDefinitionManager.Static.GetAssetModifierDefinitionForRender(skinId);</code></pre></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetTextureChangesForSubparts(Dictionary&lt;MyStringId, MyTextureChange&gt; value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.Dictionary</span>&lt;<a class="xref" href="VRage.Utils.MyStringId.html">MyStringId</a>, <span class="xref">VRageRender.Messages.MyTextureChange</span>&gt;</td>
        <td><span class="parametername">value</span></td>
        <td><p>Key - name of texture, value - path to texture files</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_SetWorldMatrix_" data-uid="VRage.ModAPI.IMyEntity.SetWorldMatrix*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_SetWorldMatrix_VRageMath_MatrixD_System_Object_" data-uid="VRage.ModAPI.IMyEntity.SetWorldMatrix(VRageMath.MatrixD,System.Object)">SetWorldMatrix(MatrixD, Object)</h4>
  <div class="markdown level1 summary"><p>Sets world matrix of entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetWorldMatrix(MatrixD worldMatrix, object source = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">worldMatrix</span></td>
        <td><p>New world matrix</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">source</span></td>
        <td><p>Object that triggered set of new matrix</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_Teleport_" data-uid="VRage.ModAPI.IMyEntity.Teleport*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_Teleport_VRageMath_MatrixD_System_Object_System_Boolean_" data-uid="VRage.ModAPI.IMyEntity.Teleport(VRageMath.MatrixD,System.Object,System.Boolean)">Teleport(MatrixD, Object, Boolean)</h4>
  <div class="markdown level1 summary"><p>When moving entity over large distances or when entity has children, using this method preferred over <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetPosition_VRageMath_Vector3D_">SetPosition(Vector3D)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void Teleport(MatrixD pos, object source = null, bool ignoreAssert = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">pos</span></td>
        <td><p>Teleport destination</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">source</span></td>
        <td><p>Object that triggered</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">ignoreAssert</span></td>
        <td><p>Do extra validation</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntity_TryGetSubpart_" data-uid="VRage.ModAPI.IMyEntity.TryGetSubpart*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_TryGetSubpart_System_String_VRage_Game_Entity_MyEntitySubpart__" data-uid="VRage.ModAPI.IMyEntity.TryGetSubpart(System.String,VRage.Game.Entity.MyEntitySubpart@)">TryGetSubpart(String, out MyEntitySubpart)</h4>
  <div class="markdown level1 summary"><p>Gets subpart by subpart name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool TryGetSubpart(string name, out MyEntitySubpart subpart)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p>Name of subpart. Keep in mind that subpart names, should not start with <code>subpart_</code></p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.Entity.MyEntitySubpart.html">MyEntitySubpart</a></td>
        <td><span class="parametername">subpart</span></td>
        <td><p>Subpart if it is found</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if subpart is found</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_TryGetSubpart_System_String_VRage_Game_Entity_MyEntitySubpart___remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>If you press Alt+11, enable <code>Debug draw</code> and <code>Model dummies</code> then you'll see all subpart names.</p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntity_UpdateGamePruningStructure_" data-uid="VRage.ModAPI.IMyEntity.UpdateGamePruningStructure*"></a>
  <h4 id="VRage_ModAPI_IMyEntity_UpdateGamePruningStructure" data-uid="VRage.ModAPI.IMyEntity.UpdateGamePruningStructure">UpdateGamePruningStructure()</h4>
  <div class="markdown level1 summary"><p>Update position of entity in MyGamePruningStructure. Calls: <pre><code>MyGamePruningStructure.Move(this)</code></pre></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void UpdateGamePruningStructure()</code></pre>
  </div>
  <h3 id="events">Events
  </h3>
  
  
  <h4 id="VRage_ModAPI_IMyEntity_OnClose" data-uid="VRage.ModAPI.IMyEntity.OnClose">OnClose</h4>
  <div class="markdown level1 summary"><p>Called when <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a> is called. Order 1) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> 2) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a> 3)  <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClose">OnClose</a>.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnClose</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_OnClose_remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Modders should prefer <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> or <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a></p>
</div>
  
  
  <h4 id="VRage_ModAPI_IMyEntity_OnClosing" data-uid="VRage.ModAPI.IMyEntity.OnClosing">OnClosing</h4>
  <div class="markdown level1 summary"><p>Called when <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a> is called. Order 1) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> 2) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a> 3)  <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClose">OnClose</a>.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnClosing</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_OnClosing_remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Modders should prefer <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> or <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a></p>
</div>
  
  
  <h4 id="VRage_ModAPI_IMyEntity_OnMarkForClose" data-uid="VRage.ModAPI.IMyEntity.OnMarkForClose">OnMarkForClose</h4>
  <div class="markdown level1 summary"><p>Called when <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a> is called. Order 1) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> 2) <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a> 3)  <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClose">OnClose</a>.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnMarkForClose</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntity_OnMarkForClose_remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Modders should prefer <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">OnMarkForClose</a> or <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">OnClosing</a>. This event may not be invoked at all, when calling MyEntities.CloseAll, marking is bypassed</p>
</div>
  
  
  <h4 id="VRage_ModAPI_IMyEntity_OnPhysicsChanged" data-uid="VRage.ModAPI.IMyEntity.OnPhysicsChanged">OnPhysicsChanged</h4>
  <div class="markdown level1 summary"><p>Called when havok rigid body physics are changed: inited, closed, modified.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnPhysicsChanged</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
