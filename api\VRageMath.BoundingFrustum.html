﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingFrustum
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingFrustum
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingFrustum">
  
  
  <h1 id="VRageMath_BoundingFrustum" data-uid="VRageMath.BoundingFrustum" class="text-break">Class BoundingFrustum
  </h1>
  <div class="markdown level0 summary"><p>Defines a frustum and helps determine whether forms intersect with it.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingFrustum</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingFrustum_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class BoundingFrustum : Object, IEquatable&lt;BoundingFrustum&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingFrustum__ctor_" data-uid="VRageMath.BoundingFrustum.#ctor*"></a>
  <h4 id="VRageMath_BoundingFrustum__ctor" data-uid="VRageMath.BoundingFrustum.#ctor">BoundingFrustum()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingFrustum()</code></pre>
  </div>
  
  
  <a id="VRageMath_BoundingFrustum__ctor_" data-uid="VRageMath.BoundingFrustum.#ctor*"></a>
  <h4 id="VRageMath_BoundingFrustum__ctor_VRageMath_Matrix_" data-uid="VRageMath.BoundingFrustum.#ctor(VRageMath.Matrix)">BoundingFrustum(Matrix)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingFrustum(Matrix value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Combined matrix that usually takes view × projection matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingFrustum_CornerCount" data-uid="VRageMath.BoundingFrustum.CornerCount">CornerCount</h4>
  <div class="markdown level1 summary"><p>Specifies the total number of corners (8) in the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int CornerCount = 8</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingFrustum_Bottom_" data-uid="VRageMath.BoundingFrustum.Bottom*"></a>
  <h4 id="VRageMath_BoundingFrustum_Bottom" data-uid="VRageMath.BoundingFrustum.Bottom">Bottom</h4>
  <div class="markdown level1 summary"><p>Gets the bottom plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Bottom { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Far_" data-uid="VRageMath.BoundingFrustum.Far*"></a>
  <h4 id="VRageMath_BoundingFrustum_Far" data-uid="VRageMath.BoundingFrustum.Far">Far</h4>
  <div class="markdown level1 summary"><p>Gets the far plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Far { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Item_" data-uid="VRageMath.BoundingFrustum.Item*"></a>
  <h4 id="VRageMath_BoundingFrustum_Item_System_Int32_" data-uid="VRageMath.BoundingFrustum.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane this[int index] { get; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Left_" data-uid="VRageMath.BoundingFrustum.Left*"></a>
  <h4 id="VRageMath_BoundingFrustum_Left" data-uid="VRageMath.BoundingFrustum.Left">Left</h4>
  <div class="markdown level1 summary"><p>Gets the left plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Left { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Matrix_" data-uid="VRageMath.BoundingFrustum.Matrix*"></a>
  <h4 id="VRageMath_BoundingFrustum_Matrix" data-uid="VRageMath.BoundingFrustum.Matrix">Matrix</h4>
  <div class="markdown level1 summary"><p>Gets or sets the Matrix that describes this bounding frustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix Matrix { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Near_" data-uid="VRageMath.BoundingFrustum.Near*"></a>
  <h4 id="VRageMath_BoundingFrustum_Near" data-uid="VRageMath.BoundingFrustum.Near">Near</h4>
  <div class="markdown level1 summary"><p>Gets the near plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Near { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Planes_" data-uid="VRageMath.BoundingFrustum.Planes*"></a>
  <h4 id="VRageMath_BoundingFrustum_Planes" data-uid="VRageMath.BoundingFrustum.Planes">Planes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane[] Planes { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Right_" data-uid="VRageMath.BoundingFrustum.Right*"></a>
  <h4 id="VRageMath_BoundingFrustum_Right" data-uid="VRageMath.BoundingFrustum.Right">Right</h4>
  <div class="markdown level1 summary"><p>Gets the right plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Right { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Top_" data-uid="VRageMath.BoundingFrustum.Top*"></a>
  <h4 id="VRageMath_BoundingFrustum_Top" data-uid="VRageMath.BoundingFrustum.Top">Top</h4>
  <div class="markdown level1 summary"><p>Gets the top plane of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Top { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_BoundingBox__" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.BoundingBox@)">Contains(ref BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(ref BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check against the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_BoundingBox__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.BoundingBox@,VRageMath.ContainmentType@)">Contains(ref BoundingBox, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBox box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_BoundingFrustum_" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.BoundingFrustum)">Contains(BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check against the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_BoundingSphere_" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.BoundingSphere)">Contains(BoundingSphere)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check against the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_BoundingSphere__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.BoundingSphere@,VRageMath.ContainmentType@)">Contains(ref BoundingSphere, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingSphere sphere, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_Vector3_" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.Vector3)">Contains(Vector3)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3 point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to check against the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Contains_" data-uid="VRageMath.BoundingFrustum.Contains*"></a>
  <h4 id="VRageMath_BoundingFrustum_Contains_VRageMath_Vector3__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingFrustum.Contains(VRageMath.Vector3@,VRageMath.ContainmentType@)">Contains(ref Vector3, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector3 point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Equals_" data-uid="VRageMath.BoundingFrustum.Equals*"></a>
  <h4 id="VRageMath_BoundingFrustum_Equals_System_Object_" data-uid="VRageMath.BoundingFrustum.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Equals_" data-uid="VRageMath.BoundingFrustum.Equals*"></a>
  <h4 id="VRageMath_BoundingFrustum_Equals_VRageMath_BoundingFrustum_" data-uid="VRageMath.BoundingFrustum.Equals(VRageMath.BoundingFrustum)">Equals(BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified BoundingFrustum is equal to the current BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingFrustum other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingFrustum to compare with the current BoundingFrustum.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_GetCorners_" data-uid="VRageMath.BoundingFrustum.GetCorners*"></a>
  <h4 id="VRageMath_BoundingFrustum_GetCorners" data-uid="VRageMath.BoundingFrustum.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingFrustum. ALLOCATION!</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_GetCorners_" data-uid="VRageMath.BoundingFrustum.GetCorners*"></a>
  <h4 id="VRageMath_BoundingFrustum_GetCorners_VRageMath_Vector3___" data-uid="VRageMath.BoundingFrustum.GetCorners(VRageMath.Vector3[])">GetCorners(Vector3[])</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector3[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3 points where the corners of the BoundingFrustum are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_GetCornersUnsafe_" data-uid="VRageMath.BoundingFrustum.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingFrustum_GetCornersUnsafe_VRageMath_Vector3__" data-uid="VRageMath.BoundingFrustum.GetCornersUnsafe(VRageMath.Vector3*)">GetCornersUnsafe(Vector3*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector3*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector3 points where the corners of the BoundingBox are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_GetHashCode_" data-uid="VRageMath.BoundingFrustum.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingFrustum_GetHashCode" data-uid="VRageMath.BoundingFrustum.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_BoundingBox_" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.BoundingBox)">Intersects(BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects the specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_BoundingBox__System_Boolean__" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.BoundingBox@,System.Boolean@)">Intersects(ref BoundingBox, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects a BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingFrustum and BoundingBox intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_BoundingFrustum_" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.BoundingFrustum)">Intersects(BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects the specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_BoundingSphere_" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.BoundingSphere)">Intersects(BoundingSphere)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects the specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_BoundingSphere__System_Boolean__" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.BoundingSphere@,System.Boolean@)">Intersects(ref BoundingSphere, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphere sphere, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingFrustum and BoundingSphere intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_Plane_" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.Plane)">Intersects(Plane)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects the specified Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_Plane__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.Plane@,VRageMath.PlaneIntersectionType@)">Intersects(ref Plane, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects a Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Plane plane, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The Plane to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the BoundingFrustum intersects the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_Ray_" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.Ray)">Intersects(Ray)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects the specified Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;float&gt; Intersects(Ray ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_Intersects_" data-uid="VRageMath.BoundingFrustum.Intersects*"></a>
  <h4 id="VRageMath_BoundingFrustum_Intersects_VRageMath_Ray__System_Nullable_System_Single___" data-uid="VRageMath.BoundingFrustum.Intersects(VRageMath.Ray@,System.Nullable{System.Single}@)">Intersects(ref Ray, out Nullable&lt;Single&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingFrustum intersects a Ray.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Ray ray, out Nullable&lt;float&gt; result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Ray.html">Ray</a></td>
        <td><span class="parametername">ray</span></td>
        <td><p>The Ray to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Single</span>&gt;</td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Distance at which the ray intersects the BoundingFrustum or null if there is no intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_ToString_" data-uid="VRageMath.BoundingFrustum.ToString*"></a>
  <h4 id="VRageMath_BoundingFrustum_ToString" data-uid="VRageMath.BoundingFrustum.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingFrustum_op_Equality_" data-uid="VRageMath.BoundingFrustum.op_Equality*"></a>
  <h4 id="VRageMath_BoundingFrustum_op_Equality_VRageMath_BoundingFrustum_VRageMath_BoundingFrustum_" data-uid="VRageMath.BoundingFrustum.op_Equality(VRageMath.BoundingFrustum,VRageMath.BoundingFrustum)">Equality(BoundingFrustum, BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingFrustum are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingFrustum a, BoundingFrustum b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The BoundingFrustum to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The BoundingFrustum to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingFrustum_op_Inequality_" data-uid="VRageMath.BoundingFrustum.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingFrustum_op_Inequality_VRageMath_BoundingFrustum_VRageMath_BoundingFrustum_" data-uid="VRageMath.BoundingFrustum.op_Inequality(VRageMath.BoundingFrustum,VRageMath.BoundingFrustum)">Inequality(BoundingFrustum, BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingFrustum are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingFrustum a, BoundingFrustum b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The BoundingFrustum to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The BoundingFrustum to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRageMath.BoundingFrustumExtensions.html#VRageMath_BoundingFrustumExtensions_ToBoundingSphere_VRageMath_BoundingFrustum_VRageMath_Vector3___">BoundingFrustumExtensions.ToBoundingSphere(BoundingFrustum, Vector3[])</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
