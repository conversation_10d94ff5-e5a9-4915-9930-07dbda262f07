﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MatrixI
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MatrixI
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.MatrixI">
  
  
  <h1 id="VRageMath_MatrixI" data-uid="VRageMath.MatrixI" class="text-break">Class MatrixI
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MatrixI</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_MatrixI_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MatrixI : ValueType</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.#ctor(VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction)">MatrixI(Base6Directions.Direction, Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(Base6Directions.Direction forward, Base6Directions.Direction up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_MyBlockOrientation_" data-uid="VRageMath.MatrixI.#ctor(VRageMath.MyBlockOrientation)">MatrixI(MyBlockOrientation)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(MyBlockOrientation orientation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_Vector3I_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.#ctor(VRageMath.Vector3I,VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction)">MatrixI(Vector3I, Base6Directions.Direction, Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(Vector3I position, Base6Directions.Direction forward, Base6Directions.Direction up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_Vector3I__VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.#ctor(VRageMath.Vector3I@,VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction)">MatrixI(ref Vector3I, Base6Directions.Direction, Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(ref Vector3I position, Base6Directions.Direction forward, Base6Directions.Direction up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_Vector3I__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.MatrixI.#ctor(VRageMath.Vector3I@,VRageMath.Vector3@,VRageMath.Vector3@)">MatrixI(ref Vector3I, ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(ref Vector3I position, ref Vector3 forward, ref Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI__ctor_" data-uid="VRageMath.MatrixI.#ctor*"></a>
  <h4 id="VRageMath_MatrixI__ctor_VRageMath_Vector3I__VRageMath_Vector3I__VRageMath_Vector3I__" data-uid="VRageMath.MatrixI.#ctor(VRageMath.Vector3I@,VRageMath.Vector3I@,VRageMath.Vector3I@)">MatrixI(ref Vector3I, ref Vector3I, ref Vector3I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MatrixI(ref Vector3I position, ref Vector3I forward, ref Vector3I up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_MatrixI_Backward" data-uid="VRageMath.MatrixI.Backward">Backward</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Backward</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixI_Right" data-uid="VRageMath.MatrixI.Right">Right</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Right</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixI_Translation" data-uid="VRageMath.MatrixI.Translation">Translation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I Translation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_MatrixI_Up" data-uid="VRageMath.MatrixI.Up">Up</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Up</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_MatrixI_BackwardVector_" data-uid="VRageMath.MatrixI.BackwardVector*"></a>
  <h4 id="VRageMath_MatrixI_BackwardVector" data-uid="VRageMath.MatrixI.BackwardVector">BackwardVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I BackwardVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Down_" data-uid="VRageMath.MatrixI.Down*"></a>
  <h4 id="VRageMath_MatrixI_Down" data-uid="VRageMath.MatrixI.Down">Down</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Down { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_DownVector_" data-uid="VRageMath.MatrixI.DownVector*"></a>
  <h4 id="VRageMath_MatrixI_DownVector" data-uid="VRageMath.MatrixI.DownVector">DownVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I DownVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Forward_" data-uid="VRageMath.MatrixI.Forward*"></a>
  <h4 id="VRageMath_MatrixI_Forward" data-uid="VRageMath.MatrixI.Forward">Forward</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Forward { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_ForwardVector_" data-uid="VRageMath.MatrixI.ForwardVector*"></a>
  <h4 id="VRageMath_MatrixI_ForwardVector" data-uid="VRageMath.MatrixI.ForwardVector">ForwardVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I ForwardVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Left_" data-uid="VRageMath.MatrixI.Left*"></a>
  <h4 id="VRageMath_MatrixI_Left" data-uid="VRageMath.MatrixI.Left">Left</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction Left { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_LeftVector_" data-uid="VRageMath.MatrixI.LeftVector*"></a>
  <h4 id="VRageMath_MatrixI_LeftVector" data-uid="VRageMath.MatrixI.LeftVector">LeftVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I LeftVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_RightVector_" data-uid="VRageMath.MatrixI.RightVector*"></a>
  <h4 id="VRageMath_MatrixI_RightVector" data-uid="VRageMath.MatrixI.RightVector">RightVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I RightVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_UpVector_" data-uid="VRageMath.MatrixI.UpVector*"></a>
  <h4 id="VRageMath_MatrixI_UpVector" data-uid="VRageMath.MatrixI.UpVector">UpVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3I UpVector { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3I.html">Vector3I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_MatrixI_CreateRotation_" data-uid="VRageMath.MatrixI.CreateRotation*"></a>
  <h4 id="VRageMath_MatrixI_CreateRotation_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.CreateRotation(VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction)">CreateRotation(Base6Directions.Direction, Base6Directions.Direction, Base6Directions.Direction, Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixI CreateRotation(Base6Directions.Direction oldA, Base6Directions.Direction oldB, Base6Directions.Direction newA, Base6Directions.Direction newB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">oldA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">oldB</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">newA</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">newB</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_GetBlockOrientation_" data-uid="VRageMath.MatrixI.GetBlockOrientation*"></a>
  <h4 id="VRageMath_MatrixI_GetBlockOrientation" data-uid="VRageMath.MatrixI.GetBlockOrientation">GetBlockOrientation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyBlockOrientation GetBlockOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_GetDirection_" data-uid="VRageMath.MatrixI.GetDirection*"></a>
  <h4 id="VRageMath_MatrixI_GetDirection_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.GetDirection(VRageMath.Base6Directions.Direction)">GetDirection(Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Base6Directions.Direction GetDirection(Base6Directions.Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_GetFloatMatrix_" data-uid="VRageMath.MatrixI.GetFloatMatrix*"></a>
  <h4 id="VRageMath_MatrixI_GetFloatMatrix" data-uid="VRageMath.MatrixI.GetFloatMatrix">GetFloatMatrix()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Matrix GetFloatMatrix()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Invert_" data-uid="VRageMath.MatrixI.Invert*"></a>
  <h4 id="VRageMath_MatrixI_Invert_VRageMath_MatrixI__VRageMath_MatrixI__" data-uid="VRageMath.MatrixI.Invert(VRageMath.MatrixI@,VRageMath.MatrixI@)">Invert(ref MatrixI, out MatrixI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Invert(ref MatrixI matrix, out MatrixI result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Multiply_" data-uid="VRageMath.MatrixI.Multiply*"></a>
  <h4 id="VRageMath_MatrixI_Multiply_VRageMath_MatrixI__VRageMath_MatrixI__VRageMath_MatrixI__" data-uid="VRageMath.MatrixI.Multiply(VRageMath.MatrixI@,VRageMath.MatrixI@,VRageMath.MatrixI@)">Multiply(ref MatrixI, ref MatrixI, out MatrixI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref MatrixI leftMatrix, ref MatrixI rightMatrix, out MatrixI result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">leftMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">rightMatrix</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_SetDirection_" data-uid="VRageMath.MatrixI.SetDirection*"></a>
  <h4 id="VRageMath_MatrixI_SetDirection_VRageMath_Base6Directions_Direction_VRageMath_Base6Directions_Direction_" data-uid="VRageMath.MatrixI.SetDirection(VRageMath.Base6Directions.Direction,VRageMath.Base6Directions.Direction)">SetDirection(Base6Directions.Direction, Base6Directions.Direction)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetDirection(Base6Directions.Direction dirToSet, Base6Directions.Direction newDirection)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">dirToSet</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Base6Directions.Direction.html">Base6Directions.Direction</a></td>
        <td><span class="parametername">newDirection</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MatrixI_Transform_" data-uid="VRageMath.MatrixI.Transform*"></a>
  <h4 id="VRageMath_MatrixI_Transform_VRageMath_MyBlockOrientation__VRageMath_MatrixI__" data-uid="VRageMath.MatrixI.Transform(VRageMath.MyBlockOrientation@,VRageMath.MatrixI@)">Transform(ref MyBlockOrientation, ref MatrixI)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyBlockOrientation Transform(ref MyBlockOrientation orientation, ref MatrixI transform)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td><span class="parametername">orientation</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixI.html">MatrixI</a></td>
        <td><span class="parametername">transform</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyBlockOrientation.html">MyBlockOrientation</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
