﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingSphereD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingSphereD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingSphereD">
  
  
  <h1 id="VRageMath_BoundingSphereD" data-uid="VRageMath.BoundingSphereD" class="text-break">Class BoundingSphereD
  </h1>
  <div class="markdown level0 summary"><p>Defines a sphere.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingSphereD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingSphereD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class BoundingSphereD : ValueType, IEquatable&lt;BoundingSphereD&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingSphereD__ctor_" data-uid="VRageMath.BoundingSphereD.#ctor*"></a>
  <h4 id="VRageMath_BoundingSphereD__ctor_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.BoundingSphereD.#ctor(VRageMath.Vector3D,System.Double)">BoundingSphereD(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingSphereD(Vector3D center, double radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">center</span></td>
        <td><p>Center point of the sphere.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">radius</span></td>
        <td><p>Radius of the sphere.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingSphereD_Center" data-uid="VRageMath.BoundingSphereD.Center">Center</h4>
  <div class="markdown level1 summary"><p>The center point of the sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Center</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingSphereD_Radius" data-uid="VRageMath.BoundingSphereD.Radius">Radius</h4>
  <div class="markdown level1 summary"><p>The radius of the sphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Radius</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.BoundingBoxD)">Contains(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check against the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_BoundingBoxD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.BoundingBoxD@,VRageMath.ContainmentType@)">Contains(ref BoundingBoxD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBoxD box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.BoundingFrustumD)">Contains(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check against the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.BoundingSphereD)">Contains(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphereD to check against the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_BoundingSphereD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.BoundingSphereD@,VRageMath.ContainmentType@)">Contains(ref BoundingSphereD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingSphereD sphere, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphereD to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_MyOrientedBoundingBoxD_" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.MyOrientedBoundingBoxD)">Contains(MyOrientedBoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified MyOrientedBoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(MyOrientedBoundingBoxD obox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBoxD.html">MyOrientedBoundingBoxD</a></td>
        <td><span class="parametername">obox</span></td>
        <td><p>The MyOrientedBoundingBoxD to check against the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_MyOrientedBoundingBoxD__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.MyOrientedBoundingBoxD@,VRageMath.ContainmentType@)">Contains(ref MyOrientedBoundingBoxD, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified MyOrientedBoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref MyOrientedBoundingBoxD obox, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBoxD.html">MyOrientedBoundingBoxD</a></td>
        <td><span class="parametername">obox</span></td>
        <td><p>The MyOrientedBoundingBoxD to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_Vector3D_" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.Vector3D)">Contains(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to check against the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Contains_" data-uid="VRageMath.BoundingSphereD.Contains*"></a>
  <h4 id="VRageMath_BoundingSphereD_Contains_VRageMath_Vector3D__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingSphereD.Contains(VRageMath.Vector3D@,VRageMath.ContainmentType@)">Contains(ref Vector3D, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD contains the specified point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector3D point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateFromBoundingBox_" data-uid="VRageMath.BoundingSphereD.CreateFromBoundingBox*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateFromBoundingBox_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingSphereD.CreateFromBoundingBox(VRageMath.BoundingBoxD)">CreateFromBoundingBox(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingSphereD that can contain a specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD CreateFromBoundingBox(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to create the BoundingSphereD from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateFromBoundingBox_" data-uid="VRageMath.BoundingSphereD.CreateFromBoundingBox*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateFromBoundingBox_VRageMath_BoundingBoxD__VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingSphereD.CreateFromBoundingBox(VRageMath.BoundingBoxD@,VRageMath.BoundingSphereD@)">CreateFromBoundingBox(ref BoundingBoxD, out BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingSphereD that can contain a specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromBoundingBox(ref BoundingBoxD box, out BoundingSphereD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to create the BoundingSphereD from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateFromFrustum_" data-uid="VRageMath.BoundingSphereD.CreateFromFrustum*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateFromFrustum_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingSphereD.CreateFromFrustum(VRageMath.BoundingFrustumD)">CreateFromFrustum(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingSphereD that can contain a specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD CreateFromFrustum(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to create the BoundingSphereD with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateFromPoints_" data-uid="VRageMath.BoundingSphereD.CreateFromPoints*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateFromPoints_VRageMath_Vector3D___" data-uid="VRageMath.BoundingSphereD.CreateFromPoints(VRageMath.Vector3D[])">CreateFromPoints(Vector3D[])</h4>
  <div class="markdown level1 summary"><p>Creates a BoundingSphereD that can contain a specified list of points.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD CreateFromPoints(Vector3D[] points)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>[]</td>
        <td><span class="parametername">points</span></td>
        <td><p>List of points the BoundingSphereD must contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateInvalid_" data-uid="VRageMath.BoundingSphereD.CreateInvalid*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateInvalid" data-uid="VRageMath.BoundingSphereD.CreateInvalid">CreateInvalid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD CreateInvalid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateMerged_" data-uid="VRageMath.BoundingSphereD.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateMerged_VRageMath_BoundingSphereD_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.CreateMerged(VRageMath.BoundingSphereD,VRageMath.BoundingSphereD)">CreateMerged(BoundingSphereD, BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Creates a BoundingSphereD that contains the two specified BoundingSphereD instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingSphereD CreateMerged(BoundingSphereD original, BoundingSphereD additional)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>BoundingSphereD to be merged.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>BoundingSphereD to be merged.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_CreateMerged_" data-uid="VRageMath.BoundingSphereD.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingSphereD_CreateMerged_VRageMath_BoundingSphereD__VRageMath_BoundingSphereD__VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingSphereD.CreateMerged(VRageMath.BoundingSphereD@,VRageMath.BoundingSphereD@,VRageMath.BoundingSphereD@)">CreateMerged(ref BoundingSphereD, ref BoundingSphereD, out BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Creates a BoundingSphereD that contains the two specified BoundingSphereD instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateMerged(ref BoundingSphereD original, ref BoundingSphereD additional, out BoundingSphereD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>BoundingSphereD to be merged.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>BoundingSphereD to be merged.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Equals_" data-uid="VRageMath.BoundingSphereD.Equals*"></a>
  <h4 id="VRageMath_BoundingSphereD_Equals_System_Object_" data-uid="VRageMath.BoundingSphereD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Equals_" data-uid="VRageMath.BoundingSphereD.Equals*"></a>
  <h4 id="VRageMath_BoundingSphereD_Equals_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.Equals(VRageMath.BoundingSphereD)">Equals(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified BoundingSphereD is equal to the current BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingSphereD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingSphereD to compare with the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_GetBoundingBox_" data-uid="VRageMath.BoundingSphereD.GetBoundingBox*"></a>
  <h4 id="VRageMath_BoundingSphereD_GetBoundingBox" data-uid="VRageMath.BoundingSphereD.GetBoundingBox">GetBoundingBox()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD GetBoundingBox()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_GetHashCode_" data-uid="VRageMath.BoundingSphereD.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingSphereD_GetHashCode" data-uid="VRageMath.BoundingSphereD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Include_" data-uid="VRageMath.BoundingSphereD.Include*"></a>
  <h4 id="VRageMath_BoundingSphereD_Include_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.Include(VRageMath.BoundingSphereD)">Include(BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingSphereD Include(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Include_" data-uid="VRageMath.BoundingSphereD.Include*"></a>
  <h4 id="VRageMath_BoundingSphereD_Include_VRageMath_BoundingSphereD__VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingSphereD.Include(VRageMath.BoundingSphereD@,VRageMath.BoundingSphereD@)">Include(ref BoundingSphereD, ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Include(ref BoundingSphereD sphere, ref BoundingSphereD otherSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">otherSphere</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_IntersectRaySphere_" data-uid="VRageMath.BoundingSphereD.IntersectRaySphere*"></a>
  <h4 id="VRageMath_BoundingSphereD_IntersectRaySphere_VRageMath_RayD_System_Double__System_Double__" data-uid="VRageMath.BoundingSphereD.IntersectRaySphere(VRageMath.RayD,System.Double@,System.Double@)">IntersectRaySphere(RayD, out Double, out Double)</h4>
  <div class="markdown level1 summary"><p>NOTE: This function doesn't calculate the normal because it's easily derived for a sphere (p - center).</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IntersectRaySphere(RayD ray, out double tmin, out double tmax)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">tmin</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">tmax</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_BoundingBoxD_" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.BoundingBoxD)">Intersects(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD intersects with a specified BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check for intersection with the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_BoundingBoxD__System_Boolean__" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.BoundingBoxD@,System.Boolean@)">Intersects(ref BoundingBoxD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD intersects a BoundingBoxD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBoxD box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBoxD to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingSphereD and BoundingBoxD intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_BoundingFrustumD_" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.BoundingFrustumD)">Intersects(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD intersects with a specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection with the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.BoundingSphereD)">Intersects(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD intersects with a specified BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphereD to check for intersection with the current BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_BoundingSphereD__System_Boolean__" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.BoundingSphereD@,System.Boolean@)">Intersects(ref BoundingSphereD, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingSphereD intersects another BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphereD sphere, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphereD to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingSphereD instances intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Intersects_" data-uid="VRageMath.BoundingSphereD.Intersects*"></a>
  <h4 id="VRageMath_BoundingSphereD_Intersects_VRageMath_RayD_" data-uid="VRageMath.BoundingSphereD.Intersects(VRageMath.RayD)">Intersects(RayD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;double&gt; Intersects(RayD ray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.RayD.html">RayD</a></td>
        <td><span class="parametername">ray</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Double</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_RandomToUniformPointInSphere_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointInSphere*"></a>
  <h4 id="VRageMath_BoundingSphereD_RandomToUniformPointInSphere_System_Double_System_Double_System_Double_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointInSphere(System.Double,System.Double,System.Double)">RandomToUniformPointInSphere(Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>If ranX, ranY, ranZ are uniformly distributed across ranges &lt;0,1&gt;, Resulting point will be uniformly distributed inside sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D RandomToUniformPointInSphere(double ranX, double ranY, double ranZ)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranX</span></td>
        <td><p>Random number in &lt;0,1&gt; affecting azimuth</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranY</span></td>
        <td><p>Random number in &lt;0,1&gt; affecting altitude</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranZ</span></td>
        <td><p>Random number in &lt;0,1&gt; affecting distance from center</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_RandomToUniformPointInSphereWithInnerCutout_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointInSphereWithInnerCutout*"></a>
  <h4 id="VRageMath_BoundingSphereD_RandomToUniformPointInSphereWithInnerCutout_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointInSphereWithInnerCutout(System.Double,System.Double,System.Double,System.Double)">RandomToUniformPointInSphereWithInnerCutout(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Similar to RandomToUniformPointInSphere(...) but excludes points within distance of cutoutRadius from center. (Results are randomly distributed in the shape that remains from sphere that had another sphere cut out from center. )</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Nullable&lt;Vector3D&gt; RandomToUniformPointInSphereWithInnerCutout(double ranX, double ranY, double ranZ, double cutoutRadius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranX</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranY</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranZ</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">cutoutRadius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_RandomToUniformPointOnSphere_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointOnSphere*"></a>
  <h4 id="VRageMath_BoundingSphereD_RandomToUniformPointOnSphere_System_Double_System_Double_" data-uid="VRageMath.BoundingSphereD.RandomToUniformPointOnSphere(System.Double,System.Double)">RandomToUniformPointOnSphere(Double, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D RandomToUniformPointOnSphere(double ranX, double ranY)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranX</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">ranY</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_ToString_" data-uid="VRageMath.BoundingSphereD.ToString*"></a>
  <h4 id="VRageMath_BoundingSphereD_ToString" data-uid="VRageMath.BoundingSphereD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingSphereD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Transform_" data-uid="VRageMath.BoundingSphereD.Transform*"></a>
  <h4 id="VRageMath_BoundingSphereD_Transform_VRageMath_MatrixD_" data-uid="VRageMath.BoundingSphereD.Transform(VRageMath.MatrixD)">Transform(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Translates and scales the BoundingSphereD using a given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingSphereD Transform(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>A transformation matrix that might include translation, rotation, or uniform scaling. Note that BoundingSphereD.Transform will not return correct results if there are non-uniform scaling, shears, or other unusual transforms in this transformation matrix. This is because there is no way to shear or non-uniformly scale a sphere. Such an operation would cause the sphere to lose its shape as a sphere.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_Transform_" data-uid="VRageMath.BoundingSphereD.Transform*"></a>
  <h4 id="VRageMath_BoundingSphereD_Transform_VRageMath_MatrixD__VRageMath_BoundingSphereD__" data-uid="VRageMath.BoundingSphereD.Transform(VRageMath.MatrixD@,VRageMath.BoundingSphereD@)">Transform(ref MatrixD, out BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Translates and scales the BoundingSphereD using a given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Transform(ref MatrixD matrix, out BoundingSphereD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>A transformation matrix that might include translation, rotation, or uniform scaling. Note that BoundingSphereD.Transform will not return correct results if there are non-uniform scaling, shears, or other unusual transforms in this transformation matrix. This is because there is no way to shear or non-uniformly scale a sphere. Such an operation would cause the sphere to lose its shape as a sphere.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The transformed BoundingSphereD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingSphereD_op_Equality_" data-uid="VRageMath.BoundingSphereD.op_Equality*"></a>
  <h4 id="VRageMath_BoundingSphereD_op_Equality_VRageMath_BoundingSphereD_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.op_Equality(VRageMath.BoundingSphereD,VRageMath.BoundingSphereD)">Equality(BoundingSphereD, BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingSphereD are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingSphereD a, BoundingSphereD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_op_Implicit_" data-uid="VRageMath.BoundingSphereD.op_Implicit*"></a>
  <h4 id="VRageMath_BoundingSphereD_op_Implicit_VRageMath_BoundingSphere__VRageMath_BoundingSphereD" data-uid="VRageMath.BoundingSphereD.op_Implicit(VRageMath.BoundingSphere)~VRageMath.BoundingSphereD">Implicit(BoundingSphere to BoundingSphereD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator BoundingSphereD(BoundingSphere b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_op_Implicit_" data-uid="VRageMath.BoundingSphereD.op_Implicit*"></a>
  <h4 id="VRageMath_BoundingSphereD_op_Implicit_VRageMath_BoundingSphereD__VRageMath_BoundingSphere" data-uid="VRageMath.BoundingSphereD.op_Implicit(VRageMath.BoundingSphereD)~VRageMath.BoundingSphere">Implicit(BoundingSphereD to BoundingSphere)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator BoundingSphere(BoundingSphereD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingSphereD_op_Inequality_" data-uid="VRageMath.BoundingSphereD.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingSphereD_op_Inequality_VRageMath_BoundingSphereD_VRageMath_BoundingSphereD_" data-uid="VRageMath.BoundingSphereD.op_Inequality(VRageMath.BoundingSphereD,VRageMath.BoundingSphereD)">Inequality(BoundingSphereD, BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingSphereD are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingSphereD a, BoundingSphereD b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The BoundingSphereD to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The BoundingSphereD to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
