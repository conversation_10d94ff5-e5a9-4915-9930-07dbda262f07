﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Vector2
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Vector2
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Vector2">
  
  
  <h1 id="VRageMath_Vector2" data-uid="VRageMath.Vector2" class="text-break">Class Vector2
  </h1>
  <div class="markdown level0 summary"><p>Defines a vector with two components.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Vector2</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Vector2_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Vector2 : ValueType, IEquatable&lt;Vector2&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Vector2__ctor_" data-uid="VRageMath.Vector2.#ctor*"></a>
  <h4 id="VRageMath_Vector2__ctor_System_Single_" data-uid="VRageMath.Vector2.#ctor(System.Single)">Vector2(Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Vector2.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value to initialize both components to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2__ctor_" data-uid="VRageMath.Vector2.#ctor*"></a>
  <h4 id="VRageMath_Vector2__ctor_System_Single_System_Single_" data-uid="VRageMath.Vector2.#ctor(System.Single,System.Single)">Vector2(Single, Single)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector2.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2(float x, float y)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>Initial value for the x-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>Initial value for the y-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Vector2_One" data-uid="VRageMath.Vector2.One">One</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 One</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_PositiveInfinity" data-uid="VRageMath.Vector2.PositiveInfinity">PositiveInfinity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 PositiveInfinity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_UnitX" data-uid="VRageMath.Vector2.UnitX">UnitX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 UnitX</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_UnitY" data-uid="VRageMath.Vector2.UnitY">UnitY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 UnitY</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_X" data-uid="VRageMath.Vector2.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the x-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_Y" data-uid="VRageMath.Vector2.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the y-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector2_Zero" data-uid="VRageMath.Vector2.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Vector2_Item_" data-uid="VRageMath.Vector2.Item*"></a>
  <h4 id="VRageMath_Vector2_Item_System_Int32_" data-uid="VRageMath.Vector2.Item(System.Int32)">Item[Int32]</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float this[int index] { get; set; }</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Vector2_Add_" data-uid="VRageMath.Vector2.Add*"></a>
  <h4 id="VRageMath_Vector2_Add_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Add(VRageMath.Vector2,VRageMath.Vector2)">Add(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Add(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Add_" data-uid="VRageMath.Vector2.Add*"></a>
  <h4 id="VRageMath_Vector2_Add_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Add(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Add(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Sum of the source vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_AssertIsValid_" data-uid="VRageMath.Vector2.AssertIsValid*"></a>
  <h4 id="VRageMath_Vector2_AssertIsValid" data-uid="VRageMath.Vector2.AssertIsValid">AssertIsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AssertIsValid()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector2_Barycentric_" data-uid="VRageMath.Vector2.Barycentric*"></a>
  <h4 id="VRageMath_Vector2_Barycentric_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_System_Single_System_Single_" data-uid="VRageMath.Vector2.Barycentric(VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,System.Single,System.Single)">Barycentric(Vector2, Vector2, Vector2, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector2 containing the 2D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 2D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Barycentric(Vector2 value1, Vector2 value2, Vector2 value3, float amount1, float amount2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Barycentric_" data-uid="VRageMath.Vector2.Barycentric*"></a>
  <h4 id="VRageMath_Vector2_Barycentric_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__System_Single_System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.Barycentric(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,System.Single,System.Single,VRageMath.Vector2@)">Barycentric(ref Vector2, ref Vector2, ref Vector2, Single, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector2 containing the 2D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 2D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(ref Vector2 value1, ref Vector2 value2, ref Vector2 value3, float amount1, float amount2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector2 containing the 2D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The 2D Cartesian coordinates of the specified point are placed in this Vector2 on exit.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Between_" data-uid="VRageMath.Vector2.Between*"></a>
  <h4 id="VRageMath_Vector2_Between_VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Between(VRageMath.Vector2@,VRageMath.Vector2@)">Between(ref Vector2, ref Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Between(ref Vector2 start, ref Vector2 end)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">start</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">end</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_CatmullRom_" data-uid="VRageMath.Vector2.CatmullRom*"></a>
  <h4 id="VRageMath_Vector2_CatmullRom_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.CatmullRom(VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,System.Single)">CatmullRom(Vector2, Vector2, Vector2, Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 CatmullRom(Vector2 value1, Vector2 value2, Vector2 value3, Vector2 value4, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_CatmullRom_" data-uid="VRageMath.Vector2.CatmullRom*"></a>
  <h4 id="VRageMath_Vector2_CatmullRom_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.CatmullRom(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">CatmullRom(ref Vector2, ref Vector2, ref Vector2, ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CatmullRom(ref Vector2 value1, ref Vector2 value2, ref Vector2 value3, ref Vector2 value4, float amount, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A vector that is the result of the Catmull-Rom interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Clamp_" data-uid="VRageMath.Vector2.Clamp*"></a>
  <h4 id="VRageMath_Vector2_Clamp_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Clamp(VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2)">Clamp(Vector2, Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Clamp(Vector2 value1, Vector2 min, Vector2 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Clamp_" data-uid="VRageMath.Vector2.Clamp*"></a>
  <h4 id="VRageMath_Vector2_Clamp_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Clamp(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Clamp(ref Vector2, ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Clamp(ref Vector2 value1, ref Vector2 min, ref Vector2 max, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The clamped value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_ClampToSphere_" data-uid="VRageMath.Vector2.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector2_ClampToSphere_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.ClampToSphere(VRageMath.Vector2,System.Single)">ClampToSphere(Vector2, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 ClampToSphere(Vector2 vector, float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_ClampToSphere_" data-uid="VRageMath.Vector2.ClampToSphere*"></a>
  <h4 id="VRageMath_Vector2_ClampToSphere_VRageMath_Vector2__System_Single_" data-uid="VRageMath.Vector2.ClampToSphere(VRageMath.Vector2@,System.Single)">ClampToSphere(ref Vector2, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void ClampToSphere(ref Vector2 vector, float radius)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Distance_" data-uid="VRageMath.Vector2.Distance*"></a>
  <h4 id="VRageMath_Vector2_Distance_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Distance(VRageMath.Vector2,VRageMath.Vector2)">Distance(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Distance(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Distance_" data-uid="VRageMath.Vector2.Distance*"></a>
  <h4 id="VRageMath_Vector2_Distance_VRageMath_Vector2__VRageMath_Vector2__System_Single__" data-uid="VRageMath.Vector2.Distance(VRageMath.Vector2@,VRageMath.Vector2@,System.Single@)">Distance(ref Vector2, ref Vector2, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Distance(ref Vector2 value1, ref Vector2 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_DistanceSquared_" data-uid="VRageMath.Vector2.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector2_DistanceSquared_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.DistanceSquared(VRageMath.Vector2,VRageMath.Vector2)">DistanceSquared(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float DistanceSquared(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_DistanceSquared_" data-uid="VRageMath.Vector2.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector2_DistanceSquared_VRageMath_Vector2__VRageMath_Vector2__System_Single__" data-uid="VRageMath.Vector2.DistanceSquared(VRageMath.Vector2@,VRageMath.Vector2@,System.Single@)">DistanceSquared(ref Vector2, ref Vector2, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DistanceSquared(ref Vector2 value1, ref Vector2 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors squared.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Divide_" data-uid="VRageMath.Vector2.Divide*"></a>
  <h4 id="VRageMath_Vector2_Divide_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.Divide(VRageMath.Vector2,System.Single)">Divide(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Divide(Vector2 value1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Divide_" data-uid="VRageMath.Vector2.Divide*"></a>
  <h4 id="VRageMath_Vector2_Divide_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Divide(VRageMath.Vector2,VRageMath.Vector2)">Divide(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Divide(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Divide_" data-uid="VRageMath.Vector2.Divide*"></a>
  <h4 id="VRageMath_Vector2_Divide_VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.Divide(VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">Divide(ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector2 value1, float divider, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Divide_" data-uid="VRageMath.Vector2.Divide*"></a>
  <h4 id="VRageMath_Vector2_Divide_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Divide(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Divide(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Dot_" data-uid="VRageMath.Vector2.Dot*"></a>
  <h4 id="VRageMath_Vector2_Dot_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Dot(VRageMath.Vector2,VRageMath.Vector2)">Dot(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors. If the two vectors are unit vectors, the dot product returns a floating point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Dot(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Dot_" data-uid="VRageMath.Vector2.Dot*"></a>
  <h4 id="VRageMath_Vector2_Dot_VRageMath_Vector2__VRageMath_Vector2__System_Single__" data-uid="VRageMath.Vector2.Dot(VRageMath.Vector2@,VRageMath.Vector2@,System.Single@)">Dot(ref Vector2, ref Vector2, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors and writes the result to a user-specified variable. If the two vectors are unit vectors, the dot product returns a floating point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Vector2 value1, ref Vector2 value2, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the two vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Equals_" data-uid="VRageMath.Vector2.Equals*"></a>
  <h4 id="VRageMath_Vector2_Equals_System_Object_" data-uid="VRageMath.Vector2.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Equals_" data-uid="VRageMath.Vector2.Equals*"></a>
  <h4 id="VRageMath_Vector2_Equals_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Equals(VRageMath.Vector2)">Equals(Vector2)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Vector2.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector2 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Vector2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Floor_" data-uid="VRageMath.Vector2.Floor*"></a>
  <h4 id="VRageMath_Vector2_Floor_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Floor(VRageMath.Vector2)">Floor(Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Floor(Vector2 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_GetHashCode_" data-uid="VRageMath.Vector2.GetHashCode*"></a>
  <h4 id="VRageMath_Vector2_GetHashCode" data-uid="VRageMath.Vector2.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of the vector object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Hermite_" data-uid="VRageMath.Vector2.Hermite*"></a>
  <h4 id="VRageMath_Vector2_Hermite_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.Hermite(VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,VRageMath.Vector2,System.Single)">Hermite(Vector2, Vector2, Vector2, Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Hermite(Vector2 value1, Vector2 tangent1, Vector2 value2, Vector2 tangent2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Hermite_" data-uid="VRageMath.Vector2.Hermite*"></a>
  <h4 id="VRageMath_Vector2_Hermite_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.Hermite(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">Hermite(ref Vector2, ref Vector2, ref Vector2, ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Hermite(ref Vector2 value1, ref Vector2 tangent1, ref Vector2 value2, ref Vector2 tangent2, float amount, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the Hermite spline interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_IsValid_" data-uid="VRageMath.Vector2.IsValid*"></a>
  <h4 id="VRageMath_Vector2_IsValid" data-uid="VRageMath.Vector2.IsValid">IsValid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsValid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_IsZero_" data-uid="VRageMath.Vector2.IsZero*"></a>
  <h4 id="VRageMath_Vector2_IsZero_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.IsZero(VRageMath.Vector2,System.Single)">IsZero(Vector2, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(Vector2 value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_IsZero_" data-uid="VRageMath.Vector2.IsZero*"></a>
  <h4 id="VRageMath_Vector2_IsZero_VRageMath_Vector2__" data-uid="VRageMath.Vector2.IsZero(VRageMath.Vector2@)">IsZero(ref Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_IsZero_" data-uid="VRageMath.Vector2.IsZero*"></a>
  <h4 id="VRageMath_Vector2_IsZero_VRageMath_Vector2__System_Single_" data-uid="VRageMath.Vector2.IsZero(VRageMath.Vector2@,System.Single)">IsZero(ref Vector2, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(ref Vector2 value, float epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Length_" data-uid="VRageMath.Vector2.Length*"></a>
  <h4 id="VRageMath_Vector2_Length" data-uid="VRageMath.Vector2.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_LengthSquared_" data-uid="VRageMath.Vector2.LengthSquared*"></a>
  <h4 id="VRageMath_Vector2_LengthSquared" data-uid="VRageMath.Vector2.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Lerp_" data-uid="VRageMath.Vector2.Lerp*"></a>
  <h4 id="VRageMath_Vector2_Lerp_VRageMath_Vector2_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.Lerp(VRageMath.Vector2,VRageMath.Vector2,System.Single)">Lerp(Vector2, Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Lerp(Vector2 value1, Vector2 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Lerp_" data-uid="VRageMath.Vector2.Lerp*"></a>
  <h4 id="VRageMath_Vector2_Lerp_VRageMath_Vector2__VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.Lerp(VRageMath.Vector2@,VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">Lerp(ref Vector2, ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Vector2 value1, ref Vector2 value2, float amount, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Max_" data-uid="VRageMath.Vector2.Max*"></a>
  <h4 id="VRageMath_Vector2_Max_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Max(VRageMath.Vector2,VRageMath.Vector2)">Max(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Max(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Max_" data-uid="VRageMath.Vector2.Max*"></a>
  <h4 id="VRageMath_Vector2_Max_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Max(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Max(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Max(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The maximized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Min_" data-uid="VRageMath.Vector2.Min*"></a>
  <h4 id="VRageMath_Vector2_Min_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Min(VRageMath.Vector2,VRageMath.Vector2)">Min(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Min(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Min_" data-uid="VRageMath.Vector2.Min*"></a>
  <h4 id="VRageMath_Vector2_Min_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Min(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Min(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Min(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The minimized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Multiply_" data-uid="VRageMath.Vector2.Multiply*"></a>
  <h4 id="VRageMath_Vector2_Multiply_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.Multiply(VRageMath.Vector2,System.Single)">Multiply(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Multiply(Vector2 value1, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Multiply_" data-uid="VRageMath.Vector2.Multiply*"></a>
  <h4 id="VRageMath_Vector2_Multiply_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Multiply(VRageMath.Vector2,VRageMath.Vector2)">Multiply(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Multiply(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Multiply_" data-uid="VRageMath.Vector2.Multiply*"></a>
  <h4 id="VRageMath_Vector2_Multiply_VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.Multiply(VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">Multiply(ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector2 value1, float scaleFactor, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Multiply_" data-uid="VRageMath.Vector2.Multiply*"></a>
  <h4 id="VRageMath_Vector2_Multiply_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Multiply(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Multiply(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Negate_" data-uid="VRageMath.Vector2.Negate*"></a>
  <h4 id="VRageMath_Vector2_Negate_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Negate(VRageMath.Vector2)">Negate(Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Negate(Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Negate_" data-uid="VRageMath.Vector2.Negate*"></a>
  <h4 id="VRageMath_Vector2_Negate_VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Negate(VRageMath.Vector2@,VRageMath.Vector2@)">Negate(ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Vector2 value, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Vector pointing in the opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Normalize_" data-uid="VRageMath.Vector2.Normalize*"></a>
  <h4 id="VRageMath_Vector2_Normalize" data-uid="VRageMath.Vector2.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Turns the current vector into a unit vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector2_Normalize_" data-uid="VRageMath.Vector2.Normalize*"></a>
  <h4 id="VRageMath_Vector2_Normalize_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Normalize(VRageMath.Vector2)">Normalize(Vector2)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Normalize(Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source Vector2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Normalize_" data-uid="VRageMath.Vector2.Normalize*"></a>
  <h4 id="VRageMath_Vector2_Normalize_VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Normalize(VRageMath.Vector2@,VRageMath.Vector2@)">Normalize(ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector, writing the result to a user-specified variable. The result is a vector one unit in length pointing in the same direction as the original vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector2 value, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Normalized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Reflect_" data-uid="VRageMath.Vector2.Reflect*"></a>
  <h4 id="VRageMath_Vector2_Reflect_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Reflect(VRageMath.Vector2,VRageMath.Vector2)">Reflect(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Determines the reflect vector of the given vector and normal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Reflect(Vector2 vector, Vector2 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Reflect_" data-uid="VRageMath.Vector2.Reflect*"></a>
  <h4 id="VRageMath_Vector2_Reflect_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Reflect(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Reflect(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Determines the reflect vector of the given vector and normal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Reflect(ref Vector2 vector, ref Vector2 normal, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>Normal of vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created reflect vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Rotate_" data-uid="VRageMath.Vector2.Rotate*"></a>
  <h4 id="VRageMath_Vector2_Rotate_System_Double_" data-uid="VRageMath.Vector2.Rotate(System.Double)">Rotate(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Rotate(double angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_SignNonZero_" data-uid="VRageMath.Vector2.SignNonZero*"></a>
  <h4 id="VRageMath_Vector2_SignNonZero_VRageMath_Vector2_" data-uid="VRageMath.Vector2.SignNonZero(VRageMath.Vector2)">SignNonZero(Vector2)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 SignNonZero(Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_SmoothStep_" data-uid="VRageMath.Vector2.SmoothStep*"></a>
  <h4 id="VRageMath_Vector2_SmoothStep_VRageMath_Vector2_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.SmoothStep(VRageMath.Vector2,VRageMath.Vector2,System.Single)">SmoothStep(Vector2, Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 SmoothStep(Vector2 value1, Vector2 value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_SmoothStep_" data-uid="VRageMath.Vector2.SmoothStep*"></a>
  <h4 id="VRageMath_Vector2_SmoothStep_VRageMath_Vector2__VRageMath_Vector2__System_Single_VRageMath_Vector2__" data-uid="VRageMath.Vector2.SmoothStep(VRageMath.Vector2@,VRageMath.Vector2@,System.Single,VRageMath.Vector2@)">SmoothStep(ref Vector2, ref Vector2, Single, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SmoothStep(ref Vector2 value1, ref Vector2 value2, float amount, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The interpolated value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Subtract_" data-uid="VRageMath.Vector2.Subtract*"></a>
  <h4 id="VRageMath_Vector2_Subtract_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.Subtract(VRageMath.Vector2,VRageMath.Vector2)">Subtract(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Subtract(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Subtract_" data-uid="VRageMath.Vector2.Subtract*"></a>
  <h4 id="VRageMath_Vector2_Subtract_VRageMath_Vector2__VRageMath_Vector2__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Subtract(VRageMath.Vector2@,VRageMath.Vector2@,VRageMath.Vector2@)">Subtract(ref Vector2, ref Vector2, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Vector2 value1, ref Vector2 value2, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_ToString_" data-uid="VRageMath.Vector2.ToString*"></a>
  <h4 id="VRageMath_Vector2_ToString" data-uid="VRageMath.Vector2.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2_VRageMath_Matrix_" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2,VRageMath.Matrix)">Transform(Vector2, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms the vector (x, y, 0, 1) by the specified matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Transform(Vector2 position, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2_VRageMath_Quaternion_" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2,VRageMath.Quaternion)">Transform(Vector2, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a single Vector2, or the vector normal (x, y, 0, 0), by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 Transform(Vector2 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The vector to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2__VRageMath_Matrix__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2@,VRageMath.Matrix@,VRageMath.Vector2@)">Transform(ref Vector2, ref Matrix, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 position, ref Matrix matrix, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector2 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2__VRageMath_Quaternion__VRageMath_Vector2__" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2@,VRageMath.Quaternion@,VRageMath.Vector2@)">Transform(ref Vector2, ref Quaternion, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2, or the vector normal (x, y, 0, 0), by a specified Quaternion rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 value, ref Quaternion rotation, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The vector to rotate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Vector2 filled in with the result of the rotation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2___System_Int32_VRageMath_Matrix__VRageMath_Vector2___System_Int32_System_Int32_" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2[],System.Int32,VRageMath.Matrix@,VRageMath.Vector2[],System.Int32,System.Int32)">Transform(Vector2[], Int32, ref Matrix, Vector2[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2s by a specified Matrix and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2[] sourceArray, int sourceIndex, ref Matrix matrix, Vector2[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2 to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The Matrix to transform by.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s will be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2 should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector2s to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2___System_Int32_VRageMath_Quaternion__VRageMath_Vector2___System_Int32_System_Int32_" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2[],System.Int32,VRageMath.Quaternion@,VRageMath.Vector2[],System.Int32,System.Int32)">Transform(Vector2[], Int32, ref Quaternion, Vector2[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2s by a specified Quaternion and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2[] sourceArray, int sourceIndex, ref Quaternion rotation, Vector2[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2 to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s are written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2 should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector2s to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2___VRageMath_Matrix__VRageMath_Vector2___" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2[],VRageMath.Matrix@,VRageMath.Vector2[])">Transform(Vector2[], ref Matrix, Vector2[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2s by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2[] sourceArray, ref Matrix matrix, Vector2[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector2s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed Vector2s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_Transform_" data-uid="VRageMath.Vector2.Transform*"></a>
  <h4 id="VRageMath_Vector2_Transform_VRageMath_Vector2___VRageMath_Quaternion__VRageMath_Vector2___" data-uid="VRageMath.Vector2.Transform(VRageMath.Vector2[],VRageMath.Quaternion@,VRageMath.Vector2[])">Transform(Vector2[], ref Quaternion, Vector2[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2s by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector2[] sourceArray, ref Quaternion rotation, Vector2[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector2s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The transform Matrix to use.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed Vector2s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_TransformNormal_" data-uid="VRageMath.Vector2.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2_TransformNormal_VRageMath_Vector2_VRageMath_Matrix_" data-uid="VRageMath.Vector2.TransformNormal(VRageMath.Vector2,VRageMath.Matrix)">TransformNormal(Vector2, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a 2D vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 TransformNormal(Vector2 normal, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_TransformNormal_" data-uid="VRageMath.Vector2.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2_TransformNormal_VRageMath_Vector2__VRageMath_Matrix__VRageMath_Vector2__" data-uid="VRageMath.Vector2.TransformNormal(VRageMath.Vector2@,VRageMath.Matrix@,VRageMath.Vector2@)">TransformNormal(ref Vector2, ref Matrix, out Vector2)</h4>
  <div class="markdown level1 summary"><p>Transforms a vector normal by a matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(ref Vector2 normal, ref Matrix matrix, out Vector2 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector2 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_TransformNormal_" data-uid="VRageMath.Vector2.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2_TransformNormal_VRageMath_Vector2___System_Int32_VRageMath_Matrix__VRageMath_Vector2___System_Int32_System_Int32_" data-uid="VRageMath.Vector2.TransformNormal(VRageMath.Vector2[],System.Int32,VRageMath.Matrix@,VRageMath.Vector2[],System.Int32,System.Int32)">TransformNormal(Vector2[], Int32, ref Matrix, Vector2[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector2 vector normals by a specified Matrix and places the results in a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector2[] sourceArray, int sourceIndex, ref Matrix matrix, Vector2[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The source array.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index of the first Vector2 to transform in the source array.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The destination array into which the resulting Vector2s are written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index of the position in the destination array where the first result Vector2 should be written.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of vector normals to be transformed.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_TransformNormal_" data-uid="VRageMath.Vector2.TransformNormal*"></a>
  <h4 id="VRageMath_Vector2_TransformNormal_VRageMath_Vector2___VRageMath_Matrix__VRageMath_Vector2___" data-uid="VRageMath.Vector2.TransformNormal(VRageMath.Vector2[],VRageMath.Matrix@,VRageMath.Vector2[])">TransformNormal(Vector2[], ref Matrix, Vector2[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector2 vector normals by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void TransformNormal(Vector2[] sourceArray, ref Matrix matrix, Vector2[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of vector normals to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>An existing array into which the transformed vector normals are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Vector2_op_Addition_" data-uid="VRageMath.Vector2.op_Addition*"></a>
  <h4 id="VRageMath_Vector2_op_Addition_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.op_Addition(VRageMath.Vector2,System.Single)">Addition(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Adds float to each component of a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator +(Vector2 value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source float.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Addition_" data-uid="VRageMath.Vector2.op_Addition*"></a>
  <h4 id="VRageMath_Vector2_op_Addition_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Addition(VRageMath.Vector2,VRageMath.Vector2)">Addition(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator +(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Division_" data-uid="VRageMath.Vector2.op_Division*"></a>
  <h4 id="VRageMath_Vector2_op_Division_System_Single_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Division(System.Single,VRageMath.Vector2)">Division(Single, Vector2)</h4>
  <div class="markdown level1 summary"><p>Divides a scalar value by a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator /(float value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Division_" data-uid="VRageMath.Vector2.op_Division*"></a>
  <h4 id="VRageMath_Vector2_op_Division_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.op_Division(VRageMath.Vector2,System.Single)">Division(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator /(Vector2 value1, float divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Division_" data-uid="VRageMath.Vector2.op_Division*"></a>
  <h4 id="VRageMath_Vector2_op_Division_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Division(VRageMath.Vector2,VRageMath.Vector2)">Division(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator /(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Equality_" data-uid="VRageMath.Vector2.op_Equality*"></a>
  <h4 id="VRageMath_Vector2_op_Equality_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Equality(VRageMath.Vector2,VRageMath.Vector2)">Equality(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Explicit_" data-uid="VRageMath.Vector2.op_Explicit*"></a>
  <h4 id="VRageMath_Vector2_op_Explicit_VRageMath_Vector2__VRageMath_Vector2I" data-uid="VRageMath.Vector2.op_Explicit(VRageMath.Vector2)~VRageMath.Vector2I">Explicit(Vector2 to Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static explicit operator Vector2I(Vector2 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">vector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Inequality_" data-uid="VRageMath.Vector2.op_Inequality*"></a>
  <h4 id="VRageMath_Vector2_op_Inequality_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Inequality(VRageMath.Vector2,VRageMath.Vector2)">Inequality(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Multiply_" data-uid="VRageMath.Vector2.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2_op_Multiply_System_Single_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Multiply(System.Single,VRageMath.Vector2)">Multiply(Single, Vector2)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator *(float scaleFactor, Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Multiply_" data-uid="VRageMath.Vector2.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2_op_Multiply_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.op_Multiply(VRageMath.Vector2,System.Single)">Multiply(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator *(Vector2 value, float scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Multiply_" data-uid="VRageMath.Vector2.op_Multiply*"></a>
  <h4 id="VRageMath_Vector2_op_Multiply_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Multiply(VRageMath.Vector2,VRageMath.Vector2)">Multiply(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator *(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Subtraction_" data-uid="VRageMath.Vector2.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector2_op_Subtraction_VRageMath_Vector2_System_Single_" data-uid="VRageMath.Vector2.op_Subtraction(VRageMath.Vector2,System.Single)">Subtraction(Vector2, Single)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator -(Vector2 value1, float value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">value2</span></td>
        <td><p>source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_Subtraction_" data-uid="VRageMath.Vector2.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector2_op_Subtraction_VRageMath_Vector2_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_Subtraction(VRageMath.Vector2,VRageMath.Vector2)">Subtraction(Vector2, Vector2)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator -(Vector2 value1, Vector2 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector2_op_UnaryNegation_" data-uid="VRageMath.Vector2.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Vector2_op_UnaryNegation_VRageMath_Vector2_" data-uid="VRageMath.Vector2.op_UnaryNegation(VRageMath.Vector2)">UnaryNegation(Vector2)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector2 operator -(Vector2 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
