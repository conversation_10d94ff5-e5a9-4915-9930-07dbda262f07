﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyDynamicAABBTreeD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyDynamicAABBTreeD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.MyDynamicAABBTreeD">
  
  
  <h1 id="VRageMath_MyDynamicAABBTreeD" data-uid="VRageMath.MyDynamicAABBTreeD" class="text-break">Class MyDynamicAABBTreeD
  </h1>
  <div class="markdown level0 summary"><p>Dynamic aabb tree implementation as a prunning structure</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyDynamicAABBTreeD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_MyDynamicAABBTreeD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class MyDynamicAABBTreeD : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD__ctor_" data-uid="VRageMath.MyDynamicAABBTreeD.#ctor*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD__ctor" data-uid="VRageMath.MyDynamicAABBTreeD.#ctor">MyDynamicAABBTreeD()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyDynamicAABBTreeD()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD__ctor_" data-uid="VRageMath.MyDynamicAABBTreeD.#ctor*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD__ctor_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.MyDynamicAABBTreeD.#ctor(VRageMath.Vector3D,System.Double)">MyDynamicAABBTreeD(Vector3D, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyDynamicAABBTreeD(Vector3D extension, double aabbMultiplier = 1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">extension</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">aabbMultiplier</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_MyDynamicAABBTreeD_NullNode" data-uid="VRageMath.MyDynamicAABBTreeD.NullNode">NullNode</h4>
  <div class="markdown level1 summary"><p>A dynamic tree arranges data in a binary tree to accelerate
queries such as volume queries and ray casts. Leafs are proxies
with an BoundingBox. In the tree we expand the proxy BoundingBox by Settings.b2_fatAABBFactor
so that the proxy BoundingBox is bigger than the client object. This allows the client
object to move by small amounts without triggering a tree update.
Nodes are pooled and relocatable, so we use node indices rather than pointers.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public const int NullNode = -1</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_ElementsCount_" data-uid="VRageMath.MyDynamicAABBTreeD.ElementsCount*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_ElementsCount" data-uid="VRageMath.MyDynamicAABBTreeD.ElementsCount">ElementsCount</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int ElementsCount { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_AddProxy_" data-uid="VRageMath.MyDynamicAABBTreeD.AddProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_AddProxy_VRageMath_BoundingBoxD__System_Object_System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.AddProxy(VRageMath.BoundingBoxD@,System.Object,System.UInt32,System.Boolean)">AddProxy(ref BoundingBoxD, Object, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"><p>Create a proxy. Provide a tight fitting BoundingBox and a userData pointer.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int AddProxy(ref BoundingBoxD aabb, object userData, uint userFlags, bool rebalance = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">aabb</span></td>
        <td><p>The aabb.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">userData</span></td>
        <td><p>The user data.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">userFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">rebalance</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_Balance_" data-uid="VRageMath.MyDynamicAABBTreeD.Balance*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_Balance_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.Balance(System.Int32)">Balance(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Balance(int iA)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">iA</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_Clear_" data-uid="VRageMath.MyDynamicAABBTreeD.Clear*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_Clear" data-uid="VRageMath.MyDynamicAABBTreeD.Clear">Clear()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_CountLeaves_" data-uid="VRageMath.MyDynamicAABBTreeD.CountLeaves*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_CountLeaves_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.CountLeaves(System.Int32)">CountLeaves(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int CountLeaves(int nodeId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">nodeId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_Dispose_" data-uid="VRageMath.MyDynamicAABBTreeD.Dispose*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_Dispose" data-uid="VRageMath.MyDynamicAABBTreeD.Dispose">Dispose()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dispose()</code></pre>
  </div>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAabb_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAabb*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAabb_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAabb(System.Int32)">GetAabb(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBoxD GetAabb(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAll_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAll__1_System_Action___0_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll``1(System.Action{``0,VRageMath.BoundingBoxD})">GetAll&lt;T&gt;(Action&lt;T, BoundingBoxD&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAll&lt;T&gt;(Action&lt;T, BoundingBoxD&gt; add)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T, <a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a>&gt;</td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAll_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAll__1_System_Action___0__" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll``1(System.Action{``0})">GetAll&lt;T&gt;(Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAll&lt;T&gt;(Action&lt;T&gt; add)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T&gt;</td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAll_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAll__1_System_Collections_Generic_List___0__System_Boolean_System_Collections_Generic_List_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.GetAll``1(System.Collections.Generic.List{``0},System.Boolean,System.Collections.Generic.List{VRageMath.BoundingBoxD})">GetAll&lt;T&gt;(List&lt;T&gt;, Boolean, List&lt;BoundingBoxD&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAll&lt;T&gt;(List&lt;T&gt; elementsList, bool clear, List&lt;BoundingBoxD&gt; boxsList = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a>&gt;</td>
        <td><span class="parametername">boxsList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAllNodeBounds_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAllNodeBounds*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAllNodeBounds_System_Collections_Generic_List_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.GetAllNodeBounds(System.Collections.Generic.List{VRageMath.BoundingBoxD})">GetAllNodeBounds(List&lt;BoundingBoxD&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAllNodeBounds(List&lt;BoundingBoxD&gt; boxsList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a>&gt;</td>
        <td><span class="parametername">boxsList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAproximateClustersForAabb_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAproximateClustersForAabb*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAproximateClustersForAabb_VRageMath_BoundingBoxD__System_Double_System_Collections_Generic_List_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.GetAproximateClustersForAabb(VRageMath.BoundingBoxD@,System.Double,System.Collections.Generic.List{VRageMath.BoundingBoxD})">GetAproximateClustersForAabb(ref BoundingBoxD, Double, List&lt;BoundingBoxD&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAproximateClustersForAabb(ref BoundingBoxD bbox, double minSize, List&lt;BoundingBoxD&gt; boundList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a>&gt;</td>
        <td><span class="parametername">boundList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetAproximateClustersForAabbDebug_" data-uid="VRageMath.MyDynamicAABBTreeD.GetAproximateClustersForAabbDebug*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetAproximateClustersForAabbDebug_VRageMath_BoundingBoxD__System_Double_System_Collections_Generic_List_VRageMath_BoundingBoxD__System_Collections_Generic_List_VRage_MyTuple_System_Boolean_System_Int32_System_Object___" data-uid="VRageMath.MyDynamicAABBTreeD.GetAproximateClustersForAabbDebug(VRageMath.BoundingBoxD@,System.Double,System.Collections.Generic.List{VRageMath.BoundingBoxD},System.Collections.Generic.List{VRage.MyTuple{System.Boolean,System.Int32,System.Object}})">GetAproximateClustersForAabbDebug(ref BoundingBoxD, Double, List&lt;BoundingBoxD&gt;, List&lt;MyTuple&lt;Boolean, Int32, Object&gt;&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAproximateClustersForAabbDebug(ref BoundingBoxD bbox, double minSize, List&lt;BoundingBoxD&gt; boundList, List&lt;MyTuple&lt;bool, int, object&gt;&gt; nodeList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a>&gt;</td>
        <td><span class="parametername">boundList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">VRage.MyTuple</span>&lt;<span class="xref">System.Boolean</span>, <span class="xref">System.Int32</span>, <span class="xref">System.Object</span>&gt;&gt;</td>
        <td><span class="parametername">nodeList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetChildren_" data-uid="VRageMath.MyDynamicAABBTreeD.GetChildren*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetChildren_System_Int32_System_Int32__System_Int32__" data-uid="VRageMath.MyDynamicAABBTreeD.GetChildren(System.Int32,System.Int32@,System.Int32@)">GetChildren(Int32, out Int32, out Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetChildren(int proxyId, out int left, out int right)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">left</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">right</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetFatAABB_" data-uid="VRageMath.MyDynamicAABBTreeD.GetFatAABB*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetFatAABB_System_Int32_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.GetFatAABB(System.Int32,VRageMath.BoundingBoxD@)">GetFatAABB(Int32, out BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Get the fat BoundingBox for a proxy.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetFatAABB(int proxyId, out BoundingBoxD fatAABB)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">fatAABB</span></td>
        <td><p>The fat BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetHeight_" data-uid="VRageMath.MyDynamicAABBTreeD.GetHeight*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetHeight" data-uid="VRageMath.MyDynamicAABBTreeD.GetHeight">GetHeight()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetHeight()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetLeafCount_" data-uid="VRageMath.MyDynamicAABBTreeD.GetLeafCount*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetLeafCount_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.GetLeafCount(System.Int32)">GetLeafCount(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetLeafCount(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetNodeLeaves_" data-uid="VRageMath.MyDynamicAABBTreeD.GetNodeLeaves*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetNodeLeaves_System_Int32_System_Collections_Generic_List_System_Int32__" data-uid="VRageMath.MyDynamicAABBTreeD.GetNodeLeaves(System.Int32,System.Collections.Generic.List{System.Int32})">GetNodeLeaves(Int32, List&lt;Int32&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetNodeLeaves(int proxyId, List&lt;int&gt; children)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Int32</span>&gt;</td>
        <td><span class="parametername">children</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetRoot_" data-uid="VRageMath.MyDynamicAABBTreeD.GetRoot*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetRoot" data-uid="VRageMath.MyDynamicAABBTreeD.GetRoot">GetRoot()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetRoot()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_GetUserData_" data-uid="VRageMath.MyDynamicAABBTreeD.GetUserData*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_GetUserData__1_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.GetUserData``1(System.Int32)">GetUserData&lt;T&gt;(Int32)</h4>
  <div class="markdown level1 summary"><p>Get proxy user data.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public T GetUserData&lt;T&gt;(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p>the proxy user data or 0 if the id is invalid.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_IsRootNull_" data-uid="VRageMath.MyDynamicAABBTreeD.IsRootNull*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_IsRootNull" data-uid="VRageMath.MyDynamicAABBTreeD.IsRootNull">IsRootNull()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsRootNull()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_MoveProxy_" data-uid="VRageMath.MyDynamicAABBTreeD.MoveProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_MoveProxy_System_Int32_VRageMath_BoundingBoxD__VRageMath_Vector3D_" data-uid="VRageMath.MyDynamicAABBTreeD.MoveProxy(System.Int32,VRageMath.BoundingBoxD@,VRageMath.Vector3D)">MoveProxy(Int32, ref BoundingBoxD, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Move a proxy with a swepted BoundingBox. If the proxy has moved outside of its fattened BoundingBox,
then the proxy is removed from the tree and re-inserted. Otherwise
the function returns immediately.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool MoveProxy(int proxyId, ref BoundingBoxD aabb, Vector3D displacement)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">aabb</span></td>
        <td><p>The aabb.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">displacement</span></td>
        <td><p>Predicted (expected) movement</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>true if the proxy was re-inserted.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingBox_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingBox*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingBox__1_VRageMath_BoundingBoxD__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingBox``1(VRageMath.BoundingBoxD@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllBoundingBox&lt;T&gt;(ref BoundingBoxD, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingBox&lt;T&gt;(ref BoundingBoxD bbox, List&lt;T&gt; elementsList, uint requiredFlags = 0U, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingBox_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingBox*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingBox__1_VRageMath_MyOrientedBoundingBoxD__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingBox``1(VRageMath.MyOrientedBoundingBoxD@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllBoundingBox&lt;T&gt;(ref MyOrientedBoundingBoxD, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingBox&lt;T&gt;(ref MyOrientedBoundingBoxD obb, List&lt;T&gt; elementsList, uint requiredFlags = 0U, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MyOrientedBoundingBoxD.html">MyOrientedBoundingBoxD</a></td>
        <td><span class="parametername">obb</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingSphere_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingSphere*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingSphere__1_VRageMath_BoundingSphereD__System_Action___0__" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingSphere``1(VRageMath.BoundingSphereD@,System.Action{``0})">OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphereD, Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphereD sphere, Action&lt;T&gt; addAction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T&gt;</td>
        <td><span class="parametername">addAction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingSphere_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingSphere*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllBoundingSphere__1_VRageMath_BoundingSphereD__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllBoundingSphere``1(VRageMath.BoundingSphereD@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphereD, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllBoundingSphere&lt;T&gt;(ref BoundingSphereD sphere, List&lt;T&gt; overlapElementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">overlapElementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD____0_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,``0)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, T results)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">results</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD____0_System_Single_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,``0,System.Single)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, T, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, T results, float tSqr)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">results</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD__System_Action___0_System_Boolean__System_Single_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,System.Action{``0,System.Boolean},System.Single)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, Action&lt;T, Boolean&gt;, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, Action&lt;T, bool&gt; add, float tSqr)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Action</span>&lt;T, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">add</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, List&lt;T&gt; elementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD__System_Collections_Generic_List___0__System_Collections_Generic_List_System_Boolean__" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,System.Collections.Generic.List{``0},System.Collections.Generic.List{System.Boolean})">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, List&lt;T&gt;, List&lt;Boolean&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, List&lt;T&gt; elementsList, List&lt;bool&gt; isInsideList)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">isInsideList</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD__System_Collections_Generic_List___0__System_Collections_Generic_List_System_Boolean__System_Single_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,System.Collections.Generic.List{``0},System.Collections.Generic.List{System.Boolean},System.Single,System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, List&lt;T&gt;, List&lt;Boolean&gt;, Single, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, List&lt;T&gt; elementsList, List&lt;bool&gt; isInsideList, float tSqr, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">isInsideList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">tSqr</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustum__1_VRageMath_BoundingFrustumD__System_Collections_Generic_List___0__System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustum``1(VRageMath.BoundingFrustumD@,System.Collections.Generic.List{``0},System.UInt32,System.Boolean)">OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD, List&lt;T&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustum&lt;T&gt;(ref BoundingFrustumD frustum, List&lt;T&gt; elementsList, uint requiredFlags, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustumAny_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustumAny*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllFrustumAny__1_VRageMath_BoundingFrustumD__System_Collections_Generic_List___0__System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllFrustumAny``1(VRageMath.BoundingFrustumD@,System.Collections.Generic.List{``0},System.Boolean)">OverlapAllFrustumAny&lt;T&gt;(ref BoundingFrustumD, List&lt;T&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllFrustumAny&lt;T&gt;(ref BoundingFrustumD frustum, List&lt;T&gt; elementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;T&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllLineSegment_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllLineSegment*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllLineSegment__1_VRageMath_LineD__System_Collections_Generic_List_VRageMath_MyLineSegmentOverlapResult___0___System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllLineSegment``1(VRageMath.LineD@,System.Collections.Generic.List{VRageMath.MyLineSegmentOverlapResult{``0}},System.Boolean)">OverlapAllLineSegment&lt;T&gt;(ref LineD, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt;, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllLineSegment&lt;T&gt;(ref LineD line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt; elementsList, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.html">MyLineSegmentOverlapResult</a>&lt;T&gt;&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapAllLineSegment_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllLineSegment*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapAllLineSegment__1_VRageMath_LineD__System_Collections_Generic_List_VRageMath_MyLineSegmentOverlapResult___0___System_UInt32_System_Boolean_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapAllLineSegment``1(VRageMath.LineD@,System.Collections.Generic.List{VRageMath.MyLineSegmentOverlapResult{``0}},System.UInt32,System.Boolean)">OverlapAllLineSegment&lt;T&gt;(ref LineD, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt;, UInt32, Boolean)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void OverlapAllLineSegment&lt;T&gt;(ref LineD line, List&lt;MyLineSegmentOverlapResult&lt;T&gt;&gt; elementsList, uint requiredFlags, bool clear = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.LineD.html">LineD</a></td>
        <td><span class="parametername">line</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRageMath.MyLineSegmentOverlapResult-1.html">MyLineSegmentOverlapResult</a>&lt;T&gt;&gt;</td>
        <td><span class="parametername">elementsList</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">requiredFlags</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">clear</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_OverlapsAnyLeafBoundingBox_" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapsAnyLeafBoundingBox*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_OverlapsAnyLeafBoundingBox_VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.OverlapsAnyLeafBoundingBox(VRageMath.BoundingBoxD@)">OverlapsAnyLeafBoundingBox(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool OverlapsAnyLeafBoundingBox(ref BoundingBoxD bbox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">bbox</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_Query_" data-uid="VRageMath.MyDynamicAABBTreeD.Query*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_Query_System_Func_System_Int32_System_Boolean__VRageMath_BoundingBoxD__" data-uid="VRageMath.MyDynamicAABBTreeD.Query(System.Func{System.Int32,System.Boolean},VRageMath.BoundingBoxD@)">Query(Func&lt;Int32, Boolean&gt;, ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Query(Func&lt;int, bool&gt; callback, ref BoundingBoxD aabb)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<span class="xref">System.Int32</span>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">aabb</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_QueryPoint_" data-uid="VRageMath.MyDynamicAABBTreeD.QueryPoint*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_QueryPoint_System_Func_System_Int32_System_Boolean__VRageMath_Vector3D__" data-uid="VRageMath.MyDynamicAABBTreeD.QueryPoint(System.Func{System.Int32,System.Boolean},VRageMath.Vector3D@)">QueryPoint(Func&lt;Int32, Boolean&gt;, ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void QueryPoint(Func&lt;int, bool&gt; callback, ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<span class="xref">System.Int32</span>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_MyDynamicAABBTreeD_RemoveProxy_" data-uid="VRageMath.MyDynamicAABBTreeD.RemoveProxy*"></a>
  <h4 id="VRageMath_MyDynamicAABBTreeD_RemoveProxy_System_Int32_" data-uid="VRageMath.MyDynamicAABBTreeD.RemoveProxy(System.Int32)">RemoveProxy(Int32)</h4>
  <div class="markdown level1 summary"><p>Destroy a proxy. This asserts if the id is invalid.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveProxy(int proxyId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">proxyId</span></td>
        <td><p>The proxy id.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
