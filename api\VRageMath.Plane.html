﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Plane
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Plane
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Plane">
  
  
  <h1 id="VRageMath_Plane" data-uid="VRageMath.Plane" class="text-break">Class Plane
  </h1>
  <div class="markdown level0 summary"><p>Defines a plane.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Plane</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Plane_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Plane : ValueType, IEquatable&lt;Plane&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Plane.#ctor(System.Single,System.Single,System.Single,System.Single)">Plane(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(float a, float b, float c, float d)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>X component of the normal defining the Plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Y component of the normal defining the Plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">c</span></td>
        <td><p>Z component of the normal defining the Plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">d</span></td>
        <td><p>Distance of the origin from the plane along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_VRageMath_Vector3_System_Single_" data-uid="VRageMath.Plane.#ctor(VRageMath.Vector3,System.Single)">Plane(Vector3, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(Vector3 normal, float d)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The normal vector to the Plane.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">d</span></td>
        <td><p>Distance of the origin from the plane along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Plane.#ctor(VRageMath.Vector3,VRageMath.Vector3)">Plane(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(Vector3 position, Vector3 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>A point that lies on the Plane</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The normal vector to the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Plane.#ctor(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3)">Plane(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(Vector3 point1, Vector3 point2, Vector3 point3)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point1</span></td>
        <td><p>One point of a triangle defining the Plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point2</span></td>
        <td><p>One point of a triangle defining the Plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point3</span></td>
        <td><p>One point of a triangle defining the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_VRageMath_Vector3__VRageMath_Vector3__VRageMath_Vector3__" data-uid="VRageMath.Plane.#ctor(VRageMath.Vector3@,VRageMath.Vector3@,VRageMath.Vector3@)">Plane(ref Vector3, ref Vector3, ref Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(ref Vector3 point1, ref Vector3 point2, ref Vector3 point3)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">point3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane__ctor_" data-uid="VRageMath.Plane.#ctor*"></a>
  <h4 id="VRageMath_Plane__ctor_VRageMath_Vector4_" data-uid="VRageMath.Plane.#ctor(VRageMath.Vector4)">Plane(Vector4)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Vector4 with X, Y, and Z components defining the normal of the Plane. The W component defines the distance of the origin from the plane along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Plane_D" data-uid="VRageMath.Plane.D">D</h4>
  <div class="markdown level1 summary"><p>The distance of the Plane along its normal from the origin.
Note: Be careful! The distance is signed and is the opposite of what people usually expect.
If you look closely at the plane equation: (n dot P) - D = 0, you'll realize that D = - (n dot P) (that is, negative instead of positive)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float D</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Plane_Normal" data-uid="VRageMath.Plane.Normal">Normal</h4>
  <div class="markdown level1 summary"><p>The normal vector of the Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Normal</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Plane_Dot_" data-uid="VRageMath.Plane.Dot*"></a>
  <h4 id="VRageMath_Plane_Dot_VRageMath_Vector4_" data-uid="VRageMath.Plane.Dot(VRageMath.Vector4)">Dot(Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of a specified Vector4 and this Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Dot(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to multiply this Plane by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Dot_" data-uid="VRageMath.Plane.Dot*"></a>
  <h4 id="VRageMath_Plane_Dot_VRageMath_Vector4__System_Single__" data-uid="VRageMath.Plane.Dot(VRageMath.Vector4@,System.Single@)">Dot(ref Vector4, out Single)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of a specified Vector4 and this Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dot(ref Vector4 value, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to multiply this Plane by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the specified Vector4 and this Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_DotCoordinate_" data-uid="VRageMath.Plane.DotCoordinate*"></a>
  <h4 id="VRageMath_Plane_DotCoordinate_VRageMath_Vector3_" data-uid="VRageMath.Plane.DotCoordinate(VRageMath.Vector3)">DotCoordinate(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3 and the Normal vector of this Plane plus the distance (D) value of the Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float DotCoordinate(Vector3 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to multiply by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_DotCoordinate_" data-uid="VRageMath.Plane.DotCoordinate*"></a>
  <h4 id="VRageMath_Plane_DotCoordinate_VRageMath_Vector3__System_Single__" data-uid="VRageMath.Plane.DotCoordinate(VRageMath.Vector3@,System.Single@)">DotCoordinate(ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3 and the Normal vector of this Plane plus the distance (D) value of the Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DotCoordinate(ref Vector3 value, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to multiply by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_DotNormal_" data-uid="VRageMath.Plane.DotNormal*"></a>
  <h4 id="VRageMath_Plane_DotNormal_VRageMath_Vector3__System_Single__" data-uid="VRageMath.Plane.DotNormal(VRageMath.Vector3@,System.Single@)">DotNormal(ref Vector3, out Single)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3 and the Normal vector of this Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DotNormal(ref Vector3 value, out float result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to multiply by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting dot product.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_DotNormal_" data-uid="VRageMath.Plane.DotNormal*"></a>
  <h4 id="VRageMath_Plane_DotNormal_VRageMath_Vector3D_" data-uid="VRageMath.Plane.DotNormal(VRageMath.Vector3D)">DotNormal(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3 and the Normal vector of this Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DotNormal(Vector3D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to multiply by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Equals_" data-uid="VRageMath.Plane.Equals*"></a>
  <h4 id="VRageMath_Plane_Equals_System_Object_" data-uid="VRageMath.Plane.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Equals_" data-uid="VRageMath.Plane.Equals*"></a>
  <h4 id="VRageMath_Plane_Equals_VRageMath_Plane_" data-uid="VRageMath.Plane.Equals(VRageMath.Plane)">Equals(Plane)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Plane is equal to the Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Plane other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Plane to compare with the current Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_GetHashCode_" data-uid="VRageMath.Plane.GetHashCode*"></a>
  <h4 id="VRageMath_Plane_GetHashCode" data-uid="VRageMath.Plane.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersection_" data-uid="VRageMath.Plane.Intersection*"></a>
  <h4 id="VRageMath_Plane_Intersection_VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRageMath.Plane.Intersection(VRageMath.Vector3D@,VRageMath.Vector3D@)">Intersection(ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"><p>Gets intersection point in Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Intersection(ref Vector3D from, ref Vector3D direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">from</span></td>
        <td><p>Starting point of a ray.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>Ray direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><p>Point of intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersects_" data-uid="VRageMath.Plane.Intersects*"></a>
  <h4 id="VRageMath_Plane_Intersects_VRageMath_BoundingBox_" data-uid="VRageMath.Plane.Intersects(VRageMath.BoundingBox)">Intersects(BoundingBox)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Plane intersects a specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingBox box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to test for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersects_" data-uid="VRageMath.Plane.Intersects*"></a>
  <h4 id="VRageMath_Plane_Intersects_VRageMath_BoundingBox__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.Plane.Intersects(VRageMath.BoundingBox@,VRageMath.PlaneIntersectionType@)">Intersects(ref BoundingBox, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Plane intersects a BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox box, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the Plane intersects the BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersects_" data-uid="VRageMath.Plane.Intersects*"></a>
  <h4 id="VRageMath_Plane_Intersects_VRageMath_BoundingFrustum_" data-uid="VRageMath.Plane.Intersects(VRageMath.BoundingFrustum)">Intersects(BoundingFrustum)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Plane intersects a specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingFrustum frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustum.html">BoundingFrustum</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersects_" data-uid="VRageMath.Plane.Intersects*"></a>
  <h4 id="VRageMath_Plane_Intersects_VRageMath_BoundingSphere_" data-uid="VRageMath.Plane.Intersects(VRageMath.BoundingSphere)">Intersects(BoundingSphere)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Plane intersects a specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingSphere sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Intersects_" data-uid="VRageMath.Plane.Intersects*"></a>
  <h4 id="VRageMath_Plane_Intersects_VRageMath_BoundingSphere__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.Plane.Intersects(VRageMath.BoundingSphere@,VRageMath.PlaneIntersectionType@)">Intersects(ref BoundingSphere, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current Plane intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphere sphere, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the Plane intersects the BoundingSphere.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Normalize_" data-uid="VRageMath.Plane.Normalize*"></a>
  <h4 id="VRageMath_Plane_Normalize" data-uid="VRageMath.Plane.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of this Plane to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Plane_Normalize_" data-uid="VRageMath.Plane.Normalize*"></a>
  <h4 id="VRageMath_Plane_Normalize_VRageMath_Plane_" data-uid="VRageMath.Plane.Normalize(VRageMath.Plane)">Normalize(Plane)</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of a Plane to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Plane Normalize(Plane value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane to normalize.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Normalize_" data-uid="VRageMath.Plane.Normalize*"></a>
  <h4 id="VRageMath_Plane_Normalize_VRageMath_Plane__VRageMath_Plane__" data-uid="VRageMath.Plane.Normalize(VRageMath.Plane@,VRageMath.Plane@)">Normalize(ref Plane, out Plane)</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of a Plane to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Plane value, out Plane result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Plane to normalize.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing plane Plane filled in with a normalized version of the specified plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_RandomPoint_" data-uid="VRageMath.Plane.RandomPoint*"></a>
  <h4 id="VRageMath_Plane_RandomPoint" data-uid="VRageMath.Plane.RandomPoint">RandomPoint()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 RandomPoint()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_ToString_" data-uid="VRageMath.Plane.ToString*"></a>
  <h4 id="VRageMath_Plane_ToString" data-uid="VRageMath.Plane.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Transform_" data-uid="VRageMath.Plane.Transform*"></a>
  <h4 id="VRageMath_Plane_Transform_VRageMath_Plane_VRageMath_Matrix_" data-uid="VRageMath.Plane.Transform(VRageMath.Plane,VRageMath.Matrix)">Transform(Plane, Matrix)</h4>
  <div class="markdown level1 summary"><p>Transforms a normalized Plane by a Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Plane Transform(Plane plane, Matrix matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The normalized Plane to transform. This Plane must already be normalized, so that its Normal vector is of unit length, before this method is called.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply to the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_Transform_" data-uid="VRageMath.Plane.Transform*"></a>
  <h4 id="VRageMath_Plane_Transform_VRageMath_Plane__VRageMath_Matrix__VRageMath_Plane__" data-uid="VRageMath.Plane.Transform(VRageMath.Plane@,VRageMath.Matrix@,VRageMath.Plane@)">Transform(ref Plane, ref Matrix, out Plane)</h4>
  <div class="markdown level1 summary"><p>Transforms a normalized Plane by a Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Plane plane, ref Matrix matrix, out Plane result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The normalized Plane to transform. This Plane must already be normalized, so that its Normal vector is of unit length, before this method is called.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Matrix.html">Matrix</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply to the Plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing Plane filled in with the results of applying the transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Plane_op_Equality_" data-uid="VRageMath.Plane.op_Equality*"></a>
  <h4 id="VRageMath_Plane_op_Equality_VRageMath_Plane_VRageMath_Plane_" data-uid="VRageMath.Plane.op_Equality(VRageMath.Plane,VRageMath.Plane)">Equality(Plane, Plane)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of Plane are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Plane lhs, Plane rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">lhs</span></td>
        <td><p>The object to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">rhs</span></td>
        <td><p>The object to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Plane_op_Inequality_" data-uid="VRageMath.Plane.op_Inequality*"></a>
  <h4 id="VRageMath_Plane_op_Inequality_VRageMath_Plane_VRageMath_Plane_" data-uid="VRageMath.Plane.op_Inequality(VRageMath.Plane,VRageMath.Plane)">Inequality(Plane, Plane)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of Plane are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Plane lhs, Plane rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">lhs</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Plane.html">Plane</a></td>
        <td><span class="parametername">rhs</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
