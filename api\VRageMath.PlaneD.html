﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class PlaneD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class PlaneD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.PlaneD">
  
  
  <h1 id="VRageMath_PlaneD" data-uid="VRageMath.PlaneD" class="text-break">Class PlaneD
  </h1>
  <div class="markdown level0 summary"><p>Defines a PlaneD.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">PlaneD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_PlaneD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class PlaneD : ValueType, IEquatable&lt;PlaneD&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.PlaneD.#ctor(System.Double,System.Double,System.Double,System.Double)">PlaneD(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(double a, double b, double c, double d)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>X component of the normal defining the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Y component of the normal defining the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">c</span></td>
        <td><p>Z component of the normal defining the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">d</span></td>
        <td><p>Distance of the origin from the PlaneD along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.PlaneD.#ctor(VRageMath.Vector3D,System.Double)">PlaneD(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(Vector3D normal, double d)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The normal vector to the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">d</span></td>
        <td><p>The distance of the origin from the PlaneD along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_VRageMath_Vector3D_VRageMath_Vector3_" data-uid="VRageMath.PlaneD.#ctor(VRageMath.Vector3D,VRageMath.Vector3)">PlaneD(Vector3D, Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(Vector3D position, Vector3 normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>A point that lies on the Plane</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The normal vector to the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.PlaneD.#ctor(VRageMath.Vector3D,VRageMath.Vector3D)">PlaneD(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(Vector3D position, Vector3D normal)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>A point that lies on the Plane</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">normal</span></td>
        <td><p>The normal vector to the Plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.PlaneD.#ctor(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D)">PlaneD(Vector3D, Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(Vector3D point1, Vector3D point2, Vector3D point3)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point1</span></td>
        <td><p>One point of a triangle defining the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point2</span></td>
        <td><p>One point of a triangle defining the PlaneD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point3</span></td>
        <td><p>One point of a triangle defining the PlaneD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD__ctor_" data-uid="VRageMath.PlaneD.#ctor*"></a>
  <h4 id="VRageMath_PlaneD__ctor_VRageMath_Vector4_" data-uid="VRageMath.PlaneD.#ctor(VRageMath.Vector4)">PlaneD(Vector4)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneD(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Vector4 with X, Y, and Z components defining the normal of the PlaneD. The W component defines the distance of the origin from the PlaneD along its normal.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_PlaneD_D" data-uid="VRageMath.PlaneD.D">D</h4>
  <div class="markdown level1 summary"><p>The distance of the PlaneD along its normal from the origin.
Note: Be careful! The distance is signed and is the opposite of what people usually expect.
If you look closely at the plane equation: (n dot P) + D = 0, you'll realize that D = - (n dot P) (that is, negative instead of positive)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double D</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_PlaneD_Normal" data-uid="VRageMath.PlaneD.Normal">Normal</h4>
  <div class="markdown level1 summary"><p>The normal vector of the PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Normal</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_PlaneD_DistanceToPoint_" data-uid="VRageMath.PlaneD.DistanceToPoint*"></a>
  <h4 id="VRageMath_PlaneD_DistanceToPoint_VRageMath_Vector3D_" data-uid="VRageMath.PlaneD.DistanceToPoint(VRageMath.Vector3D)">DistanceToPoint(Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DistanceToPoint(Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_DistanceToPoint_" data-uid="VRageMath.PlaneD.DistanceToPoint*"></a>
  <h4 id="VRageMath_PlaneD_DistanceToPoint_VRageMath_Vector3D__" data-uid="VRageMath.PlaneD.DistanceToPoint(VRageMath.Vector3D@)">DistanceToPoint(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DistanceToPoint(ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Dot_" data-uid="VRageMath.PlaneD.Dot*"></a>
  <h4 id="VRageMath_PlaneD_Dot_VRageMath_Vector4_" data-uid="VRageMath.PlaneD.Dot(VRageMath.Vector4)">Dot(Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of a specified Vector4 and this PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Dot(Vector4 value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to multiply this PlaneD by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Dot_" data-uid="VRageMath.PlaneD.Dot*"></a>
  <h4 id="VRageMath_PlaneD_Dot_VRageMath_Vector4__System_Double__" data-uid="VRageMath.PlaneD.Dot(VRageMath.Vector4@,System.Double@)">Dot(ref Vector4, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of a specified Vector4 and this PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dot(ref Vector4 value, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to multiply this PlaneD by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the specified Vector4 and this PlaneD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_DotCoordinate_" data-uid="VRageMath.PlaneD.DotCoordinate*"></a>
  <h4 id="VRageMath_PlaneD_DotCoordinate_VRageMath_Vector3D_" data-uid="VRageMath.PlaneD.DotCoordinate(VRageMath.Vector3D)">DotCoordinate(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3D and the Normal vector of this PlaneD plus the distance (D) value of the PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DotCoordinate(Vector3D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3D to multiply by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_DotCoordinate_" data-uid="VRageMath.PlaneD.DotCoordinate*"></a>
  <h4 id="VRageMath_PlaneD_DotCoordinate_VRageMath_Vector3D__System_Double__" data-uid="VRageMath.PlaneD.DotCoordinate(VRageMath.Vector3D@,System.Double@)">DotCoordinate(ref Vector3D, out Double)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3D and the Normal vector of this PlaneD plus the distance (D) value of the PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DotCoordinate(ref Vector3D value, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3D to multiply by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_DotNormal_" data-uid="VRageMath.PlaneD.DotNormal*"></a>
  <h4 id="VRageMath_PlaneD_DotNormal_VRageMath_Vector3D_" data-uid="VRageMath.PlaneD.DotNormal(VRageMath.Vector3D)">DotNormal(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3D and the Normal vector of this PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double DotNormal(Vector3D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3D to multiply by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_DotNormal_" data-uid="VRageMath.PlaneD.DotNormal*"></a>
  <h4 id="VRageMath_PlaneD_DotNormal_VRageMath_Vector3D__System_Double__" data-uid="VRageMath.PlaneD.DotNormal(VRageMath.Vector3D@,System.Double@)">DotNormal(ref Vector3D, out Double)</h4>
  <div class="markdown level1 summary"><p>Returns the dot product of a specified Vector3D and the Normal vector of this PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void DotNormal(ref Vector3D value, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3D to multiply by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting dot product.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Equals_" data-uid="VRageMath.PlaneD.Equals*"></a>
  <h4 id="VRageMath_PlaneD_Equals_System_Object_" data-uid="VRageMath.PlaneD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current PlaneD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Equals_" data-uid="VRageMath.PlaneD.Equals*"></a>
  <h4 id="VRageMath_PlaneD_Equals_VRageMath_PlaneD_" data-uid="VRageMath.PlaneD.Equals(VRageMath.PlaneD)">Equals(PlaneD)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified PlaneD is equal to the PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(PlaneD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The PlaneD to compare with the current PlaneD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_GetHashCode_" data-uid="VRageMath.PlaneD.GetHashCode*"></a>
  <h4 id="VRageMath_PlaneD_GetHashCode" data-uid="VRageMath.PlaneD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersection_" data-uid="VRageMath.PlaneD.Intersection*"></a>
  <h4 id="VRageMath_PlaneD_Intersection_VRageMath_Vector3D__VRageMath_Vector3D__" data-uid="VRageMath.PlaneD.Intersection(VRageMath.Vector3D@,VRageMath.Vector3D@)">Intersection(ref Vector3D, ref Vector3D)</h4>
  <div class="markdown level1 summary"><p>Gets intersection point in Plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D Intersection(ref Vector3D from, ref Vector3D direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">from</span></td>
        <td><p>Starting point of a ray.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>Ray direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><p>Point of intersection.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersects_" data-uid="VRageMath.PlaneD.Intersects*"></a>
  <h4 id="VRageMath_PlaneD_Intersects_VRageMath_BoundingBoxD_" data-uid="VRageMath.PlaneD.Intersects(VRageMath.BoundingBoxD)">Intersects(BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current PlaneD intersects a specified BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingBoxD box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to test for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersects_" data-uid="VRageMath.PlaneD.Intersects*"></a>
  <h4 id="VRageMath_PlaneD_Intersects_VRageMath_BoundingBoxD__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.PlaneD.Intersects(VRageMath.BoundingBoxD@,VRageMath.PlaneIntersectionType@)">Intersects(ref BoundingBoxD, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current PlaneD intersects a BoundingBox.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBoxD box, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the PlaneD intersects the BoundingBox.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersects_" data-uid="VRageMath.PlaneD.Intersects*"></a>
  <h4 id="VRageMath_PlaneD_Intersects_VRageMath_BoundingFrustumD_" data-uid="VRageMath.PlaneD.Intersects(VRageMath.BoundingFrustumD)">Intersects(BoundingFrustumD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current PlaneD intersects a specified BoundingFrustum.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingFrustumD frustum)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingFrustumD.html">BoundingFrustumD</a></td>
        <td><span class="parametername">frustum</span></td>
        <td><p>The BoundingFrustum to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersects_" data-uid="VRageMath.PlaneD.Intersects*"></a>
  <h4 id="VRageMath_PlaneD_Intersects_VRageMath_BoundingSphere__VRageMath_PlaneIntersectionType__" data-uid="VRageMath.PlaneD.Intersects(VRageMath.BoundingSphere@,VRageMath.PlaneIntersectionType@)">Intersects(ref BoundingSphere, out PlaneIntersectionType)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current PlaneD intersects a BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingSphere sphere, out PlaneIntersectionType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphere.html">BoundingSphere</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An enumeration indicating whether the PlaneD intersects the BoundingSphere.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Intersects_" data-uid="VRageMath.PlaneD.Intersects*"></a>
  <h4 id="VRageMath_PlaneD_Intersects_VRageMath_BoundingSphereD_" data-uid="VRageMath.PlaneD.Intersects(VRageMath.BoundingSphereD)">Intersects(BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current PlaneD intersects a specified BoundingSphere.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlaneIntersectionType Intersects(BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>The BoundingSphere to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneIntersectionType.html">PlaneIntersectionType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Normalize_" data-uid="VRageMath.PlaneD.Normalize*"></a>
  <h4 id="VRageMath_PlaneD_Normalize" data-uid="VRageMath.PlaneD.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of this PlaneD to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_PlaneD_Normalize_" data-uid="VRageMath.PlaneD.Normalize*"></a>
  <h4 id="VRageMath_PlaneD_Normalize_VRageMath_PlaneD_" data-uid="VRageMath.PlaneD.Normalize(VRageMath.PlaneD)">Normalize(PlaneD)</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of a PlaneD to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static PlaneD Normalize(PlaneD value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The PlaneD to normalize.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Normalize_" data-uid="VRageMath.PlaneD.Normalize*"></a>
  <h4 id="VRageMath_PlaneD_Normalize_VRageMath_PlaneD__VRageMath_PlaneD__" data-uid="VRageMath.PlaneD.Normalize(VRageMath.PlaneD@,VRageMath.PlaneD@)">Normalize(ref PlaneD, out PlaneD)</h4>
  <div class="markdown level1 summary"><p>Changes the coefficients of the Normal vector of a PlaneD to make it of unit length.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref PlaneD value, out PlaneD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The PlaneD to normalize.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing PlaneD PlaneD filled in with a normalized version of the specified PlaneD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_ProjectPoint_" data-uid="VRageMath.PlaneD.ProjectPoint*"></a>
  <h4 id="VRageMath_PlaneD_ProjectPoint_VRageMath_Vector3D__" data-uid="VRageMath.PlaneD.ProjectPoint(VRageMath.Vector3D@)">ProjectPoint(ref Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D ProjectPoint(ref Vector3D point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_RandomPoint_" data-uid="VRageMath.PlaneD.RandomPoint*"></a>
  <h4 id="VRageMath_PlaneD_RandomPoint" data-uid="VRageMath.PlaneD.RandomPoint">RandomPoint()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3D RandomPoint()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_ToString_" data-uid="VRageMath.PlaneD.ToString*"></a>
  <h4 id="VRageMath_PlaneD_ToString" data-uid="VRageMath.PlaneD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current PlaneD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Transform_" data-uid="VRageMath.PlaneD.Transform*"></a>
  <h4 id="VRageMath_PlaneD_Transform_VRageMath_PlaneD_VRageMath_MatrixD_" data-uid="VRageMath.PlaneD.Transform(VRageMath.PlaneD,VRageMath.MatrixD)">Transform(PlaneD, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a normalized plane by a Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static PlaneD Transform(PlaneD plane, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The normalized plane to transform. This plane must already be normalized, so that its Normal vector is of unit length, before this method is called.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply to the plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_Transform_" data-uid="VRageMath.PlaneD.Transform*"></a>
  <h4 id="VRageMath_PlaneD_Transform_VRageMath_PlaneD__VRageMath_MatrixD__VRageMath_PlaneD__" data-uid="VRageMath.PlaneD.Transform(VRageMath.PlaneD@,VRageMath.MatrixD@,VRageMath.PlaneD@)">Transform(ref PlaneD, ref MatrixD, out PlaneD)</h4>
  <div class="markdown level1 summary"><p>Transforms a normalized plane by a Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref PlaneD plane, ref MatrixD matrix, out PlaneD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The normalized plane to transform. This plane must already be normalized, so that its Normal vector is of unit length, before this method is called.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply to the plane.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing PlaneD filled in with the results of applying the transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_PlaneD_op_Equality_" data-uid="VRageMath.PlaneD.op_Equality*"></a>
  <h4 id="VRageMath_PlaneD_op_Equality_VRageMath_PlaneD_VRageMath_PlaneD_" data-uid="VRageMath.PlaneD.op_Equality(VRageMath.PlaneD,VRageMath.PlaneD)">Equality(PlaneD, PlaneD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of PlaneD are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(PlaneD lhs, PlaneD rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">lhs</span></td>
        <td><p>The object to the left of the equality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">rhs</span></td>
        <td><p>The object to the right of the equality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_PlaneD_op_Inequality_" data-uid="VRageMath.PlaneD.op_Inequality*"></a>
  <h4 id="VRageMath_PlaneD_op_Inequality_VRageMath_PlaneD_VRageMath_PlaneD_" data-uid="VRageMath.PlaneD.op_Inequality(VRageMath.PlaneD,VRageMath.PlaneD)">Inequality(PlaneD, PlaneD)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of PlaneD are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(PlaneD lhs, PlaneD rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">lhs</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.PlaneD.html">PlaneD</a></td>
        <td><span class="parametername">rhs</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
