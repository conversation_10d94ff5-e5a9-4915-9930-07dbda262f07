﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyGridConveyorSystem
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyGridConveyorSystem
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyGridConveyorSystem">
  
  
  <h1 id="VRage_ModAPI_IMyGridConveyorSystem" data-uid="VRage.ModAPI.IMyGridConveyorSystem" class="text-break">Interface IMyGridConveyorSystem
  </h1>
  <div class="markdown level0 summary"><p>ModAPI interface giving access to grid-group conveyor system</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyGridConveyorSystem_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyGridConveyorSystem</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyGridConveyorSystem_PullItem_" data-uid="VRage.ModAPI.IMyGridConveyorSystem.PullItem*"></a>
  <h4 id="VRage_ModAPI_IMyGridConveyorSystem_PullItem_VRage_Game_MyDefinitionId_System_Nullable_VRage_MyFixedPoint__VRage_ModAPI_IMyEntity_VRage_Game_ModAPI_IMyInventory_System_Boolean_" data-uid="VRage.ModAPI.IMyGridConveyorSystem.PullItem(VRage.Game.MyDefinitionId,System.Nullable{VRage.MyFixedPoint},VRage.ModAPI.IMyEntity,VRage.Game.ModAPI.IMyInventory,System.Boolean)">PullItem(MyDefinitionId, Nullable&lt;MyFixedPoint&gt;, IMyEntity, IMyInventory, Boolean)</h4>
  <div class="markdown level1 summary"><p>Implements pull item with possible optional remove. Computation part of this method is done in parallel, so if you call it on new conveyor network, it will not pull anything for the first time.
So the best approach is to call it in some steps, so it does not matter that you don't get result instantly. Be careful not to call it every frame as it can degrade performance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">MyFixedPoint PullItem(MyDefinitionId itemDefinitionId, Nullable&lt;MyFixedPoint&gt; amount, IMyEntity startingBlock, IMyInventory destinationInventory, bool remove)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyDefinitionId.html">MyDefinitionId</a></td>
        <td><span class="parametername">itemDefinitionId</span></td>
        <td><p>Item id</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">VRage.MyFixedPoint</span>&gt;</td>
        <td><span class="parametername">amount</span></td>
        <td><p>Amount to transfer</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">startingBlock</span></td>
        <td><p>starting block</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Game.ModAPI.IMyInventory.html">IMyInventory</a></td>
        <td><span class="parametername">destinationInventory</span></td>
        <td><p>destination inventory</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">remove</span></td>
        <td><p>if true item is removed from inventories instead of transfer</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.MyFixedPoint</span></td>
        <td><p>amount of item pulled</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyGridConveyorSystem_PushGenerateItem_" data-uid="VRage.ModAPI.IMyGridConveyorSystem.PushGenerateItem*"></a>
  <h4 id="VRage_ModAPI_IMyGridConveyorSystem_PushGenerateItem_VRage_Game_MyDefinitionId_System_Nullable_VRage_MyFixedPoint__VRage_MyFixedPoint__VRage_ModAPI_IMyEntity_System_Boolean_" data-uid="VRage.ModAPI.IMyGridConveyorSystem.PushGenerateItem(VRage.Game.MyDefinitionId,System.Nullable{VRage.MyFixedPoint},VRage.MyFixedPoint@,VRage.ModAPI.IMyEntity,System.Boolean)">PushGenerateItem(MyDefinitionId, Nullable&lt;MyFixedPoint&gt;, out MyFixedPoint, IMyEntity, Boolean)</h4>
  <div class="markdown level1 summary"><p>Implements push item from one source block. Item will be generated from source.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool PushGenerateItem(MyDefinitionId itemDefinitionId, Nullable&lt;MyFixedPoint&gt; amount, out MyFixedPoint transferredAmount, IMyEntity sourceBlock, bool partialPush)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyDefinitionId.html">MyDefinitionId</a></td>
        <td><span class="parametername">itemDefinitionId</span></td>
        <td><p>Item type to be transferred</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">VRage.MyFixedPoint</span>&gt;</td>
        <td><span class="parametername">amount</span></td>
        <td><p>Amount of items to transfer</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">VRage.MyFixedPoint</span></td>
        <td><span class="parametername">transferredAmount</span></td>
        <td><p>Amount of items that was transferred</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">sourceBlock</span></td>
        <td><p>Source block</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">partialPush</span></td>
        <td><p>If true, items fill be pushed even though not all can fit the conveyor system. Items that can't fit will be thrown away. If false, items will be pushed into system only when all of them fits.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>Returns info whether all items could fit in target network or not.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
