﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_Base
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_Base
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base">
  
  
  <h1 id="VRage_ObjectBuilders_MyObjectBuilder_Base" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base" class="text-break">Class MyObjectBuilder_Base
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyObjectBuilder_Base</span></div>
      <div class="level2"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AutopilotBase.html">MyObjectBuilder_AutopilotBase</a></div>
      <div class="level2"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AutopilotClipboard.html">MyObjectBuilder_AutopilotClipboard</a></div>
      <div class="level2"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AutopilotWaypoint.html">MyObjectBuilder_AutopilotWaypoint</a></div>
      <div class="level2"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_TargetingFlags.html">MyObjectBuilder_TargetingFlags</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_AiTarget.html">MyObjectBuilder_AiTarget</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_AnimationControllerComponent.html">MyObjectBuilder_AnimationControllerComponent</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Battery.html">MyObjectBuilder_Battery</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_BehaviorTreeNode.html">MyObjectBuilder_BehaviorTreeNode</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_BehaviorTreeNodeMemory.html">MyObjectBuilder_BehaviorTreeNodeMemory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_BlockGroup.html">MyObjectBuilder_BlockGroup</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Bot.html">MyObjectBuilder_Bot</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_BotMemory.html">MyObjectBuilder_BotMemory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ChatHistory.html">MyObjectBuilder_ChatHistory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Checkpoint.html">MyObjectBuilder_Checkpoint</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Client.html">MyObjectBuilder_Client</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Configuration.html">MyObjectBuilder_Configuration</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ConstructionStockpile.html">MyObjectBuilder_ConstructionStockpile</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ConveyorLine.html">MyObjectBuilder_ConveyorLine</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ConveyorPacket.html">MyObjectBuilder_ConveyorPacket</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html">MyObjectBuilder_CubeBlock</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Definitions.html">MyObjectBuilder_Definitions</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DeviceBase.html">MyObjectBuilder_DeviceBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_EnvironmentSettings.html">MyObjectBuilder_EnvironmentSettings</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_FactionChatHistory.html">MyObjectBuilder_FactionChatHistory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_FactionChatItem.html">MyObjectBuilder_FactionChatItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_FactionCollection.html">MyObjectBuilder_FactionCollection</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_FontData.html">MyObjectBuilder_FontData</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GlobalChatHistory.html">MyObjectBuilder_GlobalChatHistory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GlobalChatItem.html">MyObjectBuilder_GlobalChatItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GlobalEventBase.html">MyObjectBuilder_GlobalEventBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GlobalEvents.html">MyObjectBuilder_GlobalEvents</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Gps.html">MyObjectBuilder_Gps</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiControlBase.html">MyObjectBuilder_GuiControlBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiControls.html">MyObjectBuilder_GuiControls</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_GuiScreen.html">MyObjectBuilder_GuiScreen</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_HudEntityParams.html">MyObjectBuilder_HudEntityParams</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Identity.html">MyObjectBuilder_Identity</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_InventoryItem.html">MyObjectBuilder_InventoryItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_LastSession.html">MyObjectBuilder_LastSession</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_MissionTriggers.html">MyObjectBuilder_MissionTriggers</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ModInfo.html">MyObjectBuilder_ModInfo</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_PhysicalObject.html">MyObjectBuilder_PhysicalObject</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Player.html">MyObjectBuilder_Player</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_PlayerChatHistory.html">MyObjectBuilder_PlayerChatHistory</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_PlayerChatItem.html">MyObjectBuilder_PlayerChatItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_PreloadFileInfo.html">MyObjectBuilder_PreloadFileInfo</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Profiler.html">MyObjectBuilder_Profiler</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ProfilerSnapshot.html">MyObjectBuilder_ProfilerSnapshot</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_RadialMenuItem.html">MyObjectBuilder_RadialMenuItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_SavedGridDetails.html">MyObjectBuilder_SavedGridDetails</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ScenarioDefinitions.html">MyObjectBuilder_ScenarioDefinitions</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ScriptNode.html">MyObjectBuilder_ScriptNode</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Sector.html">MyObjectBuilder_Sector</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_SessionComponent.html">MyObjectBuilder_SessionComponent</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_SessionSettings.html">MyObjectBuilder_SessionSettings</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_StockpileItem.html">MyObjectBuilder_StockpileItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Toolbar.html">MyObjectBuilder_Toolbar</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ToolbarItem.html">MyObjectBuilder_ToolbarItem</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ToolbarItemActionParameter.html">MyObjectBuilder_ToolbarItemActionParameter</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_TransparentMaterial.html">MyObjectBuilder_TransparentMaterial</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_TransparentMaterials.html">MyObjectBuilder_TransparentMaterials</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_Trigger.html">MyObjectBuilder_Trigger</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_TutorialsHelper.html">MyObjectBuilder_TutorialsHelper</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_ViewedContent.html">MyObjectBuilder_ViewedContent</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_VisualScript.html">MyObjectBuilder_VisualScript</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_WeatherLightning.html">MyObjectBuilder_WeatherLightning</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_World.html">MyObjectBuilder_World</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_WorldConfiguration.html">MyObjectBuilder_WorldConfiguration</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_WorldGeneratorOperation.html">MyObjectBuilder_WorldGeneratorOperation</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_WorldGeneratorPlayerStartingState.html">MyObjectBuilder_WorldGeneratorPlayerStartingState</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentBase.html">MyObjectBuilder_ComponentBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentContainer.html">MyObjectBuilder_ComponentContainer</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ConditionBase.html">ConditionBase</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_CompositeTexture.html">MyObjectBuilder_CompositeTexture</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControlBinding.html">MyObjectBuilder_ControlBinding</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ControlBindingContext.html">MyObjectBuilder_ControlBindingContext</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_DPadControlVisualStyle.html">MyObjectBuilder_DPadControlVisualStyle</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GravityIndicatorVisualStyle.html">MyObjectBuilder_GravityIndicatorVisualStyle</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_GuiTexture.html">MyObjectBuilder_GuiTexture</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_HudIcon.html">MyObjectBuilder_HudIcon</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StatControls.html">MyObjectBuilder_StatControls</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_StatVisualStyle.html">MyObjectBuilder_StatVisualStyle</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ToolbarControlVisualStyle.html">MyObjectBuilder_ToolbarControlVisualStyle</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationFootIkChain.html">MyObjectBuilder_AnimationFootIkChain</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationLayer.html">MyObjectBuilder_AnimationLayer</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSM.html">MyObjectBuilder_AnimationSM</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMCondition.html">MyObjectBuilder_AnimationSMCondition</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMConditionsConjunction.html">MyObjectBuilder_AnimationSMConditionsConjunction</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMNode.html">MyObjectBuilder_AnimationSMNode</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationSMTransition.html">MyObjectBuilder_AnimationSMTransition</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_AnimationTreeNode.html">MyObjectBuilder_AnimationTreeNode</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EntityStat.html">MyObjectBuilder_EntityStat</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EntityStatRegenEffect.html">MyObjectBuilder_EntityStatRegenEffect</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_EnvironmentalParticleLogic.html">MyObjectBuilder_EnvironmentalParticleLogic</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_Localization.html">MyObjectBuilder_Localization</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_PlanetMapProvider.html">MyObjectBuilder_PlanetMapProvider</a></div>
      <div class="level2"><a class="xref" href="VRage.Game.ObjectBuilders.MyObjectBuilder_SkinInventory.html">MyObjectBuilder_SkinInventory</a></div>
      <div class="level2"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ObjectBuilders.html">VRage.ObjectBuilders</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ObjectBuilders_MyObjectBuilder_Base_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract class MyObjectBuilder_Base : Object</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base__ctor_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.#ctor*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base__ctor" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.#ctor">MyObjectBuilder_Base()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected MyObjectBuilder_Base()</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.SubtypeId*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.SubtypeId">SubtypeId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyStringHash SubtypeId { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Utils.MyStringHash.html">MyStringHash</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.SubtypeName*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.SubtypeName">SubtypeName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[NoSerialize]
public string SubtypeName { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.TypeId*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.TypeId">TypeId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilderType TypeId { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_Clone_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.Clone*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_Clone" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.Clone">Clone()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual MyObjectBuilder_Base Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.Equals*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.Equals(VRage.ObjectBuilders.MyObjectBuilder_Base)">Equals(MyObjectBuilder_Base)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual bool Equals(MyObjectBuilder_Base obj2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></td>
        <td><span class="parametername">obj2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId_" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.ShouldSerializeSubtypeId*"></a>
  <h4 id="VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId" data-uid="VRage.ObjectBuilders.MyObjectBuilder_Base.ShouldSerializeSubtypeId">ShouldSerializeSubtypeId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeSubtypeId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
