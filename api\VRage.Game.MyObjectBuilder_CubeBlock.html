﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_CubeBlock
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_CubeBlock
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyObjectBuilder_CubeBlock">
  
  
  <h1 id="VRage_Game_MyObjectBuilder_CubeBlock" data-uid="VRage.Game.MyObjectBuilder_CubeBlock" class="text-break">Class MyObjectBuilder_CubeBlock
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><span class="xref">MyObjectBuilder_CubeBlock</span></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_AttachableTopBlockBase.html">MyObjectBuilder_AttachableTopBlockBase</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Conveyor.html">MyObjectBuilder_Conveyor</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_ConveyorConnector.html">MyObjectBuilder_ConveyorConnector</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_EmissiveBlock.html">MyObjectBuilder_EmissiveBlock</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Kitchen.html">MyObjectBuilder_Kitchen</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Ladder.html">MyObjectBuilder_Ladder</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Ladder2.html">MyObjectBuilder_Ladder2</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Passage.html">MyObjectBuilder_Passage</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Planter.html">MyObjectBuilder_Planter</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_SignalLight.html">MyObjectBuilder_SignalLight</a></div>
      <div class="level3"><a class="xref" href="Sandbox.Common.ObjectBuilders.MyObjectBuilder_Wheel.html">MyObjectBuilder_Wheel</a></div>
      <div class="level3"><a class="xref" href="VRage.Game.MyObjectBuilder_TerminalBlock.html">MyObjectBuilder_TerminalBlock</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyObjectBuilder_CubeBlock_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_CubeBlock : MyObjectBuilder_Base</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock__ctor_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.#ctor*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock__ctor" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.#ctor">MyObjectBuilder_CubeBlock()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_CubeBlock()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_BlockGeneralDamageModifier" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.BlockGeneralDamageModifier">BlockGeneralDamageModifier</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public float BlockGeneralDamageModifier</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_BlockOrientation" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.BlockOrientation">BlockOrientation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableBlockOrientation BlockOrientation</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.SerializableBlockOrientation.html">SerializableBlockOrientation</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_BuildPercent" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.BuildPercent">BuildPercent</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public float BuildPercent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_BuiltBy" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.BuiltBy">BuiltBy</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public long BuiltBy</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ColorMaskHSV" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ColorMaskHSV">ColorMaskHSV</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SerializableVector3 ColorMaskHSV</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.SerializableVector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ComponentContainer" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ComponentContainer">ComponentContainer</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public MyObjectBuilder_ComponentContainer ComponentContainer</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.ObjectBuilders.ComponentSystem.MyObjectBuilder_ComponentContainer.html">MyObjectBuilder_ComponentContainer</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ConstructionInventory" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ConstructionInventory">ConstructionInventory</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[NoSerialize]
public MyObjectBuilder_Inventory ConstructionInventory</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_Inventory.html">MyObjectBuilder_Inventory</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ConstructionStockpile" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ConstructionStockpile">ConstructionStockpile</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public MyObjectBuilder_ConstructionStockpile ConstructionStockpile</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_ConstructionStockpile.html">MyObjectBuilder_ConstructionStockpile</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_DeformationRatio" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.DeformationRatio">DeformationRatio</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[NoSerialize]
public float DeformationRatio</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_EntityId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.EntityId">EntityId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public long EntityId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_IntegrityPercent" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.IntegrityPercent">IntegrityPercent</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public float IntegrityPercent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Min" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Min">Min</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public SerializableVector3I Min</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.SerializableVector3I</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockDefinition" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.MultiBlockDefinition">MultiBlockDefinition</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public Nullable&lt;SerializableDefinitionId&gt; MultiBlockDefinition</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRage.ObjectBuilders.SerializableDefinitionId.html">SerializableDefinitionId</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.MultiBlockId">MultiBlockId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public int MultiBlockId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_MultiBlockIndex" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.MultiBlockIndex">MultiBlockIndex</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public int MultiBlockIndex</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Name" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Name">Name</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public string Name</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Owner" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Owner">Owner</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public long Owner</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShareMode" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShareMode">ShareMode</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyOwnershipShareModeEnum ShareMode</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyOwnershipShareModeEnum.html">MyOwnershipShareModeEnum</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_SkinSubtypeId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SkinSubtypeId">SkinSubtypeId</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public string SkinSubtypeId</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_SubBlocks" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SubBlocks">SubBlocks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serialize]
public MyObjectBuilder_CubeBlock.MySubBlockId[] SubBlocks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.MySubBlockId.html">MyObjectBuilder_CubeBlock.MySubBlockId</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_Orientation_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Orientation*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Orientation" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Orientation">Orientation</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[NoSerialize]
public SerializableQuaternion Orientation { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRage.SerializableQuaternion</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_Remap_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Remap*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Remap_VRage_ModAPI_IMyRemapHelper_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Remap(VRage.ModAPI.IMyRemapHelper)">Remap(IMyRemapHelper)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Remap(IMyRemapHelper remapHelper)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyRemapHelper.html">IMyRemapHelper</a></td>
        <td><span class="parametername">remapHelper</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_SetupForGridPaste_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SetupForGridPaste*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_SetupForGridPaste" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SetupForGridPaste">SetupForGridPaste()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void SetupForGridPaste()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_SetupForProjector_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SetupForProjector*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_SetupForProjector" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.SetupForProjector">SetupForProjector()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void SetupForProjector()</code></pre>
  </div>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeBlockOrientation_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeBlockOrientation*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeBlockOrientation" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeBlockOrientation">ShouldSerializeBlockOrientation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeBlockOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeColorMaskHSV_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeColorMaskHSV*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeColorMaskHSV" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeColorMaskHSV">ShouldSerializeColorMaskHSV()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeColorMaskHSV()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeComponentContainer_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeComponentContainer*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeComponentContainer" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeComponentContainer">ShouldSerializeComponentContainer()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeComponentContainer()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionInventory_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeConstructionInventory*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionInventory" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeConstructionInventory">ShouldSerializeConstructionInventory()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeConstructionInventory()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionStockpile_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeConstructionStockpile*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeConstructionStockpile" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeConstructionStockpile">ShouldSerializeConstructionStockpile()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeConstructionStockpile()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeEntityId_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeEntityId*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeEntityId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeEntityId">ShouldSerializeEntityId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeEntityId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMin_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMin*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMin" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMin">ShouldSerializeMin()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeMin()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockDefinition_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockDefinition*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockDefinition" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockDefinition">ShouldSerializeMultiBlockDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeMultiBlockDefinition()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockId_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockId*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeMultiBlockId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeMultiBlockId">ShouldSerializeMultiBlockId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeMultiBlockId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeOrientation_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeOrientation*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeOrientation" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeOrientation">ShouldSerializeOrientation()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeOrientation()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeSkinSubtypeId_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeSkinSubtypeId*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_ShouldSerializeSkinSubtypeId" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.ShouldSerializeSkinSubtypeId">ShouldSerializeSkinSubtypeId()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool ShouldSerializeSkinSubtypeId()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Game_MyObjectBuilder_CubeBlock_Upgrade_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Upgrade*"></a>
  <h4 id="VRage_Game_MyObjectBuilder_CubeBlock_Upgrade_VRage_Game_MyObjectBuilder_CubeBlock_VRage_ObjectBuilders_MyObjectBuilderType_System_String_" data-uid="VRage.Game.MyObjectBuilder_CubeBlock.Upgrade(VRage.Game.MyObjectBuilder_CubeBlock,VRage.ObjectBuilders.MyObjectBuilderType,System.String)">Upgrade(MyObjectBuilder_CubeBlock, MyObjectBuilderType, String)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyObjectBuilder_CubeBlock Upgrade(MyObjectBuilder_CubeBlock cubeBlock, MyObjectBuilderType newType, string newSubType)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html">MyObjectBuilder_CubeBlock</a></td>
        <td><span class="parametername">cubeBlock</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilderType.html">MyObjectBuilderType</a></td>
        <td><span class="parametername">newType</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">newSubType</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_CubeBlock.html">MyObjectBuilder_CubeBlock</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
