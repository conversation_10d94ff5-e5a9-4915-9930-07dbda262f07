﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class BoundingBox2I
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class BoundingBox2I
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.BoundingBox2I">
  
  
  <h1 id="VRageMath_BoundingBox2I" data-uid="VRageMath.BoundingBox2I" class="text-break">Class BoundingBox2I
  </h1>
  <div class="markdown level0 summary"><p>Defines an axis-aligned box-shaped 3D volume.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">BoundingBox2I</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_BoundingBox2I_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class BoundingBox2I : ValueType, IEquatable&lt;BoundingBox2I&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_BoundingBox2I__ctor_" data-uid="VRageMath.BoundingBox2I.#ctor*"></a>
  <h4 id="VRageMath_BoundingBox2I__ctor_VRageMath_Vector2I_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.#ctor(VRageMath.Vector2I,VRageMath.Vector2I)">BoundingBox2I(Vector2I, Vector2I)</h4>
  <div class="markdown level1 summary"><p>Creates an instance of BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I(Vector2I min, Vector2I max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum point the BoundingBox2I includes.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum point the BoundingBox2I includes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_BoundingBox2I_Max" data-uid="VRageMath.BoundingBox2I.Max">Max</h4>
  <div class="markdown level1 summary"><p>The maximum point the BoundingBox2I contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I Max</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_BoundingBox2I_Min" data-uid="VRageMath.BoundingBox2I.Min">Min</h4>
  <div class="markdown level1 summary"><p>The minimum point the BoundingBox2I contains.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I Min</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_BoundingBox2I_Center_" data-uid="VRageMath.BoundingBox2I.Center*"></a>
  <h4 id="VRageMath_BoundingBox2I_Center" data-uid="VRageMath.BoundingBox2I.Center">Center</h4>
  <div class="markdown level1 summary"><p>Calculates center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I Center { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Extents_" data-uid="VRageMath.BoundingBox2I.Extents*"></a>
  <h4 id="VRageMath_BoundingBox2I_Extents" data-uid="VRageMath.BoundingBox2I.Extents">Extents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I Extents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_HalfExtents_" data-uid="VRageMath.BoundingBox2I.HalfExtents*"></a>
  <h4 id="VRageMath_BoundingBox2I_HalfExtents" data-uid="VRageMath.BoundingBox2I.HalfExtents">HalfExtents</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I HalfExtents { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Height_" data-uid="VRageMath.BoundingBox2I.Height*"></a>
  <h4 id="VRageMath_BoundingBox2I_Height" data-uid="VRageMath.BoundingBox2I.Height">Height</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Height { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Size_" data-uid="VRageMath.BoundingBox2I.Size*"></a>
  <h4 id="VRageMath_BoundingBox2I_Size" data-uid="VRageMath.BoundingBox2I.Size">Size</h4>
  <div class="markdown level1 summary"><p>Size</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I Size { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Width_" data-uid="VRageMath.BoundingBox2I.Width*"></a>
  <h4 id="VRageMath_BoundingBox2I_Width" data-uid="VRageMath.BoundingBox2I.Width">Width</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Width { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_BoundingBox2I_Area_" data-uid="VRageMath.BoundingBox2I.Area*"></a>
  <h4 id="VRageMath_BoundingBox2I_Area" data-uid="VRageMath.BoundingBox2I.Area">Area()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Area()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Contains_" data-uid="VRageMath.BoundingBox2I.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2I_Contains_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.Contains(VRageMath.BoundingBox2I)">Contains(BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2I contains another BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2I to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Contains_" data-uid="VRageMath.BoundingBox2I.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2I_Contains_VRageMath_BoundingBox2I__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBox2I.Contains(VRageMath.BoundingBox2I@,VRageMath.ContainmentType@)">Contains(ref BoundingBox2I, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2I contains a BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref BoundingBox2I box, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2I to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Contains_" data-uid="VRageMath.BoundingBox2I.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2I_Contains_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.Contains(VRageMath.Vector2I)">Contains(Vector2I)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2I contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ContainmentType Contains(Vector2I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Contains_" data-uid="VRageMath.BoundingBox2I.Contains*"></a>
  <h4 id="VRageMath_BoundingBox2I_Contains_VRageMath_Vector2I__VRageMath_ContainmentType__" data-uid="VRageMath.BoundingBox2I.Contains(VRageMath.Vector2I@,VRageMath.ContainmentType@)">Contains(ref Vector2I, out ContainmentType)</h4>
  <div class="markdown level1 summary"><p>Tests whether the BoundingBox2I contains a point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Vector2I point, out ContainmentType result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">point</span></td>
        <td><p>The point to test for overlap.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.ContainmentType.html">ContainmentType</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Enumeration indicating the extent of overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateFromHalfExtent_" data-uid="VRageMath.BoundingBox2I.CreateFromHalfExtent*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateFromHalfExtent_VRageMath_Vector2I_System_Int32_" data-uid="VRageMath.BoundingBox2I.CreateFromHalfExtent(VRageMath.Vector2I,System.Int32)">CreateFromHalfExtent(Vector2I, Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2I CreateFromHalfExtent(Vector2I center, int halfExtent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">halfExtent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateFromHalfExtent_" data-uid="VRageMath.BoundingBox2I.CreateFromHalfExtent*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateFromHalfExtent_VRageMath_Vector2I_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.CreateFromHalfExtent(VRageMath.Vector2I,VRageMath.Vector2I)">CreateFromHalfExtent(Vector2I, Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2I CreateFromHalfExtent(Vector2I center, Vector2I halfExtent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">center</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">halfExtent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateFromPoints_" data-uid="VRageMath.BoundingBox2I.CreateFromPoints*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateFromPoints_System_Collections_Generic_IEnumerable_VRageMath_Vector2I__" data-uid="VRageMath.BoundingBox2I.CreateFromPoints(System.Collections.Generic.IEnumerable{VRageMath.Vector2I})">CreateFromPoints(IEnumerable&lt;Vector2I&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2I that will contain a group of points.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2I CreateFromPoints(IEnumerable&lt;Vector2I&gt; points)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRageMath.Vector2I.html">Vector2I</a>&gt;</td>
        <td><span class="parametername">points</span></td>
        <td><p>A list of points the BoundingBox2I should contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateInvalid_" data-uid="VRageMath.BoundingBox2I.CreateInvalid*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateInvalid" data-uid="VRageMath.BoundingBox2I.CreateInvalid">CreateInvalid()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2I CreateInvalid()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateMerged_" data-uid="VRageMath.BoundingBox2I.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateMerged_VRageMath_BoundingBox2I_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.CreateMerged(VRageMath.BoundingBox2I,VRageMath.BoundingBox2I)">CreateMerged(BoundingBox2I, BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2I that contains the two specified BoundingBox2I instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static BoundingBox2I CreateMerged(BoundingBox2I original, BoundingBox2I additional)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBox2Is to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBox2Is to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_CreateMerged_" data-uid="VRageMath.BoundingBox2I.CreateMerged*"></a>
  <h4 id="VRageMath_BoundingBox2I_CreateMerged_VRageMath_BoundingBox2I__VRageMath_BoundingBox2I__VRageMath_BoundingBox2I__" data-uid="VRageMath.BoundingBox2I.CreateMerged(VRageMath.BoundingBox2I@,VRageMath.BoundingBox2I@,VRageMath.BoundingBox2I@)">CreateMerged(ref BoundingBox2I, ref BoundingBox2I, out BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Creates the smallest BoundingBox2I that contains the two specified BoundingBox2I instances.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateMerged(ref BoundingBox2I original, ref BoundingBox2I additional, out BoundingBox2I result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">original</span></td>
        <td><p>One of the BoundingBox2I instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">additional</span></td>
        <td><p>One of the BoundingBox2I instances to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created BoundingBox2I.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Equals_" data-uid="VRageMath.BoundingBox2I.Equals*"></a>
  <h4 id="VRageMath_BoundingBox2I_Equals_System_Object_" data-uid="VRageMath.BoundingBox2I.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2I are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>The Object to compare with the current BoundingBox2I.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Equals_" data-uid="VRageMath.BoundingBox2I.Equals*"></a>
  <h4 id="VRageMath_BoundingBox2I_Equals_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.Equals(VRageMath.BoundingBox2I)">Equals(BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2I are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(BoundingBox2I other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The BoundingBox2I to compare with the current BoundingBox2I.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_GetCorners_" data-uid="VRageMath.BoundingBox2I.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBox2I_GetCorners" data-uid="VRageMath.BoundingBox2I.GetCorners">GetCorners()</h4>
  <div class="markdown level1 summary"><p>Gets an array of points that make up the corners of the BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector2I[] GetCorners()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a>[]</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_GetCorners_" data-uid="VRageMath.BoundingBox2I.GetCorners*"></a>
  <h4 id="VRageMath_BoundingBox2I_GetCorners_VRageMath_Vector2I___" data-uid="VRageMath.BoundingBox2I.GetCorners(VRageMath.Vector2I[])">GetCorners(Vector2I[])</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCorners(Vector2I[] corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a>[]</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector2I points where the corners of the BoundingBox2I are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_GetCornersUnsafe_" data-uid="VRageMath.BoundingBox2I.GetCornersUnsafe*"></a>
  <h4 id="VRageMath_BoundingBox2I_GetCornersUnsafe_VRageMath_Vector2I__" data-uid="VRageMath.BoundingBox2I.GetCornersUnsafe(VRageMath.Vector2I*)">GetCornersUnsafe(Vector2I*)</h4>
  <div class="markdown level1 summary"><p>Gets the array of points that make up the corners of the BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetCornersUnsafe(Vector2I*corners)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a>*</td>
        <td><span class="parametername">corners</span></td>
        <td><p>An existing array of at least 8 Vector2I points where the corners of the BoundingBox2I are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_GetHashCode_" data-uid="VRageMath.BoundingBox2I.GetHashCode*"></a>
  <h4 id="VRageMath_BoundingBox2I_GetHashCode" data-uid="VRageMath.BoundingBox2I.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_GetIncluded_" data-uid="VRageMath.BoundingBox2I.GetIncluded*"></a>
  <h4 id="VRageMath_BoundingBox2I_GetIncluded_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.GetIncluded(VRageMath.Vector2I)">GetIncluded(Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I GetIncluded(Vector2I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.BoundingBox2I)">Include(BoundingBox2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_BoundingBox2I__" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.BoundingBox2I@)">Include(ref BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(ref BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.Vector2I)">Include(Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(Vector2I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_Vector2I_VRageMath_Vector2I_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.Vector2I,VRageMath.Vector2I,VRageMath.Vector2I)">Include(Vector2I, Vector2I, Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(Vector2I p0, Vector2I p1, Vector2I p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_Vector2I__" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.Vector2I@)">Include(ref Vector2I)</h4>
  <div class="markdown level1 summary"><p>return expanded aabb (abb include point)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(ref Vector2I point)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">point</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Include_" data-uid="VRageMath.BoundingBox2I.Include*"></a>
  <h4 id="VRageMath_BoundingBox2I_Include_VRageMath_Vector2I__VRageMath_Vector2I__VRageMath_Vector2I__" data-uid="VRageMath.BoundingBox2I.Include(VRageMath.Vector2I@,VRageMath.Vector2I@,VRageMath.Vector2I@)">Include(ref Vector2I, ref Vector2I, ref Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Include(ref Vector2I p0, ref Vector2I p1, ref Vector2I p2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p0</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">p2</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Inflate_" data-uid="VRageMath.BoundingBox2I.Inflate*"></a>
  <h4 id="VRageMath_BoundingBox2I_Inflate_System_Int32_" data-uid="VRageMath.BoundingBox2I.Inflate(System.Int32)">Inflate(Int32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Inflate(int size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_InflateToMinimum_" data-uid="VRageMath.BoundingBox2I.InflateToMinimum*"></a>
  <h4 id="VRageMath_BoundingBox2I_InflateToMinimum_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.InflateToMinimum(VRageMath.Vector2I)">InflateToMinimum(Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void InflateToMinimum(Vector2I minimumSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">minimumSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Intersect_" data-uid="VRageMath.BoundingBox2I.Intersect*"></a>
  <h4 id="VRageMath_BoundingBox2I_Intersect_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.Intersect(VRageMath.BoundingBox2I)">Intersect(BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Returns bounding box which is intersection of this and box
It's called 'Prunik'
Result is invalid box when there's no intersection (Min &gt; Max)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Intersect(BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Intersects_" data-uid="VRageMath.BoundingBox2I.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2I_Intersects_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.Intersects(VRageMath.BoundingBox2I)">Intersects(BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox2I intersects another BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2I to check for intersection with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Intersects_" data-uid="VRageMath.BoundingBox2I.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2I_Intersects_VRageMath_BoundingBox2I__" data-uid="VRageMath.BoundingBox2I.Intersects(VRageMath.BoundingBox2I@)">Intersects(ref BoundingBox2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(ref BoundingBox2I box)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Intersects_" data-uid="VRageMath.BoundingBox2I.Intersects*"></a>
  <h4 id="VRageMath_BoundingBox2I_Intersects_VRageMath_BoundingBox2I__System_Boolean__" data-uid="VRageMath.BoundingBox2I.Intersects(VRageMath.BoundingBox2I@,System.Boolean@)">Intersects(ref BoundingBox2I, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Checks whether the current BoundingBox2I intersects another BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref BoundingBox2I box, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">box</span></td>
        <td><p>The BoundingBox2I to check for intersection with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the BoundingBox2I instances intersect; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Perimeter_" data-uid="VRageMath.BoundingBox2I.Perimeter*"></a>
  <h4 id="VRageMath_BoundingBox2I_Perimeter" data-uid="VRageMath.BoundingBox2I.Perimeter">Perimeter()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Perimeter()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Scale_" data-uid="VRageMath.BoundingBox2I.Scale*"></a>
  <h4 id="VRageMath_BoundingBox2I_Scale_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.Scale(VRageMath.Vector2I)">Scale(Vector2I)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Scale(Vector2I scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_ToString_" data-uid="VRageMath.BoundingBox2I.ToString*"></a>
  <h4 id="VRageMath_BoundingBox2I_ToString" data-uid="VRageMath.BoundingBox2I.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Returns a String that represents the current BoundingBox2I.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_Translate_" data-uid="VRageMath.BoundingBox2I.Translate*"></a>
  <h4 id="VRageMath_BoundingBox2I_Translate_VRageMath_Vector2I_" data-uid="VRageMath.BoundingBox2I.Translate(VRageMath.Vector2I)">Translate(Vector2I)</h4>
  <div class="markdown level1 summary"><p>Translate</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public BoundingBox2I Translate(Vector2I vctTranlsation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2I.html">Vector2I</a></td>
        <td><span class="parametername">vctTranlsation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_BoundingBox2I_op_Equality_" data-uid="VRageMath.BoundingBox2I.op_Equality*"></a>
  <h4 id="VRageMath_BoundingBox2I_op_Equality_VRageMath_BoundingBox2I_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.op_Equality(VRageMath.BoundingBox2I,VRageMath.BoundingBox2I)">Equality(BoundingBox2I, BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2I are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(BoundingBox2I a, BoundingBox2I b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>BoundingBox2I to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>BoundingBox2I to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_BoundingBox2I_op_Inequality_" data-uid="VRageMath.BoundingBox2I.op_Inequality*"></a>
  <h4 id="VRageMath_BoundingBox2I_op_Inequality_VRageMath_BoundingBox2I_VRageMath_BoundingBox2I_" data-uid="VRageMath.BoundingBox2I.op_Inequality(VRageMath.BoundingBox2I,VRageMath.BoundingBox2I)">Inequality(BoundingBox2I, BoundingBox2I)</h4>
  <div class="markdown level1 summary"><p>Determines whether two instances of BoundingBox2I are not equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(BoundingBox2I a, BoundingBox2I b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>The object to the left of the inequality operator.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox2I.html">BoundingBox2I</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>The object to the right of the inequality operator.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
