﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Color
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Color
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Color">
  
  
  <h1 id="VRageMath_Color" data-uid="VRageMath.Color" class="text-break">Class Color
  </h1>
  <div class="markdown level0 summary"><p>Represents a four-component color using red, green, blue, and alpha data.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Color</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Color_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Color : ValueType, IPackedVector&lt;uint&gt;, IPackedVector, IEquatable&lt;Color&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_Int32_System_Int32_System_Int32_" data-uid="VRageMath.Color.#ctor(System.Int32,System.Int32,System.Int32)">Color(Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(int r, int g, int b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">r</span></td>
        <td><p>Red component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">g</span></td>
        <td><p>Green component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Blue component.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_Int32_System_Int32_System_Int32_System_Int32_" data-uid="VRageMath.Color.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">Color(Int32, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(int r, int g, int b, int a)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">r</span></td>
        <td><p>Red component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">g</span></td>
        <td><p>Green component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Blue component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>Alpha component.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_Single_" data-uid="VRageMath.Color.#ctor(System.Single)">Color(Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(float rgb)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">rgb</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_Single_System_Single_System_Single_" data-uid="VRageMath.Color.#ctor(System.Single,System.Single,System.Single)">Color(Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(float r, float g, float b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">r</span></td>
        <td><p>Red component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">g</span></td>
        <td><p>Green component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Blue component.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_Single_System_Single_System_Single_System_Single_" data-uid="VRageMath.Color.#ctor(System.Single,System.Single,System.Single,System.Single)">Color(Single, Single, Single, Single)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(float r, float g, float b, float a)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">r</span></td>
        <td><p>Red component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">g</span></td>
        <td><p>Green component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Blue component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>Alpha component.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_System_UInt32_" data-uid="VRageMath.Color.#ctor(System.UInt32)">Color(UInt32)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(uint packedValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td><span class="parametername">packedValue</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_VRageMath_Color_System_Single_" data-uid="VRageMath.Color.#ctor(VRageMath.Color,System.Single)">Color(Color, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(Color color, float a)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_VRageMath_Vector3_" data-uid="VRageMath.Color.#ctor(VRageMath.Vector3)">Color(Vector3)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(Vector3 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>A three-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color__ctor_" data-uid="VRageMath.Color.#ctor*"></a>
  <h4 id="VRageMath_Color__ctor_VRageMath_Vector4_" data-uid="VRageMath.Color.#ctor(VRageMath.Vector4)">Color(Vector4)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of the class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color(Vector4 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Color_PackedValue" data-uid="VRageMath.Color.PackedValue">PackedValue</h4>
  <div class="markdown level1 summary"><p>Gets the current color as a packed value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public uint PackedValue</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.UInt32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Color_A_" data-uid="VRageMath.Color.A*"></a>
  <h4 id="VRageMath_Color_A" data-uid="VRageMath.Color.A">A</h4>
  <div class="markdown level1 summary"><p>Gets or sets the alpha component value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte A { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_AliceBlue_" data-uid="VRageMath.Color.AliceBlue*"></a>
  <h4 id="VRageMath_Color_AliceBlue" data-uid="VRageMath.Color.AliceBlue">AliceBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:240 G:248 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color AliceBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_AntiqueWhite_" data-uid="VRageMath.Color.AntiqueWhite*"></a>
  <h4 id="VRageMath_Color_AntiqueWhite" data-uid="VRageMath.Color.AntiqueWhite">AntiqueWhite</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:250 G:235 B:215 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color AntiqueWhite { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Aqua_" data-uid="VRageMath.Color.Aqua*"></a>
  <h4 id="VRageMath_Color_Aqua" data-uid="VRageMath.Color.Aqua">Aqua</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:255 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Aqua { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Aquamarine_" data-uid="VRageMath.Color.Aquamarine*"></a>
  <h4 id="VRageMath_Color_Aquamarine" data-uid="VRageMath.Color.Aquamarine">Aquamarine</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:127 G:255 B:212 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Aquamarine { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Azure_" data-uid="VRageMath.Color.Azure*"></a>
  <h4 id="VRageMath_Color_Azure" data-uid="VRageMath.Color.Azure">Azure</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:240 G:255 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Azure { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_B_" data-uid="VRageMath.Color.B*"></a>
  <h4 id="VRageMath_Color_B" data-uid="VRageMath.Color.B">B</h4>
  <div class="markdown level1 summary"><p>Gets or sets the blue component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte B { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Beige_" data-uid="VRageMath.Color.Beige*"></a>
  <h4 id="VRageMath_Color_Beige" data-uid="VRageMath.Color.Beige">Beige</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:245 G:245 B:220 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Beige { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Bisque_" data-uid="VRageMath.Color.Bisque*"></a>
  <h4 id="VRageMath_Color_Bisque" data-uid="VRageMath.Color.Bisque">Bisque</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:228 B:196 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Bisque { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Black_" data-uid="VRageMath.Color.Black*"></a>
  <h4 id="VRageMath_Color_Black" data-uid="VRageMath.Color.Black">Black</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:0 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Black { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_BlanchedAlmond_" data-uid="VRageMath.Color.BlanchedAlmond*"></a>
  <h4 id="VRageMath_Color_BlanchedAlmond" data-uid="VRageMath.Color.BlanchedAlmond">BlanchedAlmond</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:235 B:205 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color BlanchedAlmond { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Blue_" data-uid="VRageMath.Color.Blue*"></a>
  <h4 id="VRageMath_Color_Blue" data-uid="VRageMath.Color.Blue">Blue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:0 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Blue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_BlueViolet_" data-uid="VRageMath.Color.BlueViolet*"></a>
  <h4 id="VRageMath_Color_BlueViolet" data-uid="VRageMath.Color.BlueViolet">BlueViolet</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:138 G:43 B:226 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color BlueViolet { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Brown_" data-uid="VRageMath.Color.Brown*"></a>
  <h4 id="VRageMath_Color_Brown" data-uid="VRageMath.Color.Brown">Brown</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:165 G:42 B:42 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Brown { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_BurlyWood_" data-uid="VRageMath.Color.BurlyWood*"></a>
  <h4 id="VRageMath_Color_BurlyWood" data-uid="VRageMath.Color.BurlyWood">BurlyWood</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:222 G:184 B:135 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color BurlyWood { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_CadetBlue_" data-uid="VRageMath.Color.CadetBlue*"></a>
  <h4 id="VRageMath_Color_CadetBlue" data-uid="VRageMath.Color.CadetBlue">CadetBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:95 G:158 B:160 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color CadetBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Chartreuse_" data-uid="VRageMath.Color.Chartreuse*"></a>
  <h4 id="VRageMath_Color_Chartreuse" data-uid="VRageMath.Color.Chartreuse">Chartreuse</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:127 G:255 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Chartreuse { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Chocolate_" data-uid="VRageMath.Color.Chocolate*"></a>
  <h4 id="VRageMath_Color_Chocolate" data-uid="VRageMath.Color.Chocolate">Chocolate</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:210 G:105 B:30 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Chocolate { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Coral_" data-uid="VRageMath.Color.Coral*"></a>
  <h4 id="VRageMath_Color_Coral" data-uid="VRageMath.Color.Coral">Coral</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:127 B:80 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Coral { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_CornflowerBlue_" data-uid="VRageMath.Color.CornflowerBlue*"></a>
  <h4 id="VRageMath_Color_CornflowerBlue" data-uid="VRageMath.Color.CornflowerBlue">CornflowerBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:100 G:149 B:237 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color CornflowerBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Cornsilk_" data-uid="VRageMath.Color.Cornsilk*"></a>
  <h4 id="VRageMath_Color_Cornsilk" data-uid="VRageMath.Color.Cornsilk">Cornsilk</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:248 B:220 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Cornsilk { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Crimson_" data-uid="VRageMath.Color.Crimson*"></a>
  <h4 id="VRageMath_Color_Crimson" data-uid="VRageMath.Color.Crimson">Crimson</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:220 G:20 B:60 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Crimson { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Cyan_" data-uid="VRageMath.Color.Cyan*"></a>
  <h4 id="VRageMath_Color_Cyan" data-uid="VRageMath.Color.Cyan">Cyan</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:255 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Cyan { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkBlue_" data-uid="VRageMath.Color.DarkBlue*"></a>
  <h4 id="VRageMath_Color_DarkBlue" data-uid="VRageMath.Color.DarkBlue">DarkBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:0 B:139 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkCyan_" data-uid="VRageMath.Color.DarkCyan*"></a>
  <h4 id="VRageMath_Color_DarkCyan" data-uid="VRageMath.Color.DarkCyan">DarkCyan</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:139 B:139 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkCyan { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkGoldenrod_" data-uid="VRageMath.Color.DarkGoldenrod*"></a>
  <h4 id="VRageMath_Color_DarkGoldenrod" data-uid="VRageMath.Color.DarkGoldenrod">DarkGoldenrod</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:184 G:134 B:11 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkGoldenrod { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkGray_" data-uid="VRageMath.Color.DarkGray*"></a>
  <h4 id="VRageMath_Color_DarkGray" data-uid="VRageMath.Color.DarkGray">DarkGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:169 G:169 B:169 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkGreen_" data-uid="VRageMath.Color.DarkGreen*"></a>
  <h4 id="VRageMath_Color_DarkGreen" data-uid="VRageMath.Color.DarkGreen">DarkGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:100 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkKhaki_" data-uid="VRageMath.Color.DarkKhaki*"></a>
  <h4 id="VRageMath_Color_DarkKhaki" data-uid="VRageMath.Color.DarkKhaki">DarkKhaki</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:189 G:183 B:107 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkKhaki { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkMagenta_" data-uid="VRageMath.Color.DarkMagenta*"></a>
  <h4 id="VRageMath_Color_DarkMagenta" data-uid="VRageMath.Color.DarkMagenta">DarkMagenta</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:139 G:0 B:139 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkMagenta { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkOliveGreen_" data-uid="VRageMath.Color.DarkOliveGreen*"></a>
  <h4 id="VRageMath_Color_DarkOliveGreen" data-uid="VRageMath.Color.DarkOliveGreen">DarkOliveGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:85 G:107 B:47 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkOliveGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkOrange_" data-uid="VRageMath.Color.DarkOrange*"></a>
  <h4 id="VRageMath_Color_DarkOrange" data-uid="VRageMath.Color.DarkOrange">DarkOrange</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:140 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkOrange { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkOrchid_" data-uid="VRageMath.Color.DarkOrchid*"></a>
  <h4 id="VRageMath_Color_DarkOrchid" data-uid="VRageMath.Color.DarkOrchid">DarkOrchid</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:153 G:50 B:204 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkOrchid { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkRed_" data-uid="VRageMath.Color.DarkRed*"></a>
  <h4 id="VRageMath_Color_DarkRed" data-uid="VRageMath.Color.DarkRed">DarkRed</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:139 G:0 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkRed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkSalmon_" data-uid="VRageMath.Color.DarkSalmon*"></a>
  <h4 id="VRageMath_Color_DarkSalmon" data-uid="VRageMath.Color.DarkSalmon">DarkSalmon</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:233 G:150 B:122 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkSalmon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkSeaGreen_" data-uid="VRageMath.Color.DarkSeaGreen*"></a>
  <h4 id="VRageMath_Color_DarkSeaGreen" data-uid="VRageMath.Color.DarkSeaGreen">DarkSeaGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:143 G:188 B:139 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkSeaGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkSlateBlue_" data-uid="VRageMath.Color.DarkSlateBlue*"></a>
  <h4 id="VRageMath_Color_DarkSlateBlue" data-uid="VRageMath.Color.DarkSlateBlue">DarkSlateBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:72 G:61 B:139 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkSlateBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkSlateGray_" data-uid="VRageMath.Color.DarkSlateGray*"></a>
  <h4 id="VRageMath_Color_DarkSlateGray" data-uid="VRageMath.Color.DarkSlateGray">DarkSlateGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:47 G:79 B:79 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkSlateGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkTurquoise_" data-uid="VRageMath.Color.DarkTurquoise*"></a>
  <h4 id="VRageMath_Color_DarkTurquoise" data-uid="VRageMath.Color.DarkTurquoise">DarkTurquoise</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:206 B:209 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkTurquoise { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DarkViolet_" data-uid="VRageMath.Color.DarkViolet*"></a>
  <h4 id="VRageMath_Color_DarkViolet" data-uid="VRageMath.Color.DarkViolet">DarkViolet</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:148 G:0 B:211 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DarkViolet { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DeepPink_" data-uid="VRageMath.Color.DeepPink*"></a>
  <h4 id="VRageMath_Color_DeepPink" data-uid="VRageMath.Color.DeepPink">DeepPink</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:20 B:147 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DeepPink { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DeepSkyBlue_" data-uid="VRageMath.Color.DeepSkyBlue*"></a>
  <h4 id="VRageMath_Color_DeepSkyBlue" data-uid="VRageMath.Color.DeepSkyBlue">DeepSkyBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:191 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DeepSkyBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DimGray_" data-uid="VRageMath.Color.DimGray*"></a>
  <h4 id="VRageMath_Color_DimGray" data-uid="VRageMath.Color.DimGray">DimGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:105 G:105 B:105 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DimGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_DodgerBlue_" data-uid="VRageMath.Color.DodgerBlue*"></a>
  <h4 id="VRageMath_Color_DodgerBlue" data-uid="VRageMath.Color.DodgerBlue">DodgerBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:30 G:144 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color DodgerBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Firebrick_" data-uid="VRageMath.Color.Firebrick*"></a>
  <h4 id="VRageMath_Color_Firebrick" data-uid="VRageMath.Color.Firebrick">Firebrick</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:178 G:34 B:34 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Firebrick { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_FloralWhite_" data-uid="VRageMath.Color.FloralWhite*"></a>
  <h4 id="VRageMath_Color_FloralWhite" data-uid="VRageMath.Color.FloralWhite">FloralWhite</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:250 B:240 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color FloralWhite { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_ForestGreen_" data-uid="VRageMath.Color.ForestGreen*"></a>
  <h4 id="VRageMath_Color_ForestGreen" data-uid="VRageMath.Color.ForestGreen">ForestGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:34 G:139 B:34 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color ForestGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Fuchsia_" data-uid="VRageMath.Color.Fuchsia*"></a>
  <h4 id="VRageMath_Color_Fuchsia" data-uid="VRageMath.Color.Fuchsia">Fuchsia</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:0 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Fuchsia { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_G_" data-uid="VRageMath.Color.G*"></a>
  <h4 id="VRageMath_Color_G" data-uid="VRageMath.Color.G">G</h4>
  <div class="markdown level1 summary"><p>Gets or sets the green component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte G { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Gainsboro_" data-uid="VRageMath.Color.Gainsboro*"></a>
  <h4 id="VRageMath_Color_Gainsboro" data-uid="VRageMath.Color.Gainsboro">Gainsboro</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:220 G:220 B:220 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Gainsboro { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_GhostWhite_" data-uid="VRageMath.Color.GhostWhite*"></a>
  <h4 id="VRageMath_Color_GhostWhite" data-uid="VRageMath.Color.GhostWhite">GhostWhite</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:248 G:248 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color GhostWhite { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Gold_" data-uid="VRageMath.Color.Gold*"></a>
  <h4 id="VRageMath_Color_Gold" data-uid="VRageMath.Color.Gold">Gold</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:215 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Gold { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Goldenrod_" data-uid="VRageMath.Color.Goldenrod*"></a>
  <h4 id="VRageMath_Color_Goldenrod" data-uid="VRageMath.Color.Goldenrod">Goldenrod</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:218 G:165 B:32 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Goldenrod { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Gray_" data-uid="VRageMath.Color.Gray*"></a>
  <h4 id="VRageMath_Color_Gray" data-uid="VRageMath.Color.Gray">Gray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:128 G:128 B:128 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Gray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Green_" data-uid="VRageMath.Color.Green*"></a>
  <h4 id="VRageMath_Color_Green" data-uid="VRageMath.Color.Green">Green</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:128 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Green { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_GreenYellow_" data-uid="VRageMath.Color.GreenYellow*"></a>
  <h4 id="VRageMath_Color_GreenYellow" data-uid="VRageMath.Color.GreenYellow">GreenYellow</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:173 G:255 B:47 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color GreenYellow { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Honeydew_" data-uid="VRageMath.Color.Honeydew*"></a>
  <h4 id="VRageMath_Color_Honeydew" data-uid="VRageMath.Color.Honeydew">Honeydew</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:240 G:255 B:240 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Honeydew { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_HotPink_" data-uid="VRageMath.Color.HotPink*"></a>
  <h4 id="VRageMath_Color_HotPink" data-uid="VRageMath.Color.HotPink">HotPink</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:105 B:180 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color HotPink { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_IndianRed_" data-uid="VRageMath.Color.IndianRed*"></a>
  <h4 id="VRageMath_Color_IndianRed" data-uid="VRageMath.Color.IndianRed">IndianRed</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:205 G:92 B:92 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color IndianRed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Indigo_" data-uid="VRageMath.Color.Indigo*"></a>
  <h4 id="VRageMath_Color_Indigo" data-uid="VRageMath.Color.Indigo">Indigo</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:75 G:0 B:130 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Indigo { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Ivory_" data-uid="VRageMath.Color.Ivory*"></a>
  <h4 id="VRageMath_Color_Ivory" data-uid="VRageMath.Color.Ivory">Ivory</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:255 B:240 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Ivory { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Khaki_" data-uid="VRageMath.Color.Khaki*"></a>
  <h4 id="VRageMath_Color_Khaki" data-uid="VRageMath.Color.Khaki">Khaki</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:240 G:230 B:140 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Khaki { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Lavender_" data-uid="VRageMath.Color.Lavender*"></a>
  <h4 id="VRageMath_Color_Lavender" data-uid="VRageMath.Color.Lavender">Lavender</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:230 G:230 B:250 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Lavender { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LavenderBlush_" data-uid="VRageMath.Color.LavenderBlush*"></a>
  <h4 id="VRageMath_Color_LavenderBlush" data-uid="VRageMath.Color.LavenderBlush">LavenderBlush</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:240 B:245 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LavenderBlush { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LawnGreen_" data-uid="VRageMath.Color.LawnGreen*"></a>
  <h4 id="VRageMath_Color_LawnGreen" data-uid="VRageMath.Color.LawnGreen">LawnGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:124 G:252 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LawnGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LemonChiffon_" data-uid="VRageMath.Color.LemonChiffon*"></a>
  <h4 id="VRageMath_Color_LemonChiffon" data-uid="VRageMath.Color.LemonChiffon">LemonChiffon</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:250 B:205 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LemonChiffon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightBlue_" data-uid="VRageMath.Color.LightBlue*"></a>
  <h4 id="VRageMath_Color_LightBlue" data-uid="VRageMath.Color.LightBlue">LightBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:173 G:216 B:230 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightCoral_" data-uid="VRageMath.Color.LightCoral*"></a>
  <h4 id="VRageMath_Color_LightCoral" data-uid="VRageMath.Color.LightCoral">LightCoral</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:240 G:128 B:128 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightCoral { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightCyan_" data-uid="VRageMath.Color.LightCyan*"></a>
  <h4 id="VRageMath_Color_LightCyan" data-uid="VRageMath.Color.LightCyan">LightCyan</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:224 G:255 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightCyan { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightGoldenrodYellow_" data-uid="VRageMath.Color.LightGoldenrodYellow*"></a>
  <h4 id="VRageMath_Color_LightGoldenrodYellow" data-uid="VRageMath.Color.LightGoldenrodYellow">LightGoldenrodYellow</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:250 G:250 B:210 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightGoldenrodYellow { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightGray_" data-uid="VRageMath.Color.LightGray*"></a>
  <h4 id="VRageMath_Color_LightGray" data-uid="VRageMath.Color.LightGray">LightGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:211 G:211 B:211 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightGreen_" data-uid="VRageMath.Color.LightGreen*"></a>
  <h4 id="VRageMath_Color_LightGreen" data-uid="VRageMath.Color.LightGreen">LightGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:144 G:238 B:144 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightPink_" data-uid="VRageMath.Color.LightPink*"></a>
  <h4 id="VRageMath_Color_LightPink" data-uid="VRageMath.Color.LightPink">LightPink</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:182 B:193 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightPink { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightSalmon_" data-uid="VRageMath.Color.LightSalmon*"></a>
  <h4 id="VRageMath_Color_LightSalmon" data-uid="VRageMath.Color.LightSalmon">LightSalmon</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:160 B:122 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightSalmon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightSeaGreen_" data-uid="VRageMath.Color.LightSeaGreen*"></a>
  <h4 id="VRageMath_Color_LightSeaGreen" data-uid="VRageMath.Color.LightSeaGreen">LightSeaGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:32 G:178 B:170 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightSeaGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightSkyBlue_" data-uid="VRageMath.Color.LightSkyBlue*"></a>
  <h4 id="VRageMath_Color_LightSkyBlue" data-uid="VRageMath.Color.LightSkyBlue">LightSkyBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:135 G:206 B:250 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightSkyBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightSlateGray_" data-uid="VRageMath.Color.LightSlateGray*"></a>
  <h4 id="VRageMath_Color_LightSlateGray" data-uid="VRageMath.Color.LightSlateGray">LightSlateGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:119 G:136 B:153 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightSlateGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightSteelBlue_" data-uid="VRageMath.Color.LightSteelBlue*"></a>
  <h4 id="VRageMath_Color_LightSteelBlue" data-uid="VRageMath.Color.LightSteelBlue">LightSteelBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:176 G:196 B:222 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightSteelBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LightYellow_" data-uid="VRageMath.Color.LightYellow*"></a>
  <h4 id="VRageMath_Color_LightYellow" data-uid="VRageMath.Color.LightYellow">LightYellow</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:255 B:224 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LightYellow { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Lime_" data-uid="VRageMath.Color.Lime*"></a>
  <h4 id="VRageMath_Color_Lime" data-uid="VRageMath.Color.Lime">Lime</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:255 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Lime { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_LimeGreen_" data-uid="VRageMath.Color.LimeGreen*"></a>
  <h4 id="VRageMath_Color_LimeGreen" data-uid="VRageMath.Color.LimeGreen">LimeGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:50 G:205 B:50 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color LimeGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Linen_" data-uid="VRageMath.Color.Linen*"></a>
  <h4 id="VRageMath_Color_Linen" data-uid="VRageMath.Color.Linen">Linen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:250 G:240 B:230 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Linen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Magenta_" data-uid="VRageMath.Color.Magenta*"></a>
  <h4 id="VRageMath_Color_Magenta" data-uid="VRageMath.Color.Magenta">Magenta</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:0 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Magenta { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Maroon_" data-uid="VRageMath.Color.Maroon*"></a>
  <h4 id="VRageMath_Color_Maroon" data-uid="VRageMath.Color.Maroon">Maroon</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:128 G:0 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Maroon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumAquamarine_" data-uid="VRageMath.Color.MediumAquamarine*"></a>
  <h4 id="VRageMath_Color_MediumAquamarine" data-uid="VRageMath.Color.MediumAquamarine">MediumAquamarine</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:102 G:205 B:170 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumAquamarine { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumBlue_" data-uid="VRageMath.Color.MediumBlue*"></a>
  <h4 id="VRageMath_Color_MediumBlue" data-uid="VRageMath.Color.MediumBlue">MediumBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:0 B:205 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumOrchid_" data-uid="VRageMath.Color.MediumOrchid*"></a>
  <h4 id="VRageMath_Color_MediumOrchid" data-uid="VRageMath.Color.MediumOrchid">MediumOrchid</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:186 G:85 B:211 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumOrchid { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumPurple_" data-uid="VRageMath.Color.MediumPurple*"></a>
  <h4 id="VRageMath_Color_MediumPurple" data-uid="VRageMath.Color.MediumPurple">MediumPurple</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:147 G:112 B:219 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumPurple { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumSeaGreen_" data-uid="VRageMath.Color.MediumSeaGreen*"></a>
  <h4 id="VRageMath_Color_MediumSeaGreen" data-uid="VRageMath.Color.MediumSeaGreen">MediumSeaGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:60 G:179 B:113 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumSeaGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumSlateBlue_" data-uid="VRageMath.Color.MediumSlateBlue*"></a>
  <h4 id="VRageMath_Color_MediumSlateBlue" data-uid="VRageMath.Color.MediumSlateBlue">MediumSlateBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:123 G:104 B:238 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumSlateBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumSpringGreen_" data-uid="VRageMath.Color.MediumSpringGreen*"></a>
  <h4 id="VRageMath_Color_MediumSpringGreen" data-uid="VRageMath.Color.MediumSpringGreen">MediumSpringGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:250 B:154 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumSpringGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumTurquoise_" data-uid="VRageMath.Color.MediumTurquoise*"></a>
  <h4 id="VRageMath_Color_MediumTurquoise" data-uid="VRageMath.Color.MediumTurquoise">MediumTurquoise</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:72 G:209 B:204 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumTurquoise { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MediumVioletRed_" data-uid="VRageMath.Color.MediumVioletRed*"></a>
  <h4 id="VRageMath_Color_MediumVioletRed" data-uid="VRageMath.Color.MediumVioletRed">MediumVioletRed</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:199 G:21 B:133 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MediumVioletRed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MidnightBlue_" data-uid="VRageMath.Color.MidnightBlue*"></a>
  <h4 id="VRageMath_Color_MidnightBlue" data-uid="VRageMath.Color.MidnightBlue">MidnightBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:25 G:25 B:112 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MidnightBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MintCream_" data-uid="VRageMath.Color.MintCream*"></a>
  <h4 id="VRageMath_Color_MintCream" data-uid="VRageMath.Color.MintCream">MintCream</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:245 G:255 B:250 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MintCream { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_MistyRose_" data-uid="VRageMath.Color.MistyRose*"></a>
  <h4 id="VRageMath_Color_MistyRose" data-uid="VRageMath.Color.MistyRose">MistyRose</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:228 B:225 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color MistyRose { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Moccasin_" data-uid="VRageMath.Color.Moccasin*"></a>
  <h4 id="VRageMath_Color_Moccasin" data-uid="VRageMath.Color.Moccasin">Moccasin</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:228 B:181 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Moccasin { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_NavajoWhite_" data-uid="VRageMath.Color.NavajoWhite*"></a>
  <h4 id="VRageMath_Color_NavajoWhite" data-uid="VRageMath.Color.NavajoWhite">NavajoWhite</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:222 B:173 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color NavajoWhite { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Navy_" data-uid="VRageMath.Color.Navy*"></a>
  <h4 id="VRageMath_Color_Navy" data-uid="VRageMath.Color.Navy">Navy</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color R:0 G:0 B:128 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Navy { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_OldLace_" data-uid="VRageMath.Color.OldLace*"></a>
  <h4 id="VRageMath_Color_OldLace" data-uid="VRageMath.Color.OldLace">OldLace</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:253 G:245 B:230 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color OldLace { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Olive_" data-uid="VRageMath.Color.Olive*"></a>
  <h4 id="VRageMath_Color_Olive" data-uid="VRageMath.Color.Olive">Olive</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:128 G:128 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Olive { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_OliveDrab_" data-uid="VRageMath.Color.OliveDrab*"></a>
  <h4 id="VRageMath_Color_OliveDrab" data-uid="VRageMath.Color.OliveDrab">OliveDrab</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:107 G:142 B:35 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color OliveDrab { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Orange_" data-uid="VRageMath.Color.Orange*"></a>
  <h4 id="VRageMath_Color_Orange" data-uid="VRageMath.Color.Orange">Orange</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:165 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Orange { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_OrangeRed_" data-uid="VRageMath.Color.OrangeRed*"></a>
  <h4 id="VRageMath_Color_OrangeRed" data-uid="VRageMath.Color.OrangeRed">OrangeRed</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:69 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color OrangeRed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Orchid_" data-uid="VRageMath.Color.Orchid*"></a>
  <h4 id="VRageMath_Color_Orchid" data-uid="VRageMath.Color.Orchid">Orchid</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:218 G:112 B:214 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Orchid { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PaleGoldenrod_" data-uid="VRageMath.Color.PaleGoldenrod*"></a>
  <h4 id="VRageMath_Color_PaleGoldenrod" data-uid="VRageMath.Color.PaleGoldenrod">PaleGoldenrod</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:238 G:232 B:170 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PaleGoldenrod { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PaleGreen_" data-uid="VRageMath.Color.PaleGreen*"></a>
  <h4 id="VRageMath_Color_PaleGreen" data-uid="VRageMath.Color.PaleGreen">PaleGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:152 G:251 B:152 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PaleGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PaleTurquoise_" data-uid="VRageMath.Color.PaleTurquoise*"></a>
  <h4 id="VRageMath_Color_PaleTurquoise" data-uid="VRageMath.Color.PaleTurquoise">PaleTurquoise</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:175 G:238 B:238 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PaleTurquoise { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PaleVioletRed_" data-uid="VRageMath.Color.PaleVioletRed*"></a>
  <h4 id="VRageMath_Color_PaleVioletRed" data-uid="VRageMath.Color.PaleVioletRed">PaleVioletRed</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:219 G:112 B:147 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PaleVioletRed { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PapayaWhip_" data-uid="VRageMath.Color.PapayaWhip*"></a>
  <h4 id="VRageMath_Color_PapayaWhip" data-uid="VRageMath.Color.PapayaWhip">PapayaWhip</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:239 B:213 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PapayaWhip { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PeachPuff_" data-uid="VRageMath.Color.PeachPuff*"></a>
  <h4 id="VRageMath_Color_PeachPuff" data-uid="VRageMath.Color.PeachPuff">PeachPuff</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:218 B:185 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PeachPuff { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Peru_" data-uid="VRageMath.Color.Peru*"></a>
  <h4 id="VRageMath_Color_Peru" data-uid="VRageMath.Color.Peru">Peru</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:205 G:133 B:63 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Peru { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Pink_" data-uid="VRageMath.Color.Pink*"></a>
  <h4 id="VRageMath_Color_Pink" data-uid="VRageMath.Color.Pink">Pink</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:192 B:203 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Pink { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Plum_" data-uid="VRageMath.Color.Plum*"></a>
  <h4 id="VRageMath_Color_Plum" data-uid="VRageMath.Color.Plum">Plum</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:221 G:160 B:221 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Plum { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_PowderBlue_" data-uid="VRageMath.Color.PowderBlue*"></a>
  <h4 id="VRageMath_Color_PowderBlue" data-uid="VRageMath.Color.PowderBlue">PowderBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:176 G:224 B:230 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color PowderBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Purple_" data-uid="VRageMath.Color.Purple*"></a>
  <h4 id="VRageMath_Color_Purple" data-uid="VRageMath.Color.Purple">Purple</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:128 G:0 B:128 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Purple { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_R_" data-uid="VRageMath.Color.R*"></a>
  <h4 id="VRageMath_Color_R" data-uid="VRageMath.Color.R">R</h4>
  <div class="markdown level1 summary"><p>Gets or sets the red component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte R { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Red_" data-uid="VRageMath.Color.Red*"></a>
  <h4 id="VRageMath_Color_Red" data-uid="VRageMath.Color.Red">Red</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:0 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Red { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_RosyBrown_" data-uid="VRageMath.Color.RosyBrown*"></a>
  <h4 id="VRageMath_Color_RosyBrown" data-uid="VRageMath.Color.RosyBrown">RosyBrown</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:188 G:143 B:143 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color RosyBrown { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_RoyalBlue_" data-uid="VRageMath.Color.RoyalBlue*"></a>
  <h4 id="VRageMath_Color_RoyalBlue" data-uid="VRageMath.Color.RoyalBlue">RoyalBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:65 G:105 B:225 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color RoyalBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SaddleBrown_" data-uid="VRageMath.Color.SaddleBrown*"></a>
  <h4 id="VRageMath_Color_SaddleBrown" data-uid="VRageMath.Color.SaddleBrown">SaddleBrown</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:139 G:69 B:19 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SaddleBrown { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Salmon_" data-uid="VRageMath.Color.Salmon*"></a>
  <h4 id="VRageMath_Color_Salmon" data-uid="VRageMath.Color.Salmon">Salmon</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:250 G:128 B:114 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Salmon { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SandyBrown_" data-uid="VRageMath.Color.SandyBrown*"></a>
  <h4 id="VRageMath_Color_SandyBrown" data-uid="VRageMath.Color.SandyBrown">SandyBrown</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:244 G:164 B:96 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SandyBrown { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SeaGreen_" data-uid="VRageMath.Color.SeaGreen*"></a>
  <h4 id="VRageMath_Color_SeaGreen" data-uid="VRageMath.Color.SeaGreen">SeaGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:46 G:139 B:87 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SeaGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SeaShell_" data-uid="VRageMath.Color.SeaShell*"></a>
  <h4 id="VRageMath_Color_SeaShell" data-uid="VRageMath.Color.SeaShell">SeaShell</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:245 B:238 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SeaShell { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Sienna_" data-uid="VRageMath.Color.Sienna*"></a>
  <h4 id="VRageMath_Color_Sienna" data-uid="VRageMath.Color.Sienna">Sienna</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:160 G:82 B:45 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Sienna { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Silver_" data-uid="VRageMath.Color.Silver*"></a>
  <h4 id="VRageMath_Color_Silver" data-uid="VRageMath.Color.Silver">Silver</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:192 G:192 B:192 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Silver { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SkyBlue_" data-uid="VRageMath.Color.SkyBlue*"></a>
  <h4 id="VRageMath_Color_SkyBlue" data-uid="VRageMath.Color.SkyBlue">SkyBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:135 G:206 B:235 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SkyBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SlateBlue_" data-uid="VRageMath.Color.SlateBlue*"></a>
  <h4 id="VRageMath_Color_SlateBlue" data-uid="VRageMath.Color.SlateBlue">SlateBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:106 G:90 B:205 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SlateBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SlateGray_" data-uid="VRageMath.Color.SlateGray*"></a>
  <h4 id="VRageMath_Color_SlateGray" data-uid="VRageMath.Color.SlateGray">SlateGray</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:112 G:128 B:144 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SlateGray { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Snow_" data-uid="VRageMath.Color.Snow*"></a>
  <h4 id="VRageMath_Color_Snow" data-uid="VRageMath.Color.Snow">Snow</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:250 B:250 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Snow { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SpringGreen_" data-uid="VRageMath.Color.SpringGreen*"></a>
  <h4 id="VRageMath_Color_SpringGreen" data-uid="VRageMath.Color.SpringGreen">SpringGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:255 B:127 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SpringGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_SteelBlue_" data-uid="VRageMath.Color.SteelBlue*"></a>
  <h4 id="VRageMath_Color_SteelBlue" data-uid="VRageMath.Color.SteelBlue">SteelBlue</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:70 G:130 B:180 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color SteelBlue { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Tan_" data-uid="VRageMath.Color.Tan*"></a>
  <h4 id="VRageMath_Color_Tan" data-uid="VRageMath.Color.Tan">Tan</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:210 G:180 B:140 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Tan { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Teal_" data-uid="VRageMath.Color.Teal*"></a>
  <h4 id="VRageMath_Color_Teal" data-uid="VRageMath.Color.Teal">Teal</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:128 B:128 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Teal { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Thistle_" data-uid="VRageMath.Color.Thistle*"></a>
  <h4 id="VRageMath_Color_Thistle" data-uid="VRageMath.Color.Thistle">Thistle</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:216 G:191 B:216 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Thistle { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Tomato_" data-uid="VRageMath.Color.Tomato*"></a>
  <h4 id="VRageMath_Color_Tomato" data-uid="VRageMath.Color.Tomato">Tomato</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:99 B:71 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Tomato { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Transparent_" data-uid="VRageMath.Color.Transparent*"></a>
  <h4 id="VRageMath_Color_Transparent" data-uid="VRageMath.Color.Transparent">Transparent</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:0 G:0 B:0 A:0.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Transparent { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Turquoise_" data-uid="VRageMath.Color.Turquoise*"></a>
  <h4 id="VRageMath_Color_Turquoise" data-uid="VRageMath.Color.Turquoise">Turquoise</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:64 G:224 B:208 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Turquoise { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Violet_" data-uid="VRageMath.Color.Violet*"></a>
  <h4 id="VRageMath_Color_Violet" data-uid="VRageMath.Color.Violet">Violet</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:238 G:130 B:238 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Violet { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Wheat_" data-uid="VRageMath.Color.Wheat*"></a>
  <h4 id="VRageMath_Color_Wheat" data-uid="VRageMath.Color.Wheat">Wheat</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:245 G:222 B:179 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Wheat { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_White_" data-uid="VRageMath.Color.White*"></a>
  <h4 id="VRageMath_Color_White" data-uid="VRageMath.Color.White">White</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:255 B:255 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color White { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_WhiteSmoke_" data-uid="VRageMath.Color.WhiteSmoke*"></a>
  <h4 id="VRageMath_Color_WhiteSmoke" data-uid="VRageMath.Color.WhiteSmoke">WhiteSmoke</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:245 G:245 B:245 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color WhiteSmoke { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_X_" data-uid="VRageMath.Color.X*"></a>
  <h4 id="VRageMath_Color_X" data-uid="VRageMath.Color.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the red component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte X { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Y_" data-uid="VRageMath.Color.Y*"></a>
  <h4 id="VRageMath_Color_Y" data-uid="VRageMath.Color.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the green component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Y { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Yellow_" data-uid="VRageMath.Color.Yellow*"></a>
  <h4 id="VRageMath_Color_Yellow" data-uid="VRageMath.Color.Yellow">Yellow</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:255 G:255 B:0 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Yellow { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_YellowGreen_" data-uid="VRageMath.Color.YellowGreen*"></a>
  <h4 id="VRageMath_Color_YellowGreen" data-uid="VRageMath.Color.YellowGreen">YellowGreen</h4>
  <div class="markdown level1 summary"><p>Gets a system-defined color with the value R:154 G:205 B:50 A:255.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color YellowGreen { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Z_" data-uid="VRageMath.Color.Z*"></a>
  <h4 id="VRageMath_Color_Z" data-uid="VRageMath.Color.Z">Z</h4>
  <div class="markdown level1 summary"><p>Gets or sets the blue component value of this Color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public byte Z { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Byte</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Color_Darken_" data-uid="VRageMath.Color.Darken*"></a>
  <h4 id="VRageMath_Color_Darken_VRageMath_Color_System_Double_" data-uid="VRageMath.Color.Darken(VRageMath.Color,System.Double)">Darken(Color, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Darken(Color inColor, double inAmount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">inColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">inAmount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Equals_" data-uid="VRageMath.Color.Equals*"></a>
  <h4 id="VRageMath_Color_Equals_System_Object_" data-uid="VRageMath.Color.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Test an instance of a color object to see if it is equal to this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>A color object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Equals_" data-uid="VRageMath.Color.Equals*"></a>
  <h4 id="VRageMath_Color_Equals_VRageMath_Color_" data-uid="VRageMath.Color.Equals(VRageMath.Color)">Equals(Color)</h4>
  <div class="markdown level1 summary"><p>Test a color to see if it is equal to the color in this instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Color other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_FromNonPremultiplied_" data-uid="VRageMath.Color.FromNonPremultiplied*"></a>
  <h4 id="VRageMath_Color_FromNonPremultiplied_System_Int32_System_Int32_System_Int32_System_Int32_" data-uid="VRageMath.Color.FromNonPremultiplied(System.Int32,System.Int32,System.Int32,System.Int32)">FromNonPremultiplied(Int32, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Converts a non-premultipled alpha color to a color that contains premultiplied alpha.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color FromNonPremultiplied(int r, int g, int b, int a)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">r</span></td>
        <td><p>Red component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">g</span></td>
        <td><p>Green component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>Blue component.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>Alpha component.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_FromNonPremultiplied_" data-uid="VRageMath.Color.FromNonPremultiplied*"></a>
  <h4 id="VRageMath_Color_FromNonPremultiplied_VRageMath_Vector4_" data-uid="VRageMath.Color.FromNonPremultiplied(VRageMath.Vector4)">FromNonPremultiplied(Vector4)</h4>
  <div class="markdown level1 summary"><p>Convert a non premultipled color into color data that contains alpha.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color FromNonPremultiplied(Vector4 vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_GetHashCode_" data-uid="VRageMath.Color.GetHashCode*"></a>
  <h4 id="VRageMath_Color_GetHashCode" data-uid="VRageMath.Color.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Serves as a hash function for a particular type.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Lerp_" data-uid="VRageMath.Color.Lerp*"></a>
  <h4 id="VRageMath_Color_Lerp_VRageMath_Color_VRageMath_Color_System_Single_" data-uid="VRageMath.Color.Lerp(VRageMath.Color,VRageMath.Color,System.Single)">Lerp(Color, Color, Single)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolate a color.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Lerp(Color value1, Color value2, float amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Interpolation factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Lighten_" data-uid="VRageMath.Color.Lighten*"></a>
  <h4 id="VRageMath_Color_Lighten_VRageMath_Color_System_Double_" data-uid="VRageMath.Color.Lighten(VRageMath.Color,System.Double)">Lighten(Color, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Lighten(Color inColor, double inAmount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">inColor</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">inAmount</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_Multiply_" data-uid="VRageMath.Color.Multiply*"></a>
  <h4 id="VRageMath_Color_Multiply_VRageMath_Color_System_Single_" data-uid="VRageMath.Color.Multiply(VRageMath.Color,System.Single)">Multiply(Color, Single)</h4>
  <div class="markdown level1 summary"><p>Multiply each color component by the scale factor.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color Multiply(Color value, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The source, four-component color.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>The scale factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_ToGray_" data-uid="VRageMath.Color.ToGray*"></a>
  <h4 id="VRageMath_Color_ToGray" data-uid="VRageMath.Color.ToGray">ToGray()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color ToGray()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_ToString_" data-uid="VRageMath.Color.ToString*"></a>
  <h4 id="VRageMath_Color_ToString" data-uid="VRageMath.Color.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Gets a string representation of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_ToVector3_" data-uid="VRageMath.Color.ToVector3*"></a>
  <h4 id="VRageMath_Color_ToVector3" data-uid="VRageMath.Color.ToVector3">ToVector3()</h4>
  <div class="markdown level1 summary"><p>Gets a three-component vector representation for this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 ToVector3()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_ToVector4_" data-uid="VRageMath.Color.ToVector4*"></a>
  <h4 id="VRageMath_Color_ToVector4" data-uid="VRageMath.Color.ToVector4">ToVector4()</h4>
  <div class="markdown level1 summary"><p>Gets a four-component vector representation for this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4 ToVector4()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Color_op_Addition_" data-uid="VRageMath.Color.op_Addition*"></a>
  <h4 id="VRageMath_Color_op_Addition_VRageMath_Color_VRageMath_Color_" data-uid="VRageMath.Color.op_Addition(VRageMath.Color,VRageMath.Color)">Addition(Color, Color)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color operator +(Color value, Color other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">other</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Equality_" data-uid="VRageMath.Color.op_Equality*"></a>
  <h4 id="VRageMath_Color_op_Equality_VRageMath_Color_VRageMath_Color_" data-uid="VRageMath.Color.op_Equality(VRageMath.Color,VRageMath.Color)">Equality(Color, Color)</h4>
  <div class="markdown level1 summary"><p>Equality operator.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Color a, Color b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Implicit_" data-uid="VRageMath.Color.op_Implicit*"></a>
  <h4 id="VRageMath_Color_op_Implicit_VRageMath_Color__VRageMath_Vector3" data-uid="VRageMath.Color.op_Implicit(VRageMath.Color)~VRageMath.Vector3">Implicit(Color to Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Vector3(Color v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Implicit_" data-uid="VRageMath.Color.op_Implicit*"></a>
  <h4 id="VRageMath_Color_op_Implicit_VRageMath_Color__VRageMath_Vector4" data-uid="VRageMath.Color.op_Implicit(VRageMath.Color)~VRageMath.Vector4">Implicit(Color to Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Vector4(Color v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Implicit_" data-uid="VRageMath.Color.op_Implicit*"></a>
  <h4 id="VRageMath_Color_op_Implicit_VRageMath_Vector3__VRageMath_Color" data-uid="VRageMath.Color.op_Implicit(VRageMath.Vector3)~VRageMath.Color">Implicit(Vector3 to Color)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Color(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Implicit_" data-uid="VRageMath.Color.op_Implicit*"></a>
  <h4 id="VRageMath_Color_op_Implicit_VRageMath_Vector4__VRageMath_Color" data-uid="VRageMath.Color.op_Implicit(VRageMath.Vector4)~VRageMath.Color">Implicit(Vector4 to Color)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Color(Vector4 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Inequality_" data-uid="VRageMath.Color.op_Inequality*"></a>
  <h4 id="VRageMath_Color_op_Inequality_VRageMath_Color_VRageMath_Color_" data-uid="VRageMath.Color.op_Inequality(VRageMath.Color,VRageMath.Color)">Inequality(Color, Color)</h4>
  <div class="markdown level1 summary"><p>Equality operator for Testing two color objects to see if they are equal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Color a, Color b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>A four-component color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Multiply_" data-uid="VRageMath.Color.op_Multiply*"></a>
  <h4 id="VRageMath_Color_op_Multiply_VRageMath_Color_System_Single_" data-uid="VRageMath.Color.op_Multiply(VRageMath.Color,System.Single)">Multiply(Color, Single)</h4>
  <div class="markdown level1 summary"><p>Multiply operator.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color operator *(Color value, float scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A four-component color</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>Scale factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Color_op_Multiply_" data-uid="VRageMath.Color.op_Multiply*"></a>
  <h4 id="VRageMath_Color_op_Multiply_VRageMath_Color_VRageMath_Color_" data-uid="VRageMath.Color.op_Multiply(VRageMath.Color,VRageMath.Color)">Multiply(Color, Color)</h4>
  <div class="markdown level1 summary"><p>Multiply operator.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Color operator *(Color value, Color other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A four-component color</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>Multiplied color.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Color.html">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ColorToHSV_VRageMath_Color_">ColorExtensions.ColorToHSV(Color)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_ColorToHSVDX11_VRageMath_Color_">ColorExtensions.ColorToHSVDX11(Color)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_HueDistance_VRageMath_Color_System_Single_">ColorExtensions.HueDistance(Color, Single)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_HueDistance_VRageMath_Color_VRageMath_Color_">ColorExtensions.HueDistance(Color, Color)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_Shade_VRageMath_Color_System_Single_">ColorExtensions.Shade(Color, Single)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_Tint_VRageMath_Color_System_Single_">ColorExtensions.Tint(Color, Single)</a>
  </div>
  <div>
      <a class="xref" href="VRageMath.ColorExtensions.html#VRageMath_ColorExtensions_Alpha_VRageMath_Color_System_Single_">ColorExtensions.Alpha(Color, Single)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
