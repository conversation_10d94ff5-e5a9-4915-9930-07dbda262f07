﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class QuaternionD
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class QuaternionD
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.QuaternionD">
  
  
  <h1 id="VRageMath_QuaternionD" data-uid="VRageMath.QuaternionD" class="text-break">Class QuaternionD
  </h1>
  <div class="markdown level0 summary"><p>Defines a four-dimensional vector (x,y,z,w), which is used to efficiently rotate an object about the (x, y, z) vector by the angle theta, where w = cos(theta/2).
Uses double precision floating point numbers for calculation and storage</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">QuaternionD</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_QuaternionD_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class QuaternionD : ValueType</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_QuaternionD__ctor_" data-uid="VRageMath.QuaternionD.#ctor*"></a>
  <h4 id="VRageMath_QuaternionD__ctor_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.QuaternionD.#ctor(System.Double,System.Double,System.Double,System.Double)">QuaternionD(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public QuaternionD(double x, double y, double z, double w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>The x-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>The y-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>The z-value of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>The w-value of the quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD__ctor_" data-uid="VRageMath.QuaternionD.#ctor*"></a>
  <h4 id="VRageMath_QuaternionD__ctor_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.QuaternionD.#ctor(VRageMath.Vector3D,System.Double)">QuaternionD(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public QuaternionD(Vector3D vectorPart, double scalarPart)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vectorPart</span></td>
        <td><p>The vector component of the quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scalarPart</span></td>
        <td><p>The rotation component of the quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_QuaternionD_Identity" data-uid="VRageMath.QuaternionD.Identity">Identity</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Identity</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_QuaternionD_W" data-uid="VRageMath.QuaternionD.W">W</h4>
  <div class="markdown level1 summary"><p>Specifies the rotation component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double W</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_QuaternionD_X" data-uid="VRageMath.QuaternionD.X">X</h4>
  <div class="markdown level1 summary"><p>Specifies the x-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_QuaternionD_Y" data-uid="VRageMath.QuaternionD.Y">Y</h4>
  <div class="markdown level1 summary"><p>Specifies the y-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_QuaternionD_Z" data-uid="VRageMath.QuaternionD.Z">Z</h4>
  <div class="markdown level1 summary"><p>Specifies the z-value of the vector component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Z</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_QuaternionD_Add_" data-uid="VRageMath.QuaternionD.Add*"></a>
  <h4 id="VRageMath_QuaternionD_Add_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Add(VRageMath.QuaternionD,VRageMath.QuaternionD)">Add(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Add(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Add_" data-uid="VRageMath.QuaternionD.Add*"></a>
  <h4 id="VRageMath_QuaternionD_Add_VRageMath_QuaternionD__VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Add(VRageMath.QuaternionD@,VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Add(ref QuaternionD, ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref QuaternionD quaternion1, ref QuaternionD quaternion2, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of adding the Quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Concatenate_" data-uid="VRageMath.QuaternionD.Concatenate*"></a>
  <h4 id="VRageMath_QuaternionD_Concatenate_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Concatenate(VRageMath.QuaternionD,VRageMath.QuaternionD)">Concatenate(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Concatenates two Quaternions; the result represents the value1 rotation followed by the value2 rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Concatenate(QuaternionD value1, QuaternionD value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first QuaternionD rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second QuaternionD rotation in the series.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Concatenate_" data-uid="VRageMath.QuaternionD.Concatenate*"></a>
  <h4 id="VRageMath_QuaternionD_Concatenate_VRageMath_QuaternionD__VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Concatenate(VRageMath.QuaternionD@,VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Concatenate(ref QuaternionD, ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Concatenates two Quaternions; the result represents the value1 rotation followed by the value2 rotation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Concatenate(ref QuaternionD value1, ref QuaternionD value2, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first QuaternionD rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second QuaternionD rotation in the series.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The QuaternionD rotation representing the concatenation of value1 followed by value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Conjugate_" data-uid="VRageMath.QuaternionD.Conjugate*"></a>
  <h4 id="VRageMath_QuaternionD_Conjugate" data-uid="VRageMath.QuaternionD.Conjugate">Conjugate()</h4>
  <div class="markdown level1 summary"><p>Transforms this QuaternionD into its conjugate.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Conjugate()</code></pre>
  </div>
  
  
  <a id="VRageMath_QuaternionD_Conjugate_" data-uid="VRageMath.QuaternionD.Conjugate*"></a>
  <h4 id="VRageMath_QuaternionD_Conjugate_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Conjugate(VRageMath.QuaternionD)">Conjugate(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Returns the conjugate of a specified QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Conjugate(QuaternionD value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The QuaternionD of which to return the conjugate.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Conjugate_" data-uid="VRageMath.QuaternionD.Conjugate*"></a>
  <h4 id="VRageMath_QuaternionD_Conjugate_VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Conjugate(VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Conjugate(ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Returns the conjugate of a specified QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Conjugate(ref QuaternionD value, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The QuaternionD of which to return the conjugate.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing QuaternionD filled in to be the conjugate of the specified one.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromAxisAngle_" data-uid="VRageMath.QuaternionD.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromAxisAngle_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.QuaternionD.CreateFromAxisAngle(VRageMath.Vector3D,System.Double)">CreateFromAxisAngle(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a QuaternionD from a vector and an angle to rotate about the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD CreateFromAxisAngle(Vector3D axis, double angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The vector to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromAxisAngle_" data-uid="VRageMath.QuaternionD.CreateFromAxisAngle*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromAxisAngle_VRageMath_Vector3D__System_Double_VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.CreateFromAxisAngle(VRageMath.Vector3D@,System.Double,VRageMath.QuaternionD@)">CreateFromAxisAngle(ref Vector3D, Double, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Creates a QuaternionD from a vector and an angle to rotate about the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromAxisAngle(ref Vector3D axis, double angle, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The vector to rotate around.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td><p>The angle to rotate around the vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromForwardUp_" data-uid="VRageMath.QuaternionD.CreateFromForwardUp*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromForwardUp_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.QuaternionD.CreateFromForwardUp(VRageMath.Vector3D,VRageMath.Vector3D)">CreateFromForwardUp(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Works for normalized vectors only</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD CreateFromForwardUp(Vector3D forward, Vector3D up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromRotationMatrix_" data-uid="VRageMath.QuaternionD.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromRotationMatrix_VRageMath_MatrixD_" data-uid="VRageMath.QuaternionD.CreateFromRotationMatrix(VRageMath.MatrixD)">CreateFromRotationMatrix(MatrixD)</h4>
  <div class="markdown level1 summary"><p>Creates a QuaternionD from a rotation MatrixD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD CreateFromRotationMatrix(MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The rotation MatrixD to create the QuaternionD from.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromRotationMatrix_" data-uid="VRageMath.QuaternionD.CreateFromRotationMatrix*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromRotationMatrix_VRageMath_MatrixD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.CreateFromRotationMatrix(VRageMath.MatrixD@,VRageMath.QuaternionD@)">CreateFromRotationMatrix(ref MatrixD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Creates a QuaternionD from a rotation MatrixD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromRotationMatrix(ref MatrixD matrix, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The rotation MatrixD to create the QuaternionD from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The created QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromTwoVectors_" data-uid="VRageMath.QuaternionD.CreateFromTwoVectors*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromTwoVectors_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRageMath.QuaternionD.CreateFromTwoVectors(VRageMath.Vector3D,VRageMath.Vector3D)">CreateFromTwoVectors(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD CreateFromTwoVectors(Vector3D firstVector, Vector3D secondVector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">firstVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">secondVector</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromTwoVectors_" data-uid="VRageMath.QuaternionD.CreateFromTwoVectors*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromTwoVectors_VRageMath_Vector3D__VRageMath_Vector3D__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.CreateFromTwoVectors(VRageMath.Vector3D@,VRageMath.Vector3D@,VRageMath.QuaternionD@)">CreateFromTwoVectors(ref Vector3D, ref Vector3D, out QuaternionD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromTwoVectors(ref Vector3D firstVector, ref Vector3D secondVector, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">firstVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">secondVector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromYawPitchRoll_" data-uid="VRageMath.QuaternionD.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromYawPitchRoll_System_Double_System_Double_System_Double_" data-uid="VRageMath.QuaternionD.CreateFromYawPitchRoll(System.Double,System.Double,System.Double)">CreateFromYawPitchRoll(Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new QuaternionD from specified yaw, pitch, and roll angles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD CreateFromYawPitchRoll(double yaw, double pitch, double roll)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>The yaw angle, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>The pitch angle, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>The roll angle, in radians, around the z-axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_CreateFromYawPitchRoll_" data-uid="VRageMath.QuaternionD.CreateFromYawPitchRoll*"></a>
  <h4 id="VRageMath_QuaternionD_CreateFromYawPitchRoll_System_Double_System_Double_System_Double_VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.CreateFromYawPitchRoll(System.Double,System.Double,System.Double,VRageMath.QuaternionD@)">CreateFromYawPitchRoll(Double, Double, Double, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Creates a new QuaternionD from specified yaw, pitch, and roll angles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CreateFromYawPitchRoll(double yaw, double pitch, double roll, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">yaw</span></td>
        <td><p>The yaw angle, in radians, around the y-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">pitch</span></td>
        <td><p>The pitch angle, in radians, around the x-axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">roll</span></td>
        <td><p>The roll angle, in radians, around the z-axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] An existing QuaternionD filled in to express the specified yaw, pitch, and roll angles.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Divide_" data-uid="VRageMath.QuaternionD.Divide*"></a>
  <h4 id="VRageMath_QuaternionD_Divide_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Divide(VRageMath.QuaternionD,VRageMath.QuaternionD)">Divide(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Divides a QuaternionD by another QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Divide(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Divide_" data-uid="VRageMath.QuaternionD.Divide*"></a>
  <h4 id="VRageMath_QuaternionD_Divide_VRageMath_QuaternionD__VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Divide(VRageMath.QuaternionD@,VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Divide(ref QuaternionD, ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Divides a QuaternionD by another QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref QuaternionD quaternion1, ref QuaternionD quaternion2, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Dot_" data-uid="VRageMath.QuaternionD.Dot*"></a>
  <h4 id="VRageMath_QuaternionD_Dot_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Dot(VRageMath.QuaternionD,VRageMath.QuaternionD)">Dot(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Dot(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Dot_" data-uid="VRageMath.QuaternionD.Dot*"></a>
  <h4 id="VRageMath_QuaternionD_Dot_VRageMath_QuaternionD__VRageMath_QuaternionD__System_Double__" data-uid="VRageMath.QuaternionD.Dot(VRageMath.QuaternionD@,VRageMath.QuaternionD@,System.Double@)">Dot(ref QuaternionD, ref QuaternionD, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref QuaternionD quaternion1, ref QuaternionD quaternion2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Dot product of the Quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Equals_" data-uid="VRageMath.QuaternionD.Equals*"></a>
  <h4 id="VRageMath_QuaternionD_Equals_System_Object_" data-uid="VRageMath.QuaternionD.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Equals_" data-uid="VRageMath.QuaternionD.Equals*"></a>
  <h4 id="VRageMath_QuaternionD_Equals_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Equals(VRageMath.QuaternionD)">Equals(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(QuaternionD other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The QuaternionD to compare with the current QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_FromVector4_" data-uid="VRageMath.QuaternionD.FromVector4*"></a>
  <h4 id="VRageMath_QuaternionD_FromVector4_VRageMath_Vector4D_" data-uid="VRageMath.QuaternionD.FromVector4(VRageMath.Vector4D)">FromVector4(Vector4D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD FromVector4(Vector4D v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_GetAxisAngle_" data-uid="VRageMath.QuaternionD.GetAxisAngle*"></a>
  <h4 id="VRageMath_QuaternionD_GetAxisAngle_VRageMath_Vector3D__System_Double__" data-uid="VRageMath.QuaternionD.GetAxisAngle(VRageMath.Vector3D@,System.Double@)">GetAxisAngle(out Vector3D, out Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void GetAxisAngle(out Vector3D axis, out double angle)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">axis</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">angle</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_GetHashCode_" data-uid="VRageMath.QuaternionD.GetHashCode*"></a>
  <h4 id="VRageMath_QuaternionD_GetHashCode" data-uid="VRageMath.QuaternionD.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Get the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Inverse_" data-uid="VRageMath.QuaternionD.Inverse*"></a>
  <h4 id="VRageMath_QuaternionD_Inverse_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Inverse(VRageMath.QuaternionD)">Inverse(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Returns the inverse of a QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Inverse(QuaternionD quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Inverse_" data-uid="VRageMath.QuaternionD.Inverse*"></a>
  <h4 id="VRageMath_QuaternionD_Inverse_VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Inverse(VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Inverse(ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Returns the inverse of a QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Inverse(ref QuaternionD quaternion, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The inverse of the QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_IsZero_" data-uid="VRageMath.QuaternionD.IsZero*"></a>
  <h4 id="VRageMath_QuaternionD_IsZero_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.IsZero(VRageMath.QuaternionD)">IsZero(QuaternionD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(QuaternionD value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_IsZero_" data-uid="VRageMath.QuaternionD.IsZero*"></a>
  <h4 id="VRageMath_QuaternionD_IsZero_VRageMath_QuaternionD_System_Double_" data-uid="VRageMath.QuaternionD.IsZero(VRageMath.QuaternionD,System.Double)">IsZero(QuaternionD, Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsZero(QuaternionD value, double epsilon)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">epsilon</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Length_" data-uid="VRageMath.QuaternionD.Length*"></a>
  <h4 id="VRageMath_QuaternionD_Length" data-uid="VRageMath.QuaternionD.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of a QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_LengthSquared_" data-uid="VRageMath.QuaternionD.LengthSquared*"></a>
  <h4 id="VRageMath_QuaternionD_LengthSquared" data-uid="VRageMath.QuaternionD.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length squared of a QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Lerp_" data-uid="VRageMath.QuaternionD.Lerp*"></a>
  <h4 id="VRageMath_QuaternionD_Lerp_VRageMath_QuaternionD_VRageMath_QuaternionD_System_Double_" data-uid="VRageMath.QuaternionD.Lerp(VRageMath.QuaternionD,VRageMath.QuaternionD,System.Double)">Lerp(QuaternionD, QuaternionD, Double)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Lerp(QuaternionD quaternion1, QuaternionD quaternion2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value indicating how far to interpolate between the quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Lerp_" data-uid="VRageMath.QuaternionD.Lerp*"></a>
  <h4 id="VRageMath_QuaternionD_Lerp_VRageMath_QuaternionD__VRageMath_QuaternionD__System_Double_VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Lerp(VRageMath.QuaternionD@,VRageMath.QuaternionD@,System.Double,VRageMath.QuaternionD@)">Lerp(ref QuaternionD, ref QuaternionD, Double, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Linearly interpolates between two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref QuaternionD quaternion1, ref QuaternionD quaternion2, double amount, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value indicating how far to interpolate between the quaternions.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The resulting quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Multiply_" data-uid="VRageMath.QuaternionD.Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_Multiply_VRageMath_QuaternionD_System_Double_" data-uid="VRageMath.QuaternionD.Multiply(VRageMath.QuaternionD,System.Double)">Multiply(QuaternionD, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Multiply(QuaternionD quaternion1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Multiply_" data-uid="VRageMath.QuaternionD.Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_Multiply_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Multiply(VRageMath.QuaternionD,VRageMath.QuaternionD)">Multiply(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Multiply(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>The quaternion on the left of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The quaternion on the right of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Multiply_" data-uid="VRageMath.QuaternionD.Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_Multiply_VRageMath_QuaternionD__System_Double_VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Multiply(VRageMath.QuaternionD@,System.Double,VRageMath.QuaternionD@)">Multiply(ref QuaternionD, Double, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref QuaternionD quaternion1, double scaleFactor, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Multiply_" data-uid="VRageMath.QuaternionD.Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_Multiply_VRageMath_QuaternionD__VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Multiply(VRageMath.QuaternionD@,VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Multiply(ref QuaternionD, ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref QuaternionD quaternion1, ref QuaternionD quaternion2, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>The quaternion on the left of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The quaternion on the right of the multiplication.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Negate_" data-uid="VRageMath.QuaternionD.Negate*"></a>
  <h4 id="VRageMath_QuaternionD_Negate_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Negate(VRageMath.QuaternionD)">Negate(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Negate(QuaternionD quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Negate_" data-uid="VRageMath.QuaternionD.Negate*"></a>
  <h4 id="VRageMath_QuaternionD_Negate_VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Negate(VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Negate(ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref QuaternionD quaternion, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Negated quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Normalize_" data-uid="VRageMath.QuaternionD.Normalize*"></a>
  <h4 id="VRageMath_QuaternionD_Normalize" data-uid="VRageMath.QuaternionD.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_QuaternionD_Normalize_" data-uid="VRageMath.QuaternionD.Normalize*"></a>
  <h4 id="VRageMath_QuaternionD_Normalize_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Normalize(VRageMath.QuaternionD)">Normalize(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Normalize(QuaternionD quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Normalize_" data-uid="VRageMath.QuaternionD.Normalize*"></a>
  <h4 id="VRageMath_QuaternionD_Normalize_VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Normalize(VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Normalize(ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Divides each component of the quaternion by the length of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref QuaternionD quaternion, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Normalized quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Slerp_" data-uid="VRageMath.QuaternionD.Slerp*"></a>
  <h4 id="VRageMath_QuaternionD_Slerp_VRageMath_QuaternionD_VRageMath_QuaternionD_System_Double_" data-uid="VRageMath.QuaternionD.Slerp(VRageMath.QuaternionD,VRageMath.QuaternionD,System.Double)">Slerp(QuaternionD, QuaternionD, Double)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two quaternions, using spherical linear interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Slerp(QuaternionD quaternion1, QuaternionD quaternion2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value that indicates how far to interpolate between the quaternions.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Slerp_" data-uid="VRageMath.QuaternionD.Slerp*"></a>
  <h4 id="VRageMath_QuaternionD_Slerp_VRageMath_QuaternionD__VRageMath_QuaternionD__System_Double_VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Slerp(VRageMath.QuaternionD@,VRageMath.QuaternionD@,System.Double,VRageMath.QuaternionD@)">Slerp(ref QuaternionD, ref QuaternionD, Double, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two quaternions, using spherical linear interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Slerp(ref QuaternionD quaternion1, ref QuaternionD quaternion2, double amount, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value that indicates how far to interpolate between the quaternions.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Subtract_" data-uid="VRageMath.QuaternionD.Subtract*"></a>
  <h4 id="VRageMath_QuaternionD_Subtract_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.Subtract(VRageMath.QuaternionD,VRageMath.QuaternionD)">Subtract(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD Subtract(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_Subtract_" data-uid="VRageMath.QuaternionD.Subtract*"></a>
  <h4 id="VRageMath_QuaternionD_Subtract_VRageMath_QuaternionD__VRageMath_QuaternionD__VRageMath_QuaternionD__" data-uid="VRageMath.QuaternionD.Subtract(VRageMath.QuaternionD@,VRageMath.QuaternionD@,VRageMath.QuaternionD@)">Subtract(ref QuaternionD, ref QuaternionD, out QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref QuaternionD quaternion1, ref QuaternionD quaternion2, out QuaternionD result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_ToString_" data-uid="VRageMath.QuaternionD.ToString*"></a>
  <h4 id="VRageMath_QuaternionD_ToString" data-uid="VRageMath.QuaternionD.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retireves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_ToVector4_" data-uid="VRageMath.QuaternionD.ToVector4*"></a>
  <h4 id="VRageMath_QuaternionD_ToVector4" data-uid="VRageMath.QuaternionD.ToVector4">ToVector4()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D ToVector4()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_QuaternionD_op_Addition_" data-uid="VRageMath.QuaternionD.op_Addition*"></a>
  <h4 id="VRageMath_QuaternionD_op_Addition_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Addition(VRageMath.QuaternionD,VRageMath.QuaternionD)">Addition(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Adds two Quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator +(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>QuaternionD to add.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Division_" data-uid="VRageMath.QuaternionD.op_Division*"></a>
  <h4 id="VRageMath_QuaternionD_op_Division_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Division(VRageMath.QuaternionD,VRageMath.QuaternionD)">Division(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Divides a QuaternionD by another QuaternionD.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator /(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Equality_" data-uid="VRageMath.QuaternionD.op_Equality*"></a>
  <h4 id="VRageMath_QuaternionD_op_Equality_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Equality(VRageMath.QuaternionD,VRageMath.QuaternionD)">Equality(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Compares two Quaternions for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Inequality_" data-uid="VRageMath.QuaternionD.op_Inequality*"></a>
  <h4 id="VRageMath_QuaternionD_op_Inequality_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Inequality(VRageMath.QuaternionD,VRageMath.QuaternionD)">Inequality(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Compare two Quaternions for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source QuaternionD.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Multiply_" data-uid="VRageMath.QuaternionD.op_Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_op_Multiply_VRageMath_QuaternionD_System_Double_" data-uid="VRageMath.QuaternionD.op_Multiply(VRageMath.QuaternionD,System.Double)">Multiply(QuaternionD, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a quaternion by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator *(QuaternionD quaternion1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Multiply_" data-uid="VRageMath.QuaternionD.op_Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_op_Multiply_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Multiply(VRageMath.QuaternionD,VRageMath.QuaternionD)">Multiply(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Multiplies two quaternions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator *(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Multiply_" data-uid="VRageMath.QuaternionD.op_Multiply*"></a>
  <h4 id="VRageMath_QuaternionD_op_Multiply_VRageMath_QuaternionD_VRageMath_Vector3D_" data-uid="VRageMath.QuaternionD.op_Multiply(VRageMath.QuaternionD,VRageMath.Vector3D)">Multiply(QuaternionD, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a quaternion. Resulting a vector rotated by quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3D operator *(QuaternionD quaternion, Vector3D vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Vector to be rotated.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_Subtraction_" data-uid="VRageMath.QuaternionD.op_Subtraction*"></a>
  <h4 id="VRageMath_QuaternionD_op_Subtraction_VRageMath_QuaternionD_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_Subtraction(VRageMath.QuaternionD,VRageMath.QuaternionD)">Subtraction(QuaternionD, QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Subtracts a quaternion from another quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator -(QuaternionD quaternion1, QuaternionD quaternion2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion1</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion2</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_QuaternionD_op_UnaryNegation_" data-uid="VRageMath.QuaternionD.op_UnaryNegation*"></a>
  <h4 id="VRageMath_QuaternionD_op_UnaryNegation_VRageMath_QuaternionD_" data-uid="VRageMath.QuaternionD.op_UnaryNegation(VRageMath.QuaternionD)">UnaryNegation(QuaternionD)</h4>
  <div class="markdown level1 summary"><p>Flips the sign of each component of the quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static QuaternionD operator -(QuaternionD quaternion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td><span class="parametername">quaternion</span></td>
        <td><p>Source quaternion.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.QuaternionD.html">QuaternionD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
