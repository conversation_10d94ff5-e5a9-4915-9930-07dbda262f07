﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Vector4D
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Vector4D
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Vector4D">
  
  
  <h1 id="VRageMath_Vector4D" data-uid="VRageMath.Vector4D" class="text-break">Class Vector4D
  </h1>
  <div class="markdown level0 summary"><p>Defines a vector with four components.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Vector4D</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Vector4D_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Vector4D : ValueType, IEquatable&lt;Vector4&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Vector4D__ctor_" data-uid="VRageMath.Vector4D.#ctor*"></a>
  <h4 id="VRageMath_Vector4D__ctor_System_Double_" data-uid="VRageMath.Vector4D.#ctor(System.Double)">Vector4D(Double)</h4>
  <div class="markdown level1 summary"><p>Creates a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D(double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">value</span></td>
        <td><p>Value to initialize each component to.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D__ctor_" data-uid="VRageMath.Vector4D.#ctor*"></a>
  <h4 id="VRageMath_Vector4D__ctor_System_Double_System_Double_System_Double_System_Double_" data-uid="VRageMath.Vector4D.#ctor(System.Double,System.Double,System.Double,System.Double)">Vector4D(Double, Double, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D(double x, double y, double z, double w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>Initial value for the x-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>Initial value for the y-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D__ctor_" data-uid="VRageMath.Vector4D.#ctor*"></a>
  <h4 id="VRageMath_Vector4D__ctor_VRageMath_Vector2_System_Double_System_Double_" data-uid="VRageMath.Vector4D.#ctor(VRageMath.Vector2,System.Double,System.Double)">Vector4D(Vector2, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D(Vector2 value, double z, double w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A vector containing the values to initialize x and y components with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">z</span></td>
        <td><p>Initial value for the z-component of the vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D__ctor_" data-uid="VRageMath.Vector4D.#ctor*"></a>
  <h4 id="VRageMath_Vector4D__ctor_VRageMath_Vector3D_System_Double_" data-uid="VRageMath.Vector4D.#ctor(VRageMath.Vector3D,System.Double)">Vector4D(Vector3D, Double)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector4D(Vector3D value, double w)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>A vector containing the values to initialize x, y, and z components with.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">w</span></td>
        <td><p>Initial value for the w-component of the vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Vector4D_One" data-uid="VRageMath.Vector4D.One">One</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D One</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_UnitW" data-uid="VRageMath.Vector4D.UnitW">UnitW</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D UnitW</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_UnitX" data-uid="VRageMath.Vector4D.UnitX">UnitX</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D UnitX</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_UnitY" data-uid="VRageMath.Vector4D.UnitY">UnitY</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D UnitY</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_UnitZ" data-uid="VRageMath.Vector4D.UnitZ">UnitZ</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D UnitZ</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_W" data-uid="VRageMath.Vector4D.W">W</h4>
  <div class="markdown level1 summary"><p>Gets or sets the w-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double W</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_X" data-uid="VRageMath.Vector4D.X">X</h4>
  <div class="markdown level1 summary"><p>Gets or sets the x-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_Y" data-uid="VRageMath.Vector4D.Y">Y</h4>
  <div class="markdown level1 summary"><p>Gets or sets the y-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_Z" data-uid="VRageMath.Vector4D.Z">Z</h4>
  <div class="markdown level1 summary"><p>Gets or sets the z-component of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Z</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Vector4D_Zero" data-uid="VRageMath.Vector4D.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Vector4D_Add_" data-uid="VRageMath.Vector4D.Add*"></a>
  <h4 id="VRageMath_Vector4D_Add_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Add(VRageMath.Vector4D,VRageMath.Vector4D)">Add(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Add(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Add_" data-uid="VRageMath.Vector4D.Add*"></a>
  <h4 id="VRageMath_Vector4D_Add_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Add(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@)">Add(ref Vector4D, ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Add(ref Vector4D value1, ref Vector4D value2, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Sum of the source vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Barycentric_" data-uid="VRageMath.Vector4D.Barycentric*"></a>
  <h4 id="VRageMath_Vector4D_Barycentric_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_System_Double_System_Double_" data-uid="VRageMath.Vector4D.Barycentric(VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D,System.Double,System.Double)">Barycentric(Vector4D, Vector4D, Vector4D, Double, Double)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector4 containing the 4D Cartesian coordinates of a point specified in barycentric (areal) coordinates relative to a 4D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Barycentric(Vector4D value1, Vector4D value2, Vector4D value3, double amount1, double amount2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Barycentric_" data-uid="VRageMath.Vector4D.Barycentric*"></a>
  <h4 id="VRageMath_Vector4D_Barycentric_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__System_Double_System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Barycentric(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,System.Double,System.Double,VRageMath.Vector4D@)">Barycentric(ref Vector4D, ref Vector4D, ref Vector4D, Double, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Returns a Vector4 containing the 4D Cartesian coordinates of a point specified in Barycentric (areal) coordinates relative to a 4D triangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Barycentric(ref Vector4D value1, ref Vector4D value2, ref Vector4D value3, double amount1, double amount2, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 1 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 2 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>A Vector4 containing the 4D Cartesian coordinates of vertex 3 of the triangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount1</span></td>
        <td><p>Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in value2).</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount2</span></td>
        <td><p>Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in value3).</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The 4D Cartesian coordinates of the specified point are placed in this Vector4 on exit.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_CatmullRom_" data-uid="VRageMath.Vector4D.CatmullRom*"></a>
  <h4 id="VRageMath_Vector4D_CatmullRom_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.CatmullRom(VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D,System.Double)">CatmullRom(Vector4D, Vector4D, Vector4D, Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D CatmullRom(Vector4D value1, Vector4D value2, Vector4D value3, Vector4D value4, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_CatmullRom_" data-uid="VRageMath.Vector4D.CatmullRom*"></a>
  <h4 id="VRageMath_Vector4D_CatmullRom_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.CatmullRom(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">CatmullRom(ref Vector4D, ref Vector4D, ref Vector4D, ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Performs a Catmull-Rom interpolation using the specified positions.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void CatmullRom(ref Vector4D value1, ref Vector4D value2, ref Vector4D value3, ref Vector4D value4, double amount, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value3</span></td>
        <td><p>The third position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value4</span></td>
        <td><p>The fourth position in the interpolation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] A vector that is the result of the Catmull-Rom interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Clamp_" data-uid="VRageMath.Vector4D.Clamp*"></a>
  <h4 id="VRageMath_Vector4D_Clamp_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Clamp(VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D)">Clamp(Vector4D, Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Clamp(Vector4D value1, Vector4D min, Vector4D max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Clamp_" data-uid="VRageMath.Vector4D.Clamp*"></a>
  <h4 id="VRageMath_Vector4D_Clamp_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Clamp(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@)">Clamp(ref Vector4D, ref Vector4D, ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Restricts a value to be within a specified range.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Clamp(ref Vector4D value1, ref Vector4D min, ref Vector4D max, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The value to clamp.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The clamped value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Distance_" data-uid="VRageMath.Vector4D.Distance*"></a>
  <h4 id="VRageMath_Vector4D_Distance_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Distance(VRageMath.Vector4,VRageMath.Vector4)">Distance(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Distance(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Distance_" data-uid="VRageMath.Vector4D.Distance*"></a>
  <h4 id="VRageMath_Vector4D_Distance_VRageMath_Vector4__VRageMath_Vector4__System_Double__" data-uid="VRageMath.Vector4D.Distance(VRageMath.Vector4@,VRageMath.Vector4@,System.Double@)">Distance(ref Vector4, ref Vector4, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Distance(ref Vector4 value1, ref Vector4 value2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_DistanceSquared_" data-uid="VRageMath.Vector4D.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector4D_DistanceSquared_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.DistanceSquared(VRageMath.Vector4,VRageMath.Vector4)">DistanceSquared(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double DistanceSquared(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_DistanceSquared_" data-uid="VRageMath.Vector4D.DistanceSquared*"></a>
  <h4 id="VRageMath_Vector4D_DistanceSquared_VRageMath_Vector4__VRageMath_Vector4__System_Double__" data-uid="VRageMath.Vector4D.DistanceSquared(VRageMath.Vector4@,VRageMath.Vector4@,System.Double@)">DistanceSquared(ref Vector4, ref Vector4, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the distance between two vectors squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void DistanceSquared(ref Vector4 value1, ref Vector4 value2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The distance between the two vectors squared.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Divide_" data-uid="VRageMath.Vector4D.Divide*"></a>
  <h4 id="VRageMath_Vector4D_Divide_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.Divide(VRageMath.Vector4D,System.Double)">Divide(Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Divide(Vector4D value1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Divide_" data-uid="VRageMath.Vector4D.Divide*"></a>
  <h4 id="VRageMath_Vector4D_Divide_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Divide(VRageMath.Vector4D,VRageMath.Vector4D)">Divide(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Divide(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Divide_" data-uid="VRageMath.Vector4D.Divide*"></a>
  <h4 id="VRageMath_Vector4D_Divide_VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Divide(VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">Divide(ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector4D value1, double divider, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Divide_" data-uid="VRageMath.Vector4D.Divide*"></a>
  <h4 id="VRageMath_Vector4D_Divide_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Divide(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@)">Divide(ref Vector4D, ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Divide(ref Vector4D value1, ref Vector4D value2, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the division.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Dot_" data-uid="VRageMath.Vector4D.Dot*"></a>
  <h4 id="VRageMath_Vector4D_Dot_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Dot(VRageMath.Vector4,VRageMath.Vector4)">Dot(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static double Dot(Vector4 vector1, Vector4 vector2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Dot_" data-uid="VRageMath.Vector4D.Dot*"></a>
  <h4 id="VRageMath_Vector4D_Dot_VRageMath_Vector4__VRageMath_Vector4__System_Double__" data-uid="VRageMath.Vector4D.Dot(VRageMath.Vector4@,VRageMath.Vector4@,System.Double@)">Dot(ref Vector4, ref Vector4, out Double)</h4>
  <div class="markdown level1 summary"><p>Calculates the dot product of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Dot(ref Vector4 vector1, ref Vector4 vector2, out double result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">vector2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The dot product of the two vectors.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Equals_" data-uid="VRageMath.Vector4D.Equals*"></a>
  <h4 id="VRageMath_Vector4D_Equals_System_Object_" data-uid="VRageMath.Vector4D.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object with which to make the comparison.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Equals_" data-uid="VRageMath.Vector4D.Equals*"></a>
  <h4 id="VRageMath_Vector4D_Equals_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Equals(VRageMath.Vector4)">Equals(Vector4)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Vector4 other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Vector4 to compare with the current Vector4.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_GetHashCode_" data-uid="VRageMath.Vector4D.GetHashCode*"></a>
  <h4 id="VRageMath_Vector4D_GetHashCode" data-uid="VRageMath.Vector4D.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code of this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Hermite_" data-uid="VRageMath.Vector4D.Hermite*"></a>
  <h4 id="VRageMath_Vector4D_Hermite_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.Hermite(VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D,VRageMath.Vector4D,System.Double)">Hermite(Vector4D, Vector4D, Vector4D, Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Hermite(Vector4D value1, Vector4D tangent1, Vector4D value2, Vector4D tangent2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Hermite_" data-uid="VRageMath.Vector4D.Hermite*"></a>
  <h4 id="VRageMath_Vector4D_Hermite_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Hermite(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">Hermite(ref Vector4D, ref Vector4D, ref Vector4D, ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Performs a Hermite spline interpolation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Hermite(ref Vector4D value1, ref Vector4D tangent1, ref Vector4D value2, ref Vector4D tangent2, double amount, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">tangent1</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source position vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">tangent2</span></td>
        <td><p>Source tangent vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the Hermite spline interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Length_" data-uid="VRageMath.Vector4D.Length*"></a>
  <h4 id="VRageMath_Vector4D_Length" data-uid="VRageMath.Vector4D.Length">Length()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Length()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_LengthSquared_" data-uid="VRageMath.Vector4D.LengthSquared*"></a>
  <h4 id="VRageMath_Vector4D_LengthSquared" data-uid="VRageMath.Vector4D.LengthSquared">LengthSquared()</h4>
  <div class="markdown level1 summary"><p>Calculates the length of the vector squared.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double LengthSquared()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Lerp_" data-uid="VRageMath.Vector4D.Lerp*"></a>
  <h4 id="VRageMath_Vector4D_Lerp_VRageMath_Vector4D_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.Lerp(VRageMath.Vector4D,VRageMath.Vector4D,System.Double)">Lerp(Vector4D, Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Lerp(Vector4D value1, Vector4D value2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Lerp_" data-uid="VRageMath.Vector4D.Lerp*"></a>
  <h4 id="VRageMath_Vector4D_Lerp_VRageMath_Vector4D__VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Lerp(VRageMath.Vector4D@,VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">Lerp(ref Vector4D, ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Performs a linear interpolation between two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Lerp(ref Vector4D value1, ref Vector4D value2, double amount, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Value between 0 and 1 indicating the weight of value2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the interpolation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Max_" data-uid="VRageMath.Vector4D.Max*"></a>
  <h4 id="VRageMath_Vector4D_Max_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Max(VRageMath.Vector4,VRageMath.Vector4)">Max(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Max(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Max_" data-uid="VRageMath.Vector4D.Max*"></a>
  <h4 id="VRageMath_Vector4D_Max_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4D.Max(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Max(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the highest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Max(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The maximized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Min_" data-uid="VRageMath.Vector4D.Min*"></a>
  <h4 id="VRageMath_Vector4D_Min_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Min(VRageMath.Vector4,VRageMath.Vector4)">Min(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Min(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Min_" data-uid="VRageMath.Vector4D.Min*"></a>
  <h4 id="VRageMath_Vector4D_Min_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4D.Min(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Min(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Returns a vector that contains the lowest value from each matching pair of components.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Min(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The minimized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Multiply_" data-uid="VRageMath.Vector4D.Multiply*"></a>
  <h4 id="VRageMath_Vector4D_Multiply_VRageMath_Vector4__VRageMath_Vector4__VRageMath_Vector4__" data-uid="VRageMath.Vector4D.Multiply(VRageMath.Vector4@,VRageMath.Vector4@,VRageMath.Vector4@)">Multiply(ref Vector4, ref Vector4, out Vector4)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector4 value1, ref Vector4 value2, out Vector4 result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Multiply_" data-uid="VRageMath.Vector4D.Multiply*"></a>
  <h4 id="VRageMath_Vector4D_Multiply_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.Multiply(VRageMath.Vector4D,System.Double)">Multiply(Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Multiply(Vector4D value1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Multiply_" data-uid="VRageMath.Vector4D.Multiply*"></a>
  <h4 id="VRageMath_Vector4D_Multiply_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Multiply(VRageMath.Vector4D,VRageMath.Vector4D)">Multiply(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Multiply(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Multiply_" data-uid="VRageMath.Vector4D.Multiply*"></a>
  <h4 id="VRageMath_Vector4D_Multiply_VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Multiply(VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">Multiply(ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Multiply(ref Vector4D value1, double scaleFactor, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the multiplication.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Negate_" data-uid="VRageMath.Vector4D.Negate*"></a>
  <h4 id="VRageMath_Vector4D_Negate_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Negate(VRageMath.Vector4D)">Negate(Vector4D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Negate(Vector4D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Negate_" data-uid="VRageMath.Vector4D.Negate*"></a>
  <h4 id="VRageMath_Vector4D_Negate_VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Negate(VRageMath.Vector4D@,VRageMath.Vector4D@)">Negate(ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Negate(ref Vector4D value, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] Vector pointing in the opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Normalize_" data-uid="VRageMath.Vector4D.Normalize*"></a>
  <h4 id="VRageMath_Vector4D_Normalize" data-uid="VRageMath.Vector4D.Normalize">Normalize()</h4>
  <div class="markdown level1 summary"><p>Turns the current vector into a unit vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Normalize()</code></pre>
  </div>
  
  
  <a id="VRageMath_Vector4D_Normalize_" data-uid="VRageMath.Vector4D.Normalize*"></a>
  <h4 id="VRageMath_Vector4D_Normalize_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.Normalize(VRageMath.Vector4D)">Normalize(Vector4D)</h4>
  <div class="markdown level1 summary"><p>Creates a unit vector from the specified vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Normalize(Vector4D vector)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Normalize_" data-uid="VRageMath.Vector4D.Normalize*"></a>
  <h4 id="VRageMath_Vector4D_Normalize_VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Normalize(VRageMath.Vector4D@,VRageMath.Vector4D@)">Normalize(ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Returns a normalized version of the specified vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Normalize(ref Vector4D vector, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The normalized vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_PackOrthoMatrix_" data-uid="VRageMath.Vector4D.PackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4D_PackOrthoMatrix_VRageMath_MatrixD__" data-uid="VRageMath.Vector4D.PackOrthoMatrix(VRageMath.MatrixD@)">PackOrthoMatrix(ref MatrixD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D PackOrthoMatrix(ref MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_PackOrthoMatrix_" data-uid="VRageMath.Vector4D.PackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4D_PackOrthoMatrix_VRageMath_Vector3D_VRageMath_Vector3_VRageMath_Vector3_" data-uid="VRageMath.Vector4D.PackOrthoMatrix(VRageMath.Vector3D,VRageMath.Vector3,VRageMath.Vector3)">PackOrthoMatrix(Vector3D, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D PackOrthoMatrix(Vector3D position, Vector3 forward, Vector3 up)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">forward</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">up</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_SmoothStep_" data-uid="VRageMath.Vector4D.SmoothStep*"></a>
  <h4 id="VRageMath_Vector4D_SmoothStep_VRageMath_Vector4D_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.SmoothStep(VRageMath.Vector4D,VRageMath.Vector4D,System.Double)">SmoothStep(Vector4D, Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D SmoothStep(Vector4D value1, Vector4D value2, double amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_SmoothStep_" data-uid="VRageMath.Vector4D.SmoothStep*"></a>
  <h4 id="VRageMath_Vector4D_SmoothStep_VRageMath_Vector4D__VRageMath_Vector4D__System_Double_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.SmoothStep(VRageMath.Vector4D@,VRageMath.Vector4D@,System.Double,VRageMath.Vector4D@)">SmoothStep(ref Vector4D, ref Vector4D, Double, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Interpolates between two values using a cubic equation.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void SmoothStep(ref Vector4D value1, ref Vector4D value2, double amount, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">amount</span></td>
        <td><p>Weighting factor.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The interpolated value.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Subtract_" data-uid="VRageMath.Vector4D.Subtract*"></a>
  <h4 id="VRageMath_Vector4D_Subtract_VRageMath_Vector4_VRageMath_Vector4_" data-uid="VRageMath.Vector4D.Subtract(VRageMath.Vector4,VRageMath.Vector4)">Subtract(Vector4, Vector4)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4 Subtract(Vector4 value1, Vector4 value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Subtract_" data-uid="VRageMath.Vector4D.Subtract*"></a>
  <h4 id="VRageMath_Vector4D_Subtract_VRageMath_Vector4D__VRageMath_Vector4D__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Subtract(VRageMath.Vector4D@,VRageMath.Vector4D@,VRageMath.Vector4D@)">Subtract(ref Vector4D, ref Vector4D, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Subtract(ref Vector4D value1, ref Vector4D value2, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The result of the subtraction.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_ToString_" data-uid="VRageMath.Vector4D.ToString*"></a>
  <h4 id="VRageMath_Vector4D_ToString" data-uid="VRageMath.Vector4D.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector2_VRageMath_MatrixD_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector2,VRageMath.MatrixD)">Transform(Vector2, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector2 position, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector2_VRageMath_Quaternion_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector2,VRageMath.Quaternion)">Transform(Vector2, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector2 value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector2 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector2__VRageMath_MatrixD__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector2@,VRageMath.MatrixD@,VRageMath.Vector4D@)">Transform(ref Vector2, ref MatrixD, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 position, ref MatrixD matrix, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector2.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector2__VRageMath_Quaternion__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector2@,VRageMath.Quaternion@,VRageMath.Vector4D@)">Transform(ref Vector2, ref Quaternion, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector2 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector2 value, ref Quaternion rotation, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector2.html">Vector2</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector2 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector3D_VRageMath_MatrixD_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector3D,VRageMath.MatrixD)">Transform(Vector3D, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector3D position, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector3D_VRageMath_Quaternion_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector3D,VRageMath.Quaternion)">Transform(Vector3D, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector3D value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector3D__VRageMath_MatrixD__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector3D@,VRageMath.MatrixD@,VRageMath.Vector4D@)">Transform(ref Vector3D, ref MatrixD, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3D position, ref MatrixD matrix, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">position</span></td>
        <td><p>The source Vector3.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector3D__VRageMath_Quaternion__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector3D@,VRageMath.Quaternion@,VRageMath.Vector4D@)">Transform(ref Vector3D, ref Quaternion, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector3 by a specified Quaternion into a Vector4.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector3D value, ref Quaternion rotation, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector3 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D_VRageMath_MatrixD_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D,VRageMath.MatrixD)">Transform(Vector4D, MatrixD)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by the specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector4D vector, MatrixD matrix)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D_VRageMath_Quaternion_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D,VRageMath.Quaternion)">Transform(Vector4D, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D Transform(Vector4D value, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D__VRageMath_MatrixD__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D@,VRageMath.MatrixD@,VRageMath.Vector4D@)">Transform(ref Vector4D, ref MatrixD, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by the given Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector4D vector, ref MatrixD matrix, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">vector</span></td>
        <td><p>The source Vector4.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transformation Matrix.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D__VRageMath_Quaternion__VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D@,VRageMath.Quaternion@,VRageMath.Vector4D@)">Transform(ref Vector4D, ref Quaternion, out Vector4D)</h4>
  <div class="markdown level1 summary"><p>Transforms a Vector4 by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(ref Vector4D value, ref Quaternion rotation, out Vector4D result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Vector4 resulting from the transformation.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D___System_Int32_VRageMath_MatrixD__VRageMath_Vector4D___System_Int32_System_Int32_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D[],System.Int32,VRageMath.MatrixD@,VRageMath.Vector4D[],System.Int32,System.Int32)">Transform(Vector4D[], Int32, ref MatrixD, Vector4D[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector4s by a specified Matrix into a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4D[] sourceArray, int sourceIndex, ref MatrixD matrix, Vector4D[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s containing the range to transform.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array of the first Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array of Vector4s into which to write the results.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array of the first result Vector4 to write.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector4s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D___System_Int32_VRageMath_Quaternion__VRageMath_Vector4D___System_Int32_System_Int32_" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D[],System.Int32,VRageMath.Quaternion@,VRageMath.Vector4D[],System.Int32,System.Int32)">Transform(Vector4D[], Int32, ref Quaternion, Vector4D[], Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Transforms a specified range in an array of Vector4s by a specified Quaternion into a specified range in a destination array.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4D[] sourceArray, int sourceIndex, ref Quaternion rotation, Vector4D[] destinationArray, int destinationIndex, int length)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s containing the range to transform.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">sourceIndex</span></td>
        <td><p>The index in the source array of the first Vector4 to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array of Vector4s into which to write the results.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">destinationIndex</span></td>
        <td><p>The index in the destination array of the first result Vector4 to write.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">length</span></td>
        <td><p>The number of Vector4s to transform.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D___VRageMath_MatrixD__VRageMath_Vector4D___" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D[],VRageMath.MatrixD@,VRageMath.Vector4D[])">Transform(Vector4D[], ref MatrixD, Vector4D[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector4s by a specified Matrix.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4D[] sourceArray, ref MatrixD matrix, Vector4D[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td><span class="parametername">matrix</span></td>
        <td><p>The transform Matrix to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array into which the transformed Vector4s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_Transform_" data-uid="VRageMath.Vector4D.Transform*"></a>
  <h4 id="VRageMath_Vector4D_Transform_VRageMath_Vector4D___VRageMath_Quaternion__VRageMath_Vector4D___" data-uid="VRageMath.Vector4D.Transform(VRageMath.Vector4D[],VRageMath.Quaternion@,VRageMath.Vector4D[])">Transform(Vector4D[], ref Quaternion, Vector4D[])</h4>
  <div class="markdown level1 summary"><p>Transforms an array of Vector4s by a specified Quaternion.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Transform(Vector4D[] sourceArray, ref Quaternion rotation, Vector4D[] destinationArray)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">sourceArray</span></td>
        <td><p>The array of Vector4s to transform.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Quaternion.html">Quaternion</a></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The Quaternion rotation to apply.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a>[]</td>
        <td><span class="parametername">destinationArray</span></td>
        <td><p>The existing destination array into which the transformed Vector4s are written.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_UnpackOrthoMatrix_" data-uid="VRageMath.Vector4D.UnpackOrthoMatrix*"></a>
  <h4 id="VRageMath_Vector4D_UnpackOrthoMatrix_VRageMath_Vector4D__" data-uid="VRageMath.Vector4D.UnpackOrthoMatrix(VRageMath.Vector4D@)">UnpackOrthoMatrix(ref Vector4D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MatrixD UnpackOrthoMatrix(ref Vector4D packed)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">packed</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.MatrixD.html">MatrixD</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Vector4D_op_Addition_" data-uid="VRageMath.Vector4D.op_Addition*"></a>
  <h4 id="VRageMath_Vector4D_op_Addition_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Addition(VRageMath.Vector4D,VRageMath.Vector4D)">Addition(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Adds two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator +(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Division_" data-uid="VRageMath.Vector4D.op_Division*"></a>
  <h4 id="VRageMath_Vector4D_op_Division_System_Double_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Division(System.Double,VRageMath.Vector4D)">Division(Double, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator /(double lhs, Vector4D rhs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">lhs</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">rhs</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Division_" data-uid="VRageMath.Vector4D.op_Division*"></a>
  <h4 id="VRageMath_Vector4D_op_Division_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.op_Division(VRageMath.Vector4D,System.Double)">Division(Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Divides a vector by a scalar value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator /(Vector4D value1, double divider)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">divider</span></td>
        <td><p>The divisor.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Division_" data-uid="VRageMath.Vector4D.op_Division*"></a>
  <h4 id="VRageMath_Vector4D_op_Division_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Division(VRageMath.Vector4D,VRageMath.Vector4D)">Division(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Divides the components of a vector by the components of another vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator /(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Equality_" data-uid="VRageMath.Vector4D.op_Equality*"></a>
  <h4 id="VRageMath_Vector4D_op_Equality_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Equality(VRageMath.Vector4D,VRageMath.Vector4D)">Equality(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Implicit_" data-uid="VRageMath.Vector4D.op_Implicit*"></a>
  <h4 id="VRageMath_Vector4D_op_Implicit_VRageMath_Vector4__VRageMath_Vector4D" data-uid="VRageMath.Vector4D.op_Implicit(VRageMath.Vector4)~VRageMath.Vector4D">Implicit(Vector4 to Vector4D)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Vector4D(Vector4 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Implicit_" data-uid="VRageMath.Vector4D.op_Implicit*"></a>
  <h4 id="VRageMath_Vector4D_op_Implicit_VRageMath_Vector4D__VRageMath_Vector4" data-uid="VRageMath.Vector4D.op_Implicit(VRageMath.Vector4D)~VRageMath.Vector4">Implicit(Vector4D to Vector4)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static implicit operator Vector4(Vector4D v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">v</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4.html">Vector4</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Inequality_" data-uid="VRageMath.Vector4D.op_Inequality*"></a>
  <h4 id="VRageMath_Vector4D_op_Inequality_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Inequality(VRageMath.Vector4D,VRageMath.Vector4D)">Inequality(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Tests vectors for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Vector to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Multiply_" data-uid="VRageMath.Vector4D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4D_op_Multiply_System_Double_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Multiply(System.Double,VRageMath.Vector4D)">Multiply(Double, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator *(double scaleFactor, Vector4D value1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Multiply_" data-uid="VRageMath.Vector4D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4D_op_Multiply_VRageMath_Vector4D_System_Double_" data-uid="VRageMath.Vector4D.op_Multiply(VRageMath.Vector4D,System.Double)">Multiply(Vector4D, Double)</h4>
  <div class="markdown level1 summary"><p>Multiplies a vector by a scalar.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator *(Vector4D value1, double scaleFactor)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">scaleFactor</span></td>
        <td><p>Scalar value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Multiply_" data-uid="VRageMath.Vector4D.op_Multiply*"></a>
  <h4 id="VRageMath_Vector4D_op_Multiply_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Multiply(VRageMath.Vector4D,VRageMath.Vector4D)">Multiply(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Multiplies the components of two vectors by each other.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator *(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_Subtraction_" data-uid="VRageMath.Vector4D.op_Subtraction*"></a>
  <h4 id="VRageMath_Vector4D_op_Subtraction_VRageMath_Vector4D_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_Subtraction(VRageMath.Vector4D,VRageMath.Vector4D)">Subtraction(Vector4D, Vector4D)</h4>
  <div class="markdown level1 summary"><p>Subtracts a vector from a vector.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator -(Vector4D value1, Vector4D value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Vector4D_op_UnaryNegation_" data-uid="VRageMath.Vector4D.op_UnaryNegation*"></a>
  <h4 id="VRageMath_Vector4D_op_UnaryNegation_VRageMath_Vector4D_" data-uid="VRageMath.Vector4D.op_UnaryNegation(VRageMath.Vector4D)">UnaryNegation(Vector4D)</h4>
  <div class="markdown level1 summary"><p>Returns a vector pointing in the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector4D operator -(Vector4D value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>Source vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector4D.html">Vector4D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
