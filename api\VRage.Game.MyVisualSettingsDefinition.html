﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyVisualSettingsDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyVisualSettingsDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.MyVisualSettingsDefinition">
  
  
  <h1 id="VRage_Game_MyVisualSettingsDefinition" data-uid="VRage.Game.MyVisualSettingsDefinition" class="text-break">Class MyVisualSettingsDefinition
  </h1>
  <div class="markdown level0 summary"><p>Stripped environment definition with only visual settings</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.Game.MyDefinitionBase.html">MyDefinitionBase</a></div>
    <div class="level2"><span class="xref">MyVisualSettingsDefinition</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Id">MyDefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameEnum">MyDefinitionBase.DisplayNameEnum</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionEnum">MyDefinitionBase.DescriptionEnum</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameString">MyDefinitionBase.DisplayNameString</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionString">MyDefinitionBase.DescriptionString</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionArgs">MyDefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Icons">MyDefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Enabled">MyDefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Public">MyDefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_AvailableInSurvival">MyDefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Context">MyDefinitionBase.Context</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Init_VRage_Game_MyObjectBuilder_DefinitionBase_VRage_Game_MyModContext_">MyDefinitionBase.Init(MyObjectBuilder_DefinitionBase, MyModContext)</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Postprocess">MyDefinitionBase.Postprocess()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_ToString">MyDefinitionBase.ToString()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_CheckDefinitionDLCs_System_String___">MyDefinitionBase.CheckDefinitionDLCs(String[])</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DLCs">MyDefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DisplayNameText">MyDefinitionBase.DisplayNameText</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_DescriptionText">MyDefinitionBase.DescriptionText</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.html">VRage.Game</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_MyVisualSettingsDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyDefinitionType]
public class MyVisualSettingsDefinition : MyDefinitionBase</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_MyVisualSettingsDefinition__ctor_" data-uid="VRage.Game.MyVisualSettingsDefinition.#ctor*"></a>
  <h4 id="VRage_Game_MyVisualSettingsDefinition__ctor" data-uid="VRage.Game.MyVisualSettingsDefinition.#ctor">MyVisualSettingsDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyVisualSettingsDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_MyVisualSettingsDefinition_FogProperties" data-uid="VRage.Game.MyVisualSettingsDefinition.FogProperties">FogProperties</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyFogProperties FogProperties</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyFogProperties.html">MyFogProperties</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVisualSettingsDefinition_PostProcessSettings" data-uid="VRage.Game.MyVisualSettingsDefinition.PostProcessSettings">PostProcessSettings</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyPostprocessSettings PostProcessSettings</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyPostprocessSettings</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_MyVisualSettingsDefinition_SunProperties" data-uid="VRage.Game.MyVisualSettingsDefinition.SunProperties">SunProperties</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MySunProperties SunProperties</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MySunProperties.html">MySunProperties</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Game_MyVisualSettingsDefinition_ShadowSettings_" data-uid="VRage.Game.MyVisualSettingsDefinition.ShadowSettings*"></a>
  <h4 id="VRage_Game_MyVisualSettingsDefinition_ShadowSettings" data-uid="VRage.Game.MyVisualSettingsDefinition.ShadowSettings">ShadowSettings</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyShadowsSettings ShadowSettings { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">VRageRender.MyShadowsSettings</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Game_MyVisualSettingsDefinition_GetObjectBuilder_" data-uid="VRage.Game.MyVisualSettingsDefinition.GetObjectBuilder*"></a>
  <h4 id="VRage_Game_MyVisualSettingsDefinition_GetObjectBuilder" data-uid="VRage.Game.MyVisualSettingsDefinition.GetObjectBuilder">GetObjectBuilder()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override MyObjectBuilder_DefinitionBase GetObjectBuilder()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_GetObjectBuilder">MyDefinitionBase.GetObjectBuilder()</a></div>
  
  
  <a id="VRage_Game_MyVisualSettingsDefinition_Init_" data-uid="VRage.Game.MyVisualSettingsDefinition.Init*"></a>
  <h4 id="VRage_Game_MyVisualSettingsDefinition_Init_VRage_Game_MyObjectBuilder_DefinitionBase_" data-uid="VRage.Game.MyVisualSettingsDefinition.Init(VRage.Game.MyObjectBuilder_DefinitionBase)">Init(MyObjectBuilder_DefinitionBase)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Init(MyObjectBuilder_DefinitionBase builder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></td>
        <td><span class="parametername">builder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="VRage.Game.MyDefinitionBase.html#VRage_Game_MyDefinitionBase_Init_VRage_Game_MyObjectBuilder_DefinitionBase_">MyDefinitionBase.Init(MyObjectBuilder_DefinitionBase)</a></div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
