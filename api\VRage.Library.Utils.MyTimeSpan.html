﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyTimeSpan
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyTimeSpan
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Library.Utils.MyTimeSpan">
  
  
  <h1 id="VRage_Library_Utils_MyTimeSpan" data-uid="VRage.Library.Utils.MyTimeSpan" class="text-break">Class MyTimeSpan
  </h1>
  <div class="markdown level0 summary"><p>Hi-resolution time span. Beware: the resolution can be different on different systems!</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">MyTimeSpan</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Library.Utils.html">VRage.Library.Utils</a></h6>
  <h6><strong>Assembly</strong>: VRage.Library.dll</h6>
  <h5 id="VRage_Library_Utils_MyTimeSpan_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class MyTimeSpan : ValueType</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan__ctor_" data-uid="VRage.Library.Utils.MyTimeSpan.#ctor*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan__ctor_System_Int64_" data-uid="VRage.Library.Utils.MyTimeSpan.#ctor(System.Int64)">MyTimeSpan(Int64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyTimeSpan(long stopwatchTicks)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">stopwatchTicks</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Library_Utils_MyTimeSpan_MaxValue" data-uid="VRage.Library.Utils.MyTimeSpan.MaxValue">MaxValue</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly MyTimeSpan MaxValue</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Library_Utils_MyTimeSpan_Ticks" data-uid="VRage.Library.Utils.MyTimeSpan.Ticks">Ticks</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public readonly long Ticks</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Library_Utils_MyTimeSpan_Zero" data-uid="VRage.Library.Utils.MyTimeSpan.Zero">Zero</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly MyTimeSpan Zero</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Microseconds_" data-uid="VRage.Library.Utils.MyTimeSpan.Microseconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Microseconds" data-uid="VRage.Library.Utils.MyTimeSpan.Microseconds">Microseconds</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Microseconds { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Milliseconds_" data-uid="VRage.Library.Utils.MyTimeSpan.Milliseconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Milliseconds" data-uid="VRage.Library.Utils.MyTimeSpan.Milliseconds">Milliseconds</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Milliseconds { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Minutes_" data-uid="VRage.Library.Utils.MyTimeSpan.Minutes*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Minutes" data-uid="VRage.Library.Utils.MyTimeSpan.Minutes">Minutes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Minutes { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Nanoseconds_" data-uid="VRage.Library.Utils.MyTimeSpan.Nanoseconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Nanoseconds" data-uid="VRage.Library.Utils.MyTimeSpan.Nanoseconds">Nanoseconds</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Nanoseconds { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Seconds_" data-uid="VRage.Library.Utils.MyTimeSpan.Seconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Seconds" data-uid="VRage.Library.Utils.MyTimeSpan.Seconds">Seconds</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Seconds { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_TimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.TimeSpan*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_TimeSpan" data-uid="VRage.Library.Utils.MyTimeSpan.TimeSpan">TimeSpan</h4>
  <div class="markdown level1 summary"><p>This may not be accurate for large values - double accuracy</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TimeSpan TimeSpan { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.TimeSpan</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_Equals_" data-uid="VRage.Library.Utils.MyTimeSpan.Equals*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_Equals_System_Object_" data-uid="VRage.Library.Utils.MyTimeSpan.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_FromMilliseconds_" data-uid="VRage.Library.Utils.MyTimeSpan.FromMilliseconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_FromMilliseconds_System_Double_" data-uid="VRage.Library.Utils.MyTimeSpan.FromMilliseconds(System.Double)">FromMilliseconds(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan FromMilliseconds(double milliseconds)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">milliseconds</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_FromMinutes_" data-uid="VRage.Library.Utils.MyTimeSpan.FromMinutes*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_FromMinutes_System_Double_" data-uid="VRage.Library.Utils.MyTimeSpan.FromMinutes(System.Double)">FromMinutes(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan FromMinutes(double minutes)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">minutes</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_FromSeconds_" data-uid="VRage.Library.Utils.MyTimeSpan.FromSeconds*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_FromSeconds_System_Double_" data-uid="VRage.Library.Utils.MyTimeSpan.FromSeconds(System.Double)">FromSeconds(Double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan FromSeconds(double seconds)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Double</span></td>
        <td><span class="parametername">seconds</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_FromTicks_" data-uid="VRage.Library.Utils.MyTimeSpan.FromTicks*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_FromTicks_System_Int64_" data-uid="VRage.Library.Utils.MyTimeSpan.FromTicks(System.Int64)">FromTicks(Int64)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan FromTicks(long ticks)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">ticks</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_GetHashCode_" data-uid="VRage.Library.Utils.MyTimeSpan.GetHashCode*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_GetHashCode" data-uid="VRage.Library.Utils.MyTimeSpan.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_ToString_" data-uid="VRage.Library.Utils.MyTimeSpan.ToString*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_ToString" data-uid="VRage.Library.Utils.MyTimeSpan.ToString">ToString()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_Addition_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Addition*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_Addition_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Addition(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">Addition(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan operator +(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_Equality_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Equality*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_Equality_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Equality(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">Equality(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_GreaterThan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_GreaterThan*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_GreaterThan_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_GreaterThan(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">GreaterThan(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator&gt;(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_GreaterThanOrEqual_" data-uid="VRage.Library.Utils.MyTimeSpan.op_GreaterThanOrEqual*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_GreaterThanOrEqual_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_GreaterThanOrEqual(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">GreaterThanOrEqual(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator &gt;=(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_Inequality_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Inequality*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_Inequality_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Inequality(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">Inequality(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_LessThan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_LessThan*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_LessThan_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_LessThan(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">LessThan(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator &lt;(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_LessThanOrEqual_" data-uid="VRage.Library.Utils.MyTimeSpan.op_LessThanOrEqual*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_LessThanOrEqual_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_LessThanOrEqual(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">LessThanOrEqual(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator &lt;=(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_Library_Utils_MyTimeSpan_op_Subtraction_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Subtraction*"></a>
  <h4 id="VRage_Library_Utils_MyTimeSpan_op_Subtraction_VRage_Library_Utils_MyTimeSpan_VRage_Library_Utils_MyTimeSpan_" data-uid="VRage.Library.Utils.MyTimeSpan.op_Subtraction(VRage.Library.Utils.MyTimeSpan,VRage.Library.Utils.MyTimeSpan)">Subtraction(MyTimeSpan, MyTimeSpan)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static MyTimeSpan operator -(MyTimeSpan a, MyTimeSpan b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">a</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td><span class="parametername">b</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.Library.Utils.MyTimeSpan.html">MyTimeSpan</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
