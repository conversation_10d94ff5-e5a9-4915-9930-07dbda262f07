﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class MyObjectBuilder_ShipSoundsDefinition
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class MyObjectBuilder_ShipSoundsDefinition
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx ********">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition">
  
  
  <h1 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition" class="text-break">Class MyObjectBuilder_ShipSoundsDefinition
  </h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html">MyObjectBuilder_Base</a></div>
    <div class="level2"><a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html">MyObjectBuilder_DefinitionBase</a></div>
    <div class="level3"><span class="xref">MyObjectBuilder_ShipSoundsDefinition</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Id">MyObjectBuilder_DefinitionBase.Id</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DisplayName">MyObjectBuilder_DefinitionBase.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Description">MyObjectBuilder_DefinitionBase.Description</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Icons">MyObjectBuilder_DefinitionBase.Icons</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Public">MyObjectBuilder_DefinitionBase.Public</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_Enabled">MyObjectBuilder_DefinitionBase.Enabled</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_AvailableInSurvival">MyObjectBuilder_DefinitionBase.AvailableInSurvival</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DescriptionArgs">MyObjectBuilder_DefinitionBase.DescriptionArgs</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.MyObjectBuilder_DefinitionBase.html#VRage_Game_MyObjectBuilder_DefinitionBase_DLCs">MyObjectBuilder_DefinitionBase.DLCs</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_ShouldSerializeSubtypeId">MyObjectBuilder_Base.ShouldSerializeSubtypeId()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Clone">MyObjectBuilder_Base.Clone()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_Equals_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilder_Base.Equals(MyObjectBuilder_Base)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeId">MyObjectBuilder_Base.SubtypeId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_SubtypeName">MyObjectBuilder_Base.SubtypeName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_Base.html#VRage_ObjectBuilders_MyObjectBuilder_Base_TypeId">MyObjectBuilder_Base.TypeId</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.Game.ObjectBuilders.Definitions.html">VRage.Game.ObjectBuilders.Definitions</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MyObjectBuilderDefinition]
public class MyObjectBuilder_ShipSoundsDefinition : MyObjectBuilder_DefinitionBase</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition__ctor_" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.#ctor*"></a>
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition__ctor" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.#ctor">MyObjectBuilder_ShipSoundsDefinition()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public MyObjectBuilder_ShipSoundsDefinition()</code></pre>
  </div>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_AllowLargeGrid" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.AllowLargeGrid">AllowLargeGrid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AllowLargeGrid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_AllowSmallGrid" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.AllowSmallGrid">AllowSmallGrid</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AllowSmallGrid</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_EnginePitchRangeInSemitones" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.EnginePitchRangeInSemitones">EnginePitchRangeInSemitones</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float EnginePitchRangeInSemitones</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_EngineTimeToTurnOff" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.EngineTimeToTurnOff">EngineTimeToTurnOff</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float EngineTimeToTurnOff</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_EngineTimeToTurnOn" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.EngineTimeToTurnOn">EngineTimeToTurnOn</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float EngineTimeToTurnOn</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_EngineVolumes" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.EngineVolumes">EngineVolumes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;ShipSoundVolumePair&gt; EngineVolumes</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSoundVolumePair.html">ShipSoundVolumePair</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_MinWeight" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.MinWeight">MinWeight</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float MinWeight</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_Sounds" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.Sounds">Sounds</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;ShipSound&gt; Sounds</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSound.html">ShipSound</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_SpeedDownSoundChangeVolumeTo" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.SpeedDownSoundChangeVolumeTo">SpeedDownSoundChangeVolumeTo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SpeedDownSoundChangeVolumeTo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_SpeedUpDownChangeSpeed" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.SpeedUpDownChangeSpeed">SpeedUpDownChangeSpeed</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SpeedUpDownChangeSpeed</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_SpeedUpSoundChangeVolumeTo" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.SpeedUpSoundChangeVolumeTo">SpeedUpSoundChangeVolumeTo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SpeedUpSoundChangeVolumeTo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_ThrusterCompositionChangeSpeed" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.ThrusterCompositionChangeSpeed">ThrusterCompositionChangeSpeed</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ThrusterCompositionChangeSpeed</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_ThrusterCompositionMinVolume" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.ThrusterCompositionMinVolume">ThrusterCompositionMinVolume</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ThrusterCompositionMinVolume</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_ThrusterPitchRangeInSemitones" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.ThrusterPitchRangeInSemitones">ThrusterPitchRangeInSemitones</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ThrusterPitchRangeInSemitones</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_ThrusterVolumes" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.ThrusterVolumes">ThrusterVolumes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;ShipSoundVolumePair&gt; ThrusterVolumes</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSoundVolumePair.html">ShipSoundVolumePair</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_WheelsFullSpeed" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.WheelsFullSpeed">WheelsFullSpeed</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float WheelsFullSpeed</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_WheelsGroundMinVolume" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.WheelsGroundMinVolume">WheelsGroundMinVolume</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float WheelsGroundMinVolume</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_WheelsLowerThrusterVolumeBy" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.WheelsLowerThrusterVolumeBy">WheelsLowerThrusterVolumeBy</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float WheelsLowerThrusterVolumeBy</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_WheelsPitchRangeInSemitones" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.WheelsPitchRangeInSemitones">WheelsPitchRangeInSemitones</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float WheelsPitchRangeInSemitones</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_Game_ObjectBuilders_Definitions_MyObjectBuilder_ShipSoundsDefinition_WheelsVolumes" data-uid="VRage.Game.ObjectBuilders.Definitions.MyObjectBuilder_ShipSoundsDefinition.WheelsVolumes">WheelsVolumes</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;ShipSoundVolumePair&gt; WheelsVolumes</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.Game.ObjectBuilders.Definitions.ShipSoundVolumePair.html">ShipSoundVolumePair</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="VRage.Game.MyObjectBuilderExtensions.html#VRage_Game_MyObjectBuilderExtensions_GetId_VRage_ObjectBuilders_MyObjectBuilder_Base_">MyObjectBuilderExtensions.GetId(MyObjectBuilder_Base)</a>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
