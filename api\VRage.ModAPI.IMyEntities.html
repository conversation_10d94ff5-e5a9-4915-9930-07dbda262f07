﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyEntities
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyEntities
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyEntities">
  
  
  <h1 id="VRage_ModAPI_IMyEntities" data-uid="VRage.ModAPI.IMyEntities" class="text-break">Interface IMyEntities
  </h1>
  <div class="markdown level0 summary"><p>Provides API, that granting access to enitities (mods interface)</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyEntities_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyEntities</code></pre>
  </div>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyEntities_AddEntity_" data-uid="VRage.ModAPI.IMyEntities.AddEntity*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_AddEntity_VRage_ModAPI_IMyEntity_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.AddEntity(VRage.ModAPI.IMyEntity,System.Boolean)">AddEntity(IMyEntity, Boolean)</h4>
  <div class="markdown level1 summary"><p>Registers entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void AddEntity(IMyEntity entity, bool insertIntoScene = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity that should be registered</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">insertIntoScene</span></td>
        <td><p>When true <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnAddedToScene_System_Object_">OnAddedToScene(Object)</a> is called</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilder_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilder*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilder_VRage_ObjectBuilders_MyObjectBuilder_EntityBase_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilder(VRage.ObjectBuilders.MyObjectBuilder_EntityBase)">CreateFromObjectBuilder(MyObjectBuilder_EntityBase)</h4>
  <div class="markdown level1 summary"><p>Create entity from object builder</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity CreateFromObjectBuilder(MyObjectBuilder_EntityBase objectBuilder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>Object builder of entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Created entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderAndAdd_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderAndAdd*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderAndAdd_VRage_ObjectBuilders_MyObjectBuilder_EntityBase_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderAndAdd(VRage.ObjectBuilders.MyObjectBuilder_EntityBase)">CreateFromObjectBuilderAndAdd(MyObjectBuilder_EntityBase)</h4>
  <div class="markdown level1 summary"><p>Create entity from object builder, and then call <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_AddEntity_VRage_ModAPI_IMyEntity_System_Boolean_">AddEntity(IMyEntity, Boolean)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity CreateFromObjectBuilderAndAdd(MyObjectBuilder_EntityBase objectBuilder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>Object builder of entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Created entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderNoinit_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderNoinit*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderNoinit_VRage_ObjectBuilders_MyObjectBuilder_EntityBase_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderNoinit(VRage.ObjectBuilders.MyObjectBuilder_EntityBase)">CreateFromObjectBuilderNoinit(MyObjectBuilder_EntityBase)</h4>
  <div class="markdown level1 summary"><p>Create new entity from objectBuilder, but doesn't call <strong>Init(MyObjectBuilder_EntityBase objectBuilder)</strong></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity CreateFromObjectBuilderNoinit(MyObjectBuilder_EntityBase objectBuilder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderParallel_" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderParallel*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_CreateFromObjectBuilderParallel_VRage_ObjectBuilders_MyObjectBuilder_EntityBase_System_Boolean_System_Action_VRage_ModAPI_IMyEntity__" data-uid="VRage.ModAPI.IMyEntities.CreateFromObjectBuilderParallel(VRage.ObjectBuilders.MyObjectBuilder_EntityBase,System.Boolean,System.Action{VRage.ModAPI.IMyEntity})">CreateFromObjectBuilderParallel(MyObjectBuilder_EntityBase, Boolean, Action&lt;IMyEntity&gt;)</h4>
  <div class="markdown level1 summary"><p>Creates and asynchronously initializes and entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity CreateFromObjectBuilderParallel(MyObjectBuilder_EntityBase objectBuilder, bool addToScene = false, Action&lt;IMyEntity&gt; completionCallback = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>Object builder of grid</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">addToScene</span></td>
        <td><p>Call <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_AddEntity_VRage_ModAPI_IMyEntity_System_Boolean_">AddEntity(IMyEntity, Boolean)</a> and call OnAddedToScene</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><span class="parametername">completionCallback</span></td>
        <td><p>Callback called in main thread.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Create <strong>but not inited yet</strong> entity. Entity would be inited correctly after callback trigger</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_EnableEntityBoundingBoxDraw_" data-uid="VRage.ModAPI.IMyEntities.EnableEntityBoundingBoxDraw*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_EnableEntityBoundingBoxDraw_VRage_ModAPI_IMyEntity_System_Boolean_System_Nullable_VRageMath_Vector4__System_Single_System_Nullable_VRageMath_Vector3__" data-uid="VRage.ModAPI.IMyEntities.EnableEntityBoundingBoxDraw(VRage.ModAPI.IMyEntity,System.Boolean,System.Nullable{VRageMath.Vector4},System.Single,System.Nullable{VRageMath.Vector3})">EnableEntityBoundingBoxDraw(IMyEntity, Boolean, Nullable&lt;Vector4&gt;, Single, Nullable&lt;Vector3&gt;)</h4>
  <div class="markdown level1 summary"><p>Draw bounding box around entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void EnableEntityBoundingBoxDraw(IMyEntity entity, bool enable, Nullable&lt;Vector4&gt; color = null, float lineWidth = 0.01F, Nullable&lt;Vector3&gt; inflateAmount = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>That should have visible bounding box</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">enable</span></td>
        <td><p>When true, bounding box start draw around entity</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector4.html">Vector4</a>&gt;</td>
        <td><span class="parametername">color</span></td>
        <td><p>Color of lines</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">lineWidth</span></td>
        <td><p>With of lines</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3.html">Vector3</a>&gt;</td>
        <td><span class="parametername">inflateAmount</span></td>
        <td><p>Distance from original bounding box, from each side in meters</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_EntityExists_" data-uid="VRage.ModAPI.IMyEntities.EntityExists*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_EntityExists_System_Int64_" data-uid="VRage.ModAPI.IMyEntities.EntityExists(System.Int64)">EntityExists(Int64)</h4>
  <div class="markdown level1 summary"><p>Returns if entity with provided name exists</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool EntityExists(long entityId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">entityId</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity exists</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_EntityExists_" data-uid="VRage.ModAPI.IMyEntities.EntityExists*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_EntityExists_System_Nullable_System_Int64__" data-uid="VRage.ModAPI.IMyEntities.EntityExists(System.Nullable{System.Int64})">EntityExists(Nullable&lt;Int64&gt;)</h4>
  <div class="markdown level1 summary"><p>Returns if entity with provided name exists</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool EntityExists(Nullable&lt;long&gt; entityId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int64</span>&gt;</td>
        <td><span class="parametername">entityId</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity exists</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_EntityExists_" data-uid="VRage.ModAPI.IMyEntities.EntityExists*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_EntityExists_System_String_" data-uid="VRage.ModAPI.IMyEntities.EntityExists(System.String)">EntityExists(String)</h4>
  <div class="markdown level1 summary"><p>Returns if entity with provided name exists</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool EntityExists(string name)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity exists</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_Exist_" data-uid="VRage.ModAPI.IMyEntities.Exist*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_Exist_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.Exist(VRage.ModAPI.IMyEntity)">Exist(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Checks if entity is registered entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool Exist(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity to test</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity is registered</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_FindFreePlace_" data-uid="VRage.ModAPI.IMyEntities.FindFreePlace*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_FindFreePlace_VRageMath_Vector3D_System_Single_System_Int32_System_Int32_System_Single_" data-uid="VRage.ModAPI.IMyEntities.FindFreePlace(VRageMath.Vector3D,System.Single,System.Int32,System.Int32,System.Single)">FindFreePlace(Vector3D, Single, Int32, Int32, Single)</h4>
  <div class="markdown level1 summary"><p>Use to find place that doesn't have any voxels, grids, or physical bodies.
If original position can't fill check sphere, new position in some distance is picked.
Distance grows each testsPerDistance attempts.
Maximum distance from BasePos that can be used is calculated by formula: maxTestCount / testsPerDistance * radius * stepSize</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Nullable&lt;Vector3D&gt; FindFreePlace(Vector3D basePos, float radius, int maxTestCount = 20, int testsPerDistance = 5, float stepSize = 1F)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">basePos</span></td>
        <td><p>Base position</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">radius</span></td>
        <td><p>Radius in which there should be nothing</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">maxTestCount</span></td>
        <td><p>How many tries should be done, to find free space</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">testsPerDistance</span></td>
        <td><p>Depends how often distance from original position grows</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">stepSize</span></td>
        <td><p>How distance grows</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<a class="xref" href="VRageMath.Vector3D.html">Vector3D</a>&gt;</td>
        <td><p>Position that can doesn't have voxels, grids and other HkBodies in provided radius</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetElementsInBox_" data-uid="VRage.ModAPI.IMyEntities.GetElementsInBox*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetElementsInBox_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyEntities.GetElementsInBox(VRageMath.BoundingBoxD@)">GetElementsInBox(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Returns list of entities that intersects with BoundingBox.
This function will return CubeBlocks. This function works slower than <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetTopMostEntitiesInBox_VRageMath_BoundingBoxD__">GetTopMostEntitiesInBox(ref BoundingBoxD)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetElementsInBox(ref BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td><p>Bounding box in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>New list of entities</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntities_GetElementsInBox_VRageMath_BoundingBoxD___remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Same as <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetEntitiesInAABB_VRageMath_BoundingBoxD__">GetEntitiesInAABB(ref BoundingBoxD)</a></p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntities_" data-uid="VRage.ModAPI.IMyEntities.GetEntities*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntities_System_Collections_Generic_HashSet_VRage_ModAPI_IMyEntity__System_Func_VRage_ModAPI_IMyEntity_System_Boolean__" data-uid="VRage.ModAPI.IMyEntities.GetEntities(System.Collections.Generic.HashSet{VRage.ModAPI.IMyEntity},System.Func{VRage.ModAPI.IMyEntity,System.Boolean})">GetEntities(HashSet&lt;IMyEntity&gt;, Func&lt;IMyEntity, Boolean&gt;)</h4>
  <div class="markdown level1 summary"><p>Get all entities matching condition</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void GetEntities(HashSet&lt;IMyEntity&gt; entities, Func&lt;IMyEntity, bool&gt; collect = null)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.HashSet</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><span class="parametername">entities</span></td>
        <td><p>This set would receive results. Can be null, but then collect function should always return false</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">collect</span></td>
        <td><p>When it is null or returns true, provided hashset adds entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntitiesInAABB_" data-uid="VRage.ModAPI.IMyEntities.GetEntitiesInAABB*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntitiesInAABB_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyEntities.GetEntitiesInAABB(VRageMath.BoundingBoxD@)">GetEntitiesInAABB(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Returns list of entities that intersects with BoundingBox.
This function will return CubeBlocks. This function works slower than <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetTopMostEntitiesInBox_VRageMath_BoundingBoxD__">GetTopMostEntitiesInBox(ref BoundingBoxD)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetEntitiesInAABB(ref BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td><p>Bounding box in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>New list of entities</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntities_GetEntitiesInAABB_VRageMath_BoundingBoxD___remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Same as <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetElementsInBox_VRageMath_BoundingBoxD__">GetElementsInBox(ref BoundingBoxD)</a></p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntitiesInSphere_" data-uid="VRage.ModAPI.IMyEntities.GetEntitiesInSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntitiesInSphere_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyEntities.GetEntitiesInSphere(VRageMath.BoundingSphereD@)">GetEntitiesInSphere(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Returns list of entities that intersects with sphere.
This function will return CubeBlocks. This function works slower than <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetTopMostEntitiesInSphere_VRageMath_BoundingSphereD__">GetTopMostEntitiesInSphere(ref BoundingSphereD)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetEntitiesInSphere(ref BoundingSphereD boundingSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">boundingSphere</span></td>
        <td><p>Bounding sphere in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>New list of entities</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntity_" data-uid="VRage.ModAPI.IMyEntities.GetEntity*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntity_System_Func_VRage_ModAPI_IMyEntity_System_Boolean__" data-uid="VRage.ModAPI.IMyEntities.GetEntity(System.Func{VRage.ModAPI.IMyEntity,System.Boolean})">GetEntity(Func&lt;IMyEntity, Boolean&gt;)</h4>
  <div class="markdown level1 summary"><p>Get first entity that matching condition</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetEntity(Func&lt;IMyEntity, bool&gt; match)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Func</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>, <span class="xref">System.Boolean</span>&gt;</td>
        <td><span class="parametername">match</span></td>
        <td><p>When return true, this entity would be used as a return value</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>First matching condition entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntityById_" data-uid="VRage.ModAPI.IMyEntities.GetEntityById*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntityById_System_Int64_" data-uid="VRage.ModAPI.IMyEntities.GetEntityById(System.Int64)">GetEntityById(Int64)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided entityId</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetEntityById(long entityId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">entityId</span></td>
        <td><p>EntityId</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Entity with provided id, or null</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntityById_" data-uid="VRage.ModAPI.IMyEntities.GetEntityById*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntityById_System_Nullable_System_Int64__" data-uid="VRage.ModAPI.IMyEntities.GetEntityById(System.Nullable{System.Int64})">GetEntityById(Nullable&lt;Int64&gt;)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided entityId</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetEntityById(Nullable&lt;long&gt; entityId)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int64</span>&gt;</td>
        <td><span class="parametername">entityId</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Entity with provided id, or null</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetEntityByName_" data-uid="VRage.ModAPI.IMyEntities.GetEntityByName*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetEntityByName_System_String_" data-uid="VRage.ModAPI.IMyEntities.GetEntityByName(System.String)">GetEntityByName(String)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetEntityByName(string name)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a></p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Entity with registered Name or null</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetInflatedPlayerBoundingBox_" data-uid="VRage.ModAPI.IMyEntities.GetInflatedPlayerBoundingBox*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetInflatedPlayerBoundingBox_VRageMath_BoundingBox__System_Single_" data-uid="VRage.ModAPI.IMyEntities.GetInflatedPlayerBoundingBox(VRageMath.BoundingBox@,System.Single)">GetInflatedPlayerBoundingBox(ref BoundingBox, Single)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void GetInflatedPlayerBoundingBox(ref BoundingBox playerBox, float inflation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBox.html">BoundingBox</a></td>
        <td><span class="parametername">playerBox</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">inflation</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetInflatedPlayerBoundingBox_" data-uid="VRage.ModAPI.IMyEntities.GetInflatedPlayerBoundingBox*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetInflatedPlayerBoundingBox_VRageMath_BoundingBoxD__System_Single_" data-uid="VRage.ModAPI.IMyEntities.GetInflatedPlayerBoundingBox(VRageMath.BoundingBoxD@,System.Single)">GetInflatedPlayerBoundingBox(ref BoundingBoxD, Single)</h4>
  <div class="markdown level1 summary"><p>Making playerBox include all connected players</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void GetInflatedPlayerBoundingBox(ref BoundingBoxD playerBox, float inflation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">playerBox</span></td>
        <td><p>Box, that would contain all players</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td><span class="parametername">inflation</span></td>
        <td><p>Minimal distance between player, and border</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere(VRageMath.BoundingSphereD@)">GetIntersectionWithSphere(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Returns first found (not closest) entity that intersects with sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetIntersectionWithSphere(ref BoundingSphereD sphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to test (in world coordinates)</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>First found entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_VRageMath_BoundingSphereD__VRage_ModAPI_IMyEntity_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere(VRageMath.BoundingSphereD@,VRage.ModAPI.IMyEntity,VRage.ModAPI.IMyEntity)">GetIntersectionWithSphere(ref BoundingSphereD, IMyEntity, IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Returns first found (not closest) entity that intersects with sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetIntersectionWithSphere(ref BoundingSphereD sphere, IMyEntity ignoreEntity0, IMyEntity ignoreEntity1)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to test (in world coordinates)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity0</span></td>
        <td><p>Return value can't be this entity</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity1</span></td>
        <td><p>Return value can't be this entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>First found entity, or null</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_VRageMath_BoundingSphereD__VRage_ModAPI_IMyEntity_VRage_ModAPI_IMyEntity_System_Boolean_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere(VRageMath.BoundingSphereD@,VRage.ModAPI.IMyEntity,VRage.ModAPI.IMyEntity,System.Boolean,System.Boolean)">GetIntersectionWithSphere(ref BoundingSphereD, IMyEntity, IMyEntity, Boolean, Boolean)</h4>
  <div class="markdown level1 summary"><p>Returns list of entities that intersects with sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetIntersectionWithSphere(ref BoundingSphereD sphere, IMyEntity ignoreEntity0, IMyEntity ignoreEntity1, bool ignoreVoxelMaps, bool volumetricTest)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to test (in world coordinates)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity0</span></td>
        <td><p>Returned list can't contain this entity</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity1</span></td>
        <td><p>Returned list can't contain this entity</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">ignoreVoxelMaps</span></td>
        <td><p>When true, voxels won't checked</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">volumetricTest</span></td>
        <td><p>When false physical shape checking used. It is much more accurate, but slower</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>List of entities inside of sphere</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_VRageMath_BoundingSphereD__VRage_ModAPI_IMyEntity_VRage_ModAPI_IMyEntity_System_Boolean_System_Boolean__remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p>Returned list may be used by system, next call if this or other similar function will clear list, so if you need to store data for long time, copy data from it. Also clean list, after you don't need it anymore</p>
</div>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetIntersectionWithSphere_VRageMath_BoundingSphereD__VRage_ModAPI_IMyEntity_VRage_ModAPI_IMyEntity_System_Boolean_System_Boolean_System_Boolean_System_Boolean_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.GetIntersectionWithSphere(VRageMath.BoundingSphereD@,VRage.ModAPI.IMyEntity,VRage.ModAPI.IMyEntity,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">GetIntersectionWithSphere(ref BoundingSphereD, IMyEntity, IMyEntity, Boolean, Boolean, Boolean, Boolean, Boolean)</h4>
  <div class="markdown level1 summary"><p>Returns first found (not closest) entity that intersects with sphere</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyEntity GetIntersectionWithSphere(ref BoundingSphereD sphere, IMyEntity ignoreEntity0, IMyEntity ignoreEntity1, bool ignoreVoxelMaps, bool volumetricTest, bool excludeEntitiesWithDisabledPhysics = false, bool ignoreFloatingObjects = true, bool ignoreHandWeapons = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">sphere</span></td>
        <td><p>Sphere to test (in world coordinates)</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity0</span></td>
        <td><p>Return value can't be this entity</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">ignoreEntity1</span></td>
        <td><p>Return value can't be this entity</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">ignoreVoxelMaps</span></td>
        <td><p>When true, voxels won't checked</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">volumetricTest</span></td>
        <td><p>When false physical shape checking used. It is much more accurate, but slower</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">excludeEntitiesWithDisabledPhysics</span></td>
        <td><p>When true, entities with disabled physics won't checked</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">ignoreFloatingObjects</span></td>
        <td><p>When true, floating objects won't checked</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">ignoreHandWeapons</span></td>
        <td><p>When true, hand weapons (tools) won't checked</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><p>Found entity matching conditions</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetTopMostEntitiesInBox_" data-uid="VRage.ModAPI.IMyEntities.GetTopMostEntitiesInBox*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetTopMostEntitiesInBox_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyEntities.GetTopMostEntitiesInBox(VRageMath.BoundingBoxD@)">GetTopMostEntitiesInBox(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"><p>Returns list of <code>TopMost</code> entities that intersects with bounding box.
This function won't return CubeBlocks. Use <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetElementsInBox_VRageMath_BoundingBoxD__">GetElementsInBox(ref BoundingBoxD)</a> to retrieve CubeBlocks also.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetTopMostEntitiesInBox(ref BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td><p>Bounding box in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>New list of entities</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_GetTopMostEntitiesInSphere_" data-uid="VRage.ModAPI.IMyEntities.GetTopMostEntitiesInSphere*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_GetTopMostEntitiesInSphere_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyEntities.GetTopMostEntitiesInSphere(VRageMath.BoundingSphereD@)">GetTopMostEntitiesInSphere(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Returns list of <code>TopMost</code> entities that intersects with sphere.
This function won't return CubeBlocks. Use <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_GetEntitiesInSphere_VRageMath_BoundingSphereD__">GetEntitiesInSphere(ref BoundingSphereD)</a> to retrieve CubeBlocks also.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">List&lt;IMyEntity&gt; GetTopMostEntitiesInSphere(ref BoundingSphereD boundingSphere)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">boundingSphere</span></td>
        <td><p>Bounding sphere in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td><p>New list of entities</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsInsideVoxel_" data-uid="VRage.ModAPI.IMyEntities.IsInsideVoxel*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsInsideVoxel_VRageMath_Vector3_VRageMath_Vector3_VRageMath_Vector3__" data-uid="VRage.ModAPI.IMyEntities.IsInsideVoxel(VRageMath.Vector3,VRageMath.Vector3,VRageMath.Vector3@)">IsInsideVoxel(Vector3, Vector3, out Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInsideVoxel(Vector3 pos, Vector3 hintPosition, out Vector3 lastOutsidePos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">pos</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">hintPosition</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3.html">Vector3</a></td>
        <td><span class="parametername">lastOutsidePos</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsInsideVoxel_" data-uid="VRage.ModAPI.IMyEntities.IsInsideVoxel*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsInsideVoxel_VRageMath_Vector3D_VRageMath_Vector3D_VRageMath_Vector3D__" data-uid="VRage.ModAPI.IMyEntities.IsInsideVoxel(VRageMath.Vector3D,VRageMath.Vector3D,VRageMath.Vector3D@)">IsInsideVoxel(Vector3D, Vector3D, out Vector3D)</h4>
  <div class="markdown level1 summary"><p>Return true if line between pos and hintPosition doesn't intersect any voxel, or intersects it even number of times
Does inside physical ray casting inside</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInsideVoxel(Vector3D pos, Vector3D hintPosition, out Vector3D lastOutsidePos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">pos</span></td>
        <td><p>Position of object</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">hintPosition</span></td>
        <td><p>Position of object few frames back to test whether object entered voxel. Usually pos - LinearVelocity * x, where x it time.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">lastOutsidePos</span></td>
        <td><p>Last position that was outside of voxels</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsInsideWorld_" data-uid="VRage.ModAPI.IMyEntities.IsInsideWorld*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsInsideWorld_VRageMath_Vector3D_" data-uid="VRage.ModAPI.IMyEntities.IsInsideWorld(VRageMath.Vector3D)">IsInsideWorld(Vector3D)</h4>
  <div class="markdown level1 summary"><p>Returns true if distance from 0,0,0 to provided position is less than <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_WorldHalfExtent">WorldHalfExtent()</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsInsideWorld(Vector3D pos)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">pos</span></td>
        <td><p>Checked position in world coordinates</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if distance is less than <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_WorldHalfExtent">WorldHalfExtent()</a></p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsNameExists_" data-uid="VRage.ModAPI.IMyEntities.IsNameExists*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsNameExists_VRage_ModAPI_IMyEntity_System_String_" data-uid="VRage.ModAPI.IMyEntities.IsNameExists(VRage.ModAPI.IMyEntity,System.String)">IsNameExists(IMyEntity, String)</h4>
  <div class="markdown level1 summary"><p>Checks if registered name belongs to this entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsNameExists(IMyEntity entity, string name)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity to test</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p>Name to test</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if registered name belongs to this entity</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsRaycastBlocked_" data-uid="VRage.ModAPI.IMyEntities.IsRaycastBlocked*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsRaycastBlocked_VRageMath_Vector3D_VRageMath_Vector3D_" data-uid="VRage.ModAPI.IMyEntities.IsRaycastBlocked(VRageMath.Vector3D,VRageMath.Vector3D)">IsRaycastBlocked(Vector3D, Vector3D)</h4>
  <div class="markdown level1 summary"><p>Returns true if raycast hits anything (with raycast layer=0)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsRaycastBlocked(Vector3D pos, Vector3D target)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">pos</span></td>
        <td><p>From</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td><span class="parametername">target</span></td>
        <td><p>To</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsSpherePenetrating_" data-uid="VRage.ModAPI.IMyEntities.IsSpherePenetrating*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsSpherePenetrating_VRageMath_BoundingSphereD__" data-uid="VRage.ModAPI.IMyEntities.IsSpherePenetrating(VRageMath.BoundingSphereD@)">IsSpherePenetrating(ref BoundingSphereD)</h4>
  <div class="markdown level1 summary"><p>Checks if sphere hits any <span class="xref">Havok.HkRigidBody</span></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsSpherePenetrating(ref BoundingSphereD bs)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingSphereD.html">BoundingSphereD</a></td>
        <td><span class="parametername">bs</span></td>
        <td><p>Sphere that used for intersection check</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if sphere hits any body</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsTypeHidden_" data-uid="VRage.ModAPI.IMyEntities.IsTypeHidden*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsTypeHidden_System_Type_" data-uid="VRage.ModAPI.IMyEntities.IsTypeHidden(System.Type)">IsTypeHidden(Type)</h4>
  <div class="markdown level1 summary"><p>Gets whether entities that inherit type is visible or not. Example: <pre><code>IsTypeHidden(typeof (IMyCubeGrid))</code></pre></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsTypeHidden(Type type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsVisible_" data-uid="VRage.ModAPI.IMyEntities.IsVisible*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsVisible_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.IsVisible(VRage.ModAPI.IMyEntity)">IsVisible(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Gets whether entity is visible or not because of <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_SetTypeHidden_System_Type_System_Boolean_">SetTypeHidden(Type, Boolean)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsVisible(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True when visible</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_IsWorldLimited_" data-uid="VRage.ModAPI.IMyEntities.IsWorldLimited*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_IsWorldLimited" data-uid="VRage.ModAPI.IMyEntities.IsWorldLimited">IsWorldLimited()</h4>
  <div class="markdown level1 summary"><p>Return whether world has limited size in kilometers</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsWorldLimited()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if limited</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_MarkForClose_" data-uid="VRage.ModAPI.IMyEntities.MarkForClose*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_MarkForClose_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.MarkForClose(VRage.ModAPI.IMyEntity)">MarkForClose(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Mark entity as closed. Soon it would be deleted. Doesn't call <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">Close()</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void MarkForClose(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity to close</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RegisterForDraw_" data-uid="VRage.ModAPI.IMyEntities.RegisterForDraw*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RegisterForDraw_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.RegisterForDraw(VRage.ModAPI.IMyEntity)">RegisterForDraw(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Make entity receive PrepareForDraw and sending to it's Render Draw call</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RegisterForDraw(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>That should be drawn</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RegisterForUpdate_" data-uid="VRage.ModAPI.IMyEntities.RegisterForUpdate*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RegisterForUpdate_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.RegisterForUpdate(VRage.ModAPI.IMyEntity)">RegisterForUpdate(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Make entity receive UpdateBeforeSimulation, UpdateBefore10Simulation, UpdateBefore100Simulation, UpdateAfterSimulation, UpdateAfter10Simulation, UpdateAfter100Simulation, Simulate, UpdateBeforeNextFrame depending on it's <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsUpdate">NeedsUpdate</a> flags.
Physics are still simulated</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RegisterForUpdate(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>That should have updates</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RemapObjectBuilder_" data-uid="VRage.ModAPI.IMyEntities.RemapObjectBuilder*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RemapObjectBuilder_VRage_ObjectBuilders_MyObjectBuilder_EntityBase_" data-uid="VRage.ModAPI.IMyEntities.RemapObjectBuilder(VRage.ObjectBuilders.MyObjectBuilder_EntityBase)">RemapObjectBuilder(MyObjectBuilder_EntityBase)</h4>
  <div class="markdown level1 summary"><p>Remaps this entity's <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a> and <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a> to a new values.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemapObjectBuilder(MyObjectBuilder_EntityBase objectBuilder)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a></td>
        <td><span class="parametername">objectBuilder</span></td>
        <td><p>ObjectBuilder that should be remapped</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RemapObjectBuilderCollection_" data-uid="VRage.ModAPI.IMyEntities.RemapObjectBuilderCollection*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RemapObjectBuilderCollection_System_Collections_Generic_IEnumerable_VRage_ObjectBuilders_MyObjectBuilder_EntityBase__" data-uid="VRage.ModAPI.IMyEntities.RemapObjectBuilderCollection(System.Collections.Generic.IEnumerable{VRage.ObjectBuilders.MyObjectBuilder_EntityBase})">RemapObjectBuilderCollection(IEnumerable&lt;MyObjectBuilder_EntityBase&gt;)</h4>
  <div class="markdown level1 summary"><p>Remaps this entity's <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a> and <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a> to a new values.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemapObjectBuilderCollection(IEnumerable&lt;MyObjectBuilder_EntityBase&gt; objectBuilders)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IEnumerable</span>&lt;<a class="xref" href="VRage.ObjectBuilders.MyObjectBuilder_EntityBase.html">MyObjectBuilder_EntityBase</a>&gt;</td>
        <td><span class="parametername">objectBuilders</span></td>
        <td><p>ObjectBuilders that should be remapped</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RemoveEntity_" data-uid="VRage.ModAPI.IMyEntities.RemoveEntity*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RemoveEntity_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.RemoveEntity(VRage.ModAPI.IMyEntity)">RemoveEntity(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Unregisters entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemoveEntity(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity that should be unregistered</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RemoveFromClosedEntities_" data-uid="VRage.ModAPI.IMyEntities.RemoveFromClosedEntities*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RemoveFromClosedEntities_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.RemoveFromClosedEntities(VRage.ModAPI.IMyEntity)">RemoveFromClosedEntities(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Remove entity from lists of closed entities</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemoveFromClosedEntities(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity that should be removed</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_RemoveName_" data-uid="VRage.ModAPI.IMyEntities.RemoveName*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_RemoveName_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.RemoveName(VRage.ModAPI.IMyEntity)">RemoveName(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Removes registered name from entity.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void RemoveName(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity, that has name</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_SetEntityName_" data-uid="VRage.ModAPI.IMyEntities.SetEntityName*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_SetEntityName_VRage_ModAPI_IMyEntity_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.SetEntityName(VRage.ModAPI.IMyEntity,System.Boolean)">SetEntityName(IMyEntity, Boolean)</h4>
  <div class="markdown level1 summary"><p>Apply <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a> for entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetEntityName(IMyEntity IMyEntity, bool possibleRename = true)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">IMyEntity</span></td>
        <td><p>Entity that should be named</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">possibleRename</span></td>
        <td><p>Allows renaming</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_SetTypeHidden_" data-uid="VRage.ModAPI.IMyEntities.SetTypeHidden*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_SetTypeHidden_System_Type_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.SetTypeHidden(System.Type,System.Boolean)">SetTypeHidden(Type, Boolean)</h4>
  <div class="markdown level1 summary"><p>Entities that inherit that type would be visible/invisible.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void SetTypeHidden(Type type, bool hidden)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td><p>Type that class should inherit to be invisible, ex: IMyCubeGrid</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">hidden</span></td>
        <td><p>Sets if inherited entities should be visible visible or not</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_TryGetEntityById_" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityById*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_TryGetEntityById_System_Int64_VRage_ModAPI_IMyEntity__" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityById(System.Int64,VRage.ModAPI.IMyEntity@)">TryGetEntityById(Int64, out IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided id</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool TryGetEntityById(long id, out IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int64</span></td>
        <td><span class="parametername">id</span></td>
        <td><p>EntityId</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Found entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity is found</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_TryGetEntityById_" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityById*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_TryGetEntityById_System_Nullable_System_Int64__VRage_ModAPI_IMyEntity__" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityById(System.Nullable{System.Int64},VRage.ModAPI.IMyEntity@)">TryGetEntityById(Nullable&lt;Int64&gt;, out IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided id</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool TryGetEntityById(Nullable&lt;long&gt; id, out IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Nullable</span>&lt;<span class="xref">System.Int64</span>&gt;</td>
        <td><span class="parametername">id</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">EntityId</a></p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Found entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity is found</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_TryGetEntityByName_" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityByName*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_TryGetEntityByName_System_String_VRage_ModAPI_IMyEntity__" data-uid="VRage.ModAPI.IMyEntities.TryGetEntityByName(System.String,VRage.ModAPI.IMyEntity@)">TryGetEntityByName(String, out IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Returns entity with provided name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool TryGetEntityByName(string name, out IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td><span class="parametername">name</span></td>
        <td><p><a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a></p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Found entity</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><p>True if entity is found</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_UnhideAllTypes_" data-uid="VRage.ModAPI.IMyEntities.UnhideAllTypes*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_UnhideAllTypes" data-uid="VRage.ModAPI.IMyEntities.UnhideAllTypes">UnhideAllTypes()</h4>
  <div class="markdown level1 summary"><p>Revert all changes to default. Make all entities visible, that were hidden because of <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_SetTypeHidden_System_Type_System_Boolean_">SetTypeHidden(Type, Boolean)</a></p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void UnhideAllTypes()</code></pre>
  </div>
  
  
  <a id="VRage_ModAPI_IMyEntities_UnregisterForDraw_" data-uid="VRage.ModAPI.IMyEntities.UnregisterForDraw*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_UnregisterForDraw_VRage_ModAPI_IMyEntity_" data-uid="VRage.ModAPI.IMyEntities.UnregisterForDraw(VRage.ModAPI.IMyEntity)">UnregisterForDraw(IMyEntity)</h4>
  <div class="markdown level1 summary"><p>Unregistering entity from PrepareForDraw events and it Render from Draw calls.
Entity may still be rendered</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void UnregisterForDraw(IMyEntity entity)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity that should stop receive draw calls</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_UnregisterForUpdate_" data-uid="VRage.ModAPI.IMyEntities.UnregisterForUpdate*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_UnregisterForUpdate_VRage_ModAPI_IMyEntity_System_Boolean_" data-uid="VRage.ModAPI.IMyEntities.UnregisterForUpdate(VRage.ModAPI.IMyEntity,System.Boolean)">UnregisterForUpdate(IMyEntity, Boolean)</h4>
  <div class="markdown level1 summary"><p>Unregistering entity from following updates: UpdateBeforeSimulation, UpdateBefore10Simulation, UpdateBefore100Simulation, UpdateAfterSimulation, UpdateAfter10Simulation, UpdateAfter100Simulation, Simulate, UpdateBeforeNextFrame
Physics are still simulated</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void UnregisterForUpdate(IMyEntity entity, bool immediate = false)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></td>
        <td><span class="parametername">entity</span></td>
        <td><p>Entity that should not receive updates anymore</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">immediate</span></td>
        <td><p>When true, updates removed immediately, but may cause crashes</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_WorldHalfExtent_" data-uid="VRage.ModAPI.IMyEntities.WorldHalfExtent*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_WorldHalfExtent" data-uid="VRage.ModAPI.IMyEntities.WorldHalfExtent">WorldHalfExtent()</h4>
  <div class="markdown level1 summary"><p>Returns shortest distance (i.e. axis-aligned) to the world border from the world center.
Will be 0 if world is borderless</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float WorldHalfExtent()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyEntities_WorldSafeHalfExtent_" data-uid="VRage.ModAPI.IMyEntities.WorldSafeHalfExtent*"></a>
  <h4 id="VRage_ModAPI_IMyEntities_WorldSafeHalfExtent" data-uid="VRage.ModAPI.IMyEntities.WorldSafeHalfExtent">WorldSafeHalfExtent()</h4>
  <div class="markdown level1 summary"><p>Returns shortest distance (i.e. axis-aligned) to the world border from the world center, minus 600m to make spawning somewhat safer.
Will be 0 if world is borderless</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">float WorldSafeHalfExtent()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Single</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="events">Events
  </h3>
  
  
  <h4 id="VRage_ModAPI_IMyEntities_OnCloseAll" data-uid="VRage.ModAPI.IMyEntities.OnCloseAll">OnCloseAll</h4>
  <div class="markdown level1 summary"><p>Called when session unloads, before grids are closed</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action OnCloseAll</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_IMyEntities_OnEntityAdd" data-uid="VRage.ModAPI.IMyEntities.OnEntityAdd">OnEntityAdd</h4>
  <div class="markdown level1 summary"><p>Called when <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_AddEntity_VRage_ModAPI_IMyEntity_System_Boolean_">AddEntity(IMyEntity, Boolean)</a> called on entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnEntityAdd</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_IMyEntities_OnEntityNameSet" data-uid="VRage.ModAPI.IMyEntities.OnEntityNameSet">OnEntityNameSet</h4>
  <div class="markdown level1 summary"><p>Called when entity gets <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">Name</a>. First string - old name, Second - new name</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity, string, string&gt; OnEntityNameSet</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>, <span class="xref">System.String</span>, <span class="xref">System.String</span>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRage_ModAPI_IMyEntities_OnEntityRemove" data-uid="VRage.ModAPI.IMyEntities.OnEntityRemove">OnEntityRemove</h4>
  <div class="markdown level1 summary"><p>Called when <a class="xref" href="VRage.ModAPI.IMyEntities.html#VRage_ModAPI_IMyEntities_RemoveEntity_VRage_ModAPI_IMyEntity_">RemoveEntity(IMyEntity)</a> called on entity</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">event Action&lt;IMyEntity&gt; OnEntityRemove</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span>&lt;<a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="seealso">See Also</h3>
  <div class="seealso">
      <div><a class="xref" href="VRage.ModAPI.IMyEntity.html">IMyEntity</a></div>
  </div>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
