﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface IMyVoxelBase
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface IMyVoxelBase
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRage.ModAPI.IMyVoxelBase">
  
  
  <h1 id="VRage_ModAPI_IMyVoxelBase" data-uid="VRage.ModAPI.IMyVoxelBase" class="text-break">Interface IMyVoxelBase
  </h1>
  <div class="markdown level0 summary"><p>Describes</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetFriendlyName">IMyEntity.GetFriendlyName()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Close">IMyEntity.Close()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Delete">IMyEntity.Delete()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetObjectBuilder_System_Boolean_">IMyEntity.GetObjectBuilder(Boolean)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_BeforeSave">IMyEntity.BeforeSave()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetTopMostParent_System_Type_">IMyEntity.GetTopMostParent(Type)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetLocalMatrix_VRageMath_Matrix_System_Object_">IMyEntity.SetLocalMatrix(Matrix, Object)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetChildren_System_Collections_Generic_List_VRage_ModAPI_IMyEntity__System_Func_VRage_ModAPI_IMyEntity_System_Boolean__">IMyEntity.GetChildren(List&lt;IMyEntity&gt;, Func&lt;IMyEntity, Boolean&gt;)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetSubpart_System_String_">IMyEntity.GetSubpart(String)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_TryGetSubpart_System_String_VRage_Game_Entity_MyEntitySubpart__">IMyEntity.TryGetSubpart(String, MyEntitySubpart)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetDiffuseColor">IMyEntity.GetDiffuseColor()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_IsVisible">IMyEntity.IsVisible()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_DebugDraw">IMyEntity.DebugDraw()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_DebugDrawInvalidTriangles">IMyEntity.DebugDrawInvalidTriangles()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EnableColorMaskForSubparts_System_Boolean_">IMyEntity.EnableColorMaskForSubparts(Boolean)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetColorMaskForSubparts_VRageMath_Vector3_">IMyEntity.SetColorMaskForSubparts(Vector3)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_System_Collections_Generic_Dictionary_VRage_Utils_MyStringId_VRageRender_Messages_MyTextureChange__">IMyEntity.SetTextureChangesForSubparts(Dictionary&lt;MyStringId, MyTextureChange&gt;)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetTextureChangesForSubparts_System_Collections_Generic_Dictionary_System_String_VRageRender_Messages_MyTextureChange__">IMyEntity.SetTextureChangesForSubparts(Dictionary&lt;String, MyTextureChange&gt;)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetEmissiveParts_System_String_VRageMath_Color_System_Single_">IMyEntity.SetEmissiveParts(String, Color, Single)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetEmissivePartsForSubparts_System_String_VRageMath_Color_System_Single_">IMyEntity.SetEmissivePartsForSubparts(String, Color, Single)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndBoundingSphere">IMyEntity.GetDistanceBetweenCameraAndBoundingSphere()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetDistanceBetweenCameraAndPosition">IMyEntity.GetDistanceBetweenCameraAndPosition()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetLargestDistanceBetweenCameraAndBoundingSphere">IMyEntity.GetLargestDistanceBetweenCameraAndBoundingSphere()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetSmallestDistanceBetweenCameraAndBoundingSphere">IMyEntity.GetSmallestDistanceBetweenCameraAndBoundingSphere()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnRemovedFromScene_System_Object_">IMyEntity.OnRemovedFromScene(Object)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnAddedToScene_System_Object_">IMyEntity.OnAddedToScene(Object)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetViewMatrix">IMyEntity.GetViewMatrix()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetWorldMatrixNormalizedInv">IMyEntity.GetWorldMatrixNormalizedInv()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetWorldMatrix_VRageMath_MatrixD_System_Object_">IMyEntity.SetWorldMatrix(MatrixD, Object)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SetPosition_VRageMath_Vector3D_">IMyEntity.SetPosition(Vector3D)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Teleport_VRageMath_MatrixD_System_Object_System_Boolean_">IMyEntity.Teleport(MatrixD, Object, Boolean)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetIntersectionWithLine_VRageMath_LineD__System_Nullable_VRage_Game_Models_MyIntersectionResultLineTriangleEx___VRage_Game_Components_IntersectionFlags_">IMyEntity.GetIntersectionWithLine(LineD, Nullable&lt;MyIntersectionResultLineTriangleEx&gt;, IntersectionFlags)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetIntersectionWithLineAndBoundingSphere_VRageMath_LineD__System_Single_">IMyEntity.GetIntersectionWithLineAndBoundingSphere(LineD, Single)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetIntersectionWithSphere_VRageMath_BoundingSphereD__">IMyEntity.GetIntersectionWithSphere(BoundingSphereD)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetIntersectionWithAABB_VRageMath_BoundingBoxD__">IMyEntity.GetIntersectionWithAABB(BoundingBoxD)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetTrianglesIntersectingSphere_VRageMath_BoundingSphere__System_Nullable_VRageMath_Vector3__System_Nullable_System_Single__System_Collections_Generic_List_VRage_Utils_MyTriangle_Vertex_Normals__System_Int32_">IMyEntity.GetTrianglesIntersectingSphere(BoundingSphere, Nullable&lt;Vector3&gt;, Nullable&lt;Single&gt;, List&lt;MyTriangle_Vertex_Normals&gt;, Int32)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_DoOverlapSphereTest_System_Single_VRageMath_Vector3D_">IMyEntity.DoOverlapSphereTest(Single, Vector3D)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetInventory">IMyEntity.GetInventory()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GetInventory_System_Int32_">IMyEntity.GetInventory(Int32)</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_AddToGamePruningStructure">IMyEntity.AddToGamePruningStructure()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_RemoveFromGamePruningStructure">IMyEntity.RemoveFromGamePruningStructure()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_UpdateGamePruningStructure">IMyEntity.UpdateGamePruningStructure()</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Physics">IMyEntity.Physics</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_PositionComp">IMyEntity.PositionComp</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Render">IMyEntity.Render</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_GameLogic">IMyEntity.GameLogic</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Hierarchy">IMyEntity.Hierarchy</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SyncObject">IMyEntity.SyncObject</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Flags">IMyEntity.Flags</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_EntityId">IMyEntity.EntityId</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Name">IMyEntity.Name</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_MarkedForClose">IMyEntity.MarkedForClose</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_DebugAsyncLoading">IMyEntity.DebugAsyncLoading</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Save">IMyEntity.Save</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_PersistentFlags">IMyEntity.PersistentFlags</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Model">IMyEntity.Model</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_ModelCollision">IMyEntity.ModelCollision</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Synchronized">IMyEntity.Synchronized</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsUpdate">IMyEntity.NeedsUpdate</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Parent">IMyEntity.Parent</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalMatrix">IMyEntity.LocalMatrix</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NearFlag">IMyEntity.NearFlag</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_CastShadows">IMyEntity.CastShadows</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_FastCastShadowResolve">IMyEntity.FastCastShadowResolve</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsResolveCastShadow">IMyEntity.NeedsResolveCastShadow</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_MaxGlassDistSq">IMyEntity.MaxGlassDistSq</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsDraw">IMyEntity.NeedsDraw</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsDrawFromParent">IMyEntity.NeedsDrawFromParent</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Transparent">IMyEntity.Transparent</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_ShadowBoxLod">IMyEntity.ShadowBoxLod</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_SkipIfTooSmall">IMyEntity.SkipIfTooSmall</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_Visible">IMyEntity.Visible</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_NeedsWorldMatrix">IMyEntity.NeedsWorldMatrix</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_InScene">IMyEntity.InScene</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_InvalidateOnMove">IMyEntity.InvalidateOnMove</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrix">IMyEntity.WorldMatrix</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixInvScaled">IMyEntity.WorldMatrixInvScaled</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_WorldMatrixNormalizedInv">IMyEntity.WorldMatrixNormalizedInv</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_IsVolumetric">IMyEntity.IsVolumetric</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalAABB">IMyEntity.LocalAABB</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalAABBHr">IMyEntity.LocalAABBHr</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalVolume">IMyEntity.LocalVolume</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocalVolumeOffset">IMyEntity.LocalVolumeOffset</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_LocationForHudMarker">IMyEntity.LocationForHudMarker</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_IsCCDForProjectiles">IMyEntity.IsCCDForProjectiles</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_DisplayName">IMyEntity.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_StopPhysicsActivation">IMyEntity.StopPhysicsActivation</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClose">IMyEntity.OnClose</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnClosing">IMyEntity.OnClosing</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnMarkForClose">IMyEntity.OnMarkForClose</a>
    </div>
    <div>
      <a class="xref" href="VRage.ModAPI.IMyEntity.html#VRage_ModAPI_IMyEntity_OnPhysicsChanged">IMyEntity.OnPhysicsChanged</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_GetPosition">IMyEntity.GetPosition()</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_Components">IMyEntity.Components</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_HasInventory">IMyEntity.HasInventory</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_InventoryCount">IMyEntity.InventoryCount</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_Closed">IMyEntity.Closed</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldAABB">IMyEntity.WorldAABB</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldAABBHr">IMyEntity.WorldAABBHr</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldVolume">IMyEntity.WorldVolume</a>
    </div>
    <div>
      <a class="xref" href="VRage.Game.ModAPI.Ingame.IMyEntity.html#VRage_Game_ModAPI_Ingame_IMyEntity_WorldVolumeHr">IMyEntity.WorldVolumeHr</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRage.ModAPI.html">VRage.ModAPI</a></h6>
  <h6><strong>Assembly</strong>: VRage.Game.dll</h6>
  <h5 id="VRage_ModAPI_IMyVoxelBase_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IMyVoxelBase : IMyEntity, IMyEntity</code></pre>
  </div>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRage_ModAPI_IMyVoxelBase_PositionLeftBottomCorner_" data-uid="VRage.ModAPI.IMyVoxelBase.PositionLeftBottomCorner*"></a>
  <h4 id="VRage_ModAPI_IMyVoxelBase_PositionLeftBottomCorner" data-uid="VRage.ModAPI.IMyVoxelBase.PositionLeftBottomCorner">PositionLeftBottomCorner</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Vector3D PositionLeftBottomCorner { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Vector3D.html">Vector3D</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyVoxelBase_Storage_" data-uid="VRage.ModAPI.IMyVoxelBase.Storage*"></a>
  <h4 id="VRage_ModAPI_IMyVoxelBase_Storage" data-uid="VRage.ModAPI.IMyVoxelBase.Storage">Storage</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IMyStorage Storage { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRage.ModAPI.IMyStorage.html">IMyStorage</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRage_ModAPI_IMyVoxelBase_StorageName_" data-uid="VRage.ModAPI.IMyVoxelBase.StorageName*"></a>
  <h4 id="VRage_ModAPI_IMyVoxelBase_StorageName" data-uid="VRage.ModAPI.IMyVoxelBase.StorageName">StorageName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">string StorageName { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRage_ModAPI_IMyVoxelBase_IsBoxIntersectingBoundingBoxOfThisVoxelMap_" data-uid="VRage.ModAPI.IMyVoxelBase.IsBoxIntersectingBoundingBoxOfThisVoxelMap*"></a>
  <h4 id="VRage_ModAPI_IMyVoxelBase_IsBoxIntersectingBoundingBoxOfThisVoxelMap_VRageMath_BoundingBoxD__" data-uid="VRage.ModAPI.IMyVoxelBase.IsBoxIntersectingBoundingBoxOfThisVoxelMap(VRageMath.BoundingBoxD@)">IsBoxIntersectingBoundingBoxOfThisVoxelMap(ref BoundingBoxD)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool IsBoxIntersectingBoundingBoxOfThisVoxelMap(ref BoundingBoxD boundingBox)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.BoundingBoxD.html">BoundingBoxD</a></td>
        <td><span class="parametername">boundingBox</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
