﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>
  
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Rectangle
   | Space Engineers ModAPI </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Rectangle
   | Space Engineers ModAPI ">
    <meta name="generator" content="docfx 2.58.2.0">
    
    <link rel="shortcut icon" href="../favicon.ico">
    <link rel="stylesheet" href="../styles/docfx.vendor.css">
    <link rel="stylesheet" href="../styles/docfx.css">
    <link rel="stylesheet" href="../styles/main.css">
    <meta property="docfx:navrel" content="../toc.html">
    <meta property="docfx:tocrel" content="toc.html">
    
    
    
  </head>  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>
        
        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              
              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>
        
        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div role="main" class="container body-content hide-when-search">
        
        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="VRageMath.Rectangle">
  
  
  <h1 id="VRageMath_Rectangle" data-uid="VRageMath.Rectangle" class="text-break">Class Rectangle
  </h1>
  <div class="markdown level0 summary"><p>Defines a rectangle.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">System.Object</span></div>
    <div class="level1"><span class="xref">Rectangle</span></div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="VRageMath.html">VRageMath</a></h6>
  <h6><strong>Assembly</strong>: VRage.Math.dll</h6>
  <h5 id="VRageMath_Rectangle_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public sealed class Rectangle : ValueType, IEquatable&lt;Rectangle&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
  </h3>
  
  
  <a id="VRageMath_Rectangle__ctor_" data-uid="VRageMath.Rectangle.#ctor*"></a>
  <h4 id="VRageMath_Rectangle__ctor_System_Int32_System_Int32_System_Int32_System_Int32_" data-uid="VRageMath.Rectangle.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">Rectangle(Int32, Int32, Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Rectangle(int x, int y, int width, int height)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>The x-coordinate of the rectangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>The y-coordinate of the rectangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">width</span></td>
        <td><p>Width of the rectangle.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">height</span></td>
        <td><p>Height of the rectangle.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="fields">Fields
  </h3>
  
  
  <h4 id="VRageMath_Rectangle_Height" data-uid="VRageMath.Rectangle.Height">Height</h4>
  <div class="markdown level1 summary"><p>Specifies the height of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Height</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Rectangle_Width" data-uid="VRageMath.Rectangle.Width">Width</h4>
  <div class="markdown level1 summary"><p>Specifies the width of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Width</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Rectangle_X" data-uid="VRageMath.Rectangle.X">X</h4>
  <div class="markdown level1 summary"><p>Specifies the x-coordinate of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int X</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <h4 id="VRageMath_Rectangle_Y" data-uid="VRageMath.Rectangle.Y">Y</h4>
  <div class="markdown level1 summary"><p>Specifies the y-coordinate of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Y</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
  </h3>
  
  
  <a id="VRageMath_Rectangle_Bottom_" data-uid="VRageMath.Rectangle.Bottom*"></a>
  <h4 id="VRageMath_Rectangle_Bottom" data-uid="VRageMath.Rectangle.Bottom">Bottom</h4>
  <div class="markdown level1 summary"><p>Returns the y-coordinate of the bottom of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Bottom { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Center_" data-uid="VRageMath.Rectangle.Center*"></a>
  <h4 id="VRageMath_Rectangle_Center" data-uid="VRageMath.Rectangle.Center">Center</h4>
  <div class="markdown level1 summary"><p>Gets the Point that specifies the center of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Point Center { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Point.html">Point</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Left_" data-uid="VRageMath.Rectangle.Left*"></a>
  <h4 id="VRageMath_Rectangle_Left" data-uid="VRageMath.Rectangle.Left">Left</h4>
  <div class="markdown level1 summary"><p>Returns the x-coordinate of the left side of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Left { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Location_" data-uid="VRageMath.Rectangle.Location*"></a>
  <h4 id="VRageMath_Rectangle_Location" data-uid="VRageMath.Rectangle.Location">Location</h4>
  <div class="markdown level1 summary"><p>Gets or sets the upper-left value of the Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Point Location { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Point.html">Point</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Right_" data-uid="VRageMath.Rectangle.Right*"></a>
  <h4 id="VRageMath_Rectangle_Right" data-uid="VRageMath.Rectangle.Right">Right</h4>
  <div class="markdown level1 summary"><p>Returns the x-coordinate of the right side of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Right { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Top_" data-uid="VRageMath.Rectangle.Top*"></a>
  <h4 id="VRageMath_Rectangle_Top" data-uid="VRageMath.Rectangle.Top">Top</h4>
  <div class="markdown level1 summary"><p>Returns the y-coordinate of the top of the rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Top { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
  </h3>
  
  
  <a id="VRageMath_Rectangle_Contains_" data-uid="VRageMath.Rectangle.Contains*"></a>
  <h4 id="VRageMath_Rectangle_Contains_System_Int32_System_Int32_" data-uid="VRageMath.Rectangle.Contains(System.Int32,System.Int32)">Contains(Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Rectangle contains a specified point represented by its x- and y-coordinates.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(int x, int y)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">x</span></td>
        <td><p>The x-coordinate of the specified point.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">y</span></td>
        <td><p>The y-coordinate of the specified point.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Contains_" data-uid="VRageMath.Rectangle.Contains*"></a>
  <h4 id="VRageMath_Rectangle_Contains_VRageMath_Point_" data-uid="VRageMath.Rectangle.Contains(VRageMath.Point)">Contains(Point)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Rectangle contains a specified Point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(Point value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Point.html">Point</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Point to evaluate.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Contains_" data-uid="VRageMath.Rectangle.Contains*"></a>
  <h4 id="VRageMath_Rectangle_Contains_VRageMath_Point__System_Boolean__" data-uid="VRageMath.Rectangle.Contains(VRageMath.Point@,System.Boolean@)">Contains(ref Point, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Rectangle contains a specified Point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Point value, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Point.html">Point</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Point to evaluate.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the specified Point is contained within this Rectangle; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Contains_" data-uid="VRageMath.Rectangle.Contains*"></a>
  <h4 id="VRageMath_Rectangle_Contains_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.Contains(VRageMath.Rectangle)">Contains(Rectangle)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Rectangle entirely contains a specified Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Contains(Rectangle value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Rectangle to evaluate.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Contains_" data-uid="VRageMath.Rectangle.Contains*"></a>
  <h4 id="VRageMath_Rectangle_Contains_VRageMath_Rectangle__System_Boolean__" data-uid="VRageMath.Rectangle.Contains(VRageMath.Rectangle@,System.Boolean@)">Contains(ref Rectangle, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Determines whether this Rectangle entirely contains a specified Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Contains(ref Rectangle value, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Rectangle to evaluate.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] On exit, is true if this Rectangle entirely contains the specified Rectangle, or false if not.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Equals_" data-uid="VRageMath.Rectangle.Equals*"></a>
  <h4 id="VRageMath_Rectangle_Equals_System_Object_" data-uid="VRageMath.Rectangle.Equals(System.Object)">Equals(Object)</h4>
  <div class="markdown level1 summary"><p>Returns a value that indicates whether the current instance is equal to a specified object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool Equals(object obj)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Object</span></td>
        <td><span class="parametername">obj</span></td>
        <td><p>Object to make the comparison with.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Equals_" data-uid="VRageMath.Rectangle.Equals*"></a>
  <h4 id="VRageMath_Rectangle_Equals_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.Equals(VRageMath.Rectangle)">Equals(Rectangle)</h4>
  <div class="markdown level1 summary"><p>Determines whether the specified Object is equal to the Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Equals(Rectangle other)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">other</span></td>
        <td><p>The Object to compare with the current Rectangle.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_GetHashCode_" data-uid="VRageMath.Rectangle.GetHashCode*"></a>
  <h4 id="VRageMath_Rectangle_GetHashCode" data-uid="VRageMath.Rectangle.GetHashCode">GetHashCode()</h4>
  <div class="markdown level1 summary"><p>Gets the hash code for this object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override int GetHashCode()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Inflate_" data-uid="VRageMath.Rectangle.Inflate*"></a>
  <h4 id="VRageMath_Rectangle_Inflate_System_Int32_System_Int32_" data-uid="VRageMath.Rectangle.Inflate(System.Int32,System.Int32)">Inflate(Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Pushes the edges of the Rectangle out by the horizontal and vertical values specified.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Inflate(int horizontalAmount, int verticalAmount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">horizontalAmount</span></td>
        <td><p>Value to push the sides out by.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">verticalAmount</span></td>
        <td><p>Value to push the top and bottom out by.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Intersect_" data-uid="VRageMath.Rectangle.Intersect*"></a>
  <h4 id="VRageMath_Rectangle_Intersect_VRageMath_Rectangle_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.Intersect(VRageMath.Rectangle,VRageMath.Rectangle)">Intersect(Rectangle, Rectangle)</h4>
  <div class="markdown level1 summary"><p>Creates a Rectangle defining the area where one rectangle overlaps with another rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Rectangle Intersect(Rectangle value1, Rectangle value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Rectangle to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Rectangle to compare.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Intersect_" data-uid="VRageMath.Rectangle.Intersect*"></a>
  <h4 id="VRageMath_Rectangle_Intersect_VRageMath_Rectangle__VRageMath_Rectangle__VRageMath_Rectangle__" data-uid="VRageMath.Rectangle.Intersect(VRageMath.Rectangle@,VRageMath.Rectangle@,VRageMath.Rectangle@)">Intersect(ref Rectangle, ref Rectangle, out Rectangle)</h4>
  <div class="markdown level1 summary"><p>Creates a Rectangle defining the area where one rectangle overlaps with another rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Intersect(ref Rectangle value1, ref Rectangle value2, out Rectangle result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Rectangle to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Rectangle to compare.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The area where the two first parameters overlap.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Intersects_" data-uid="VRageMath.Rectangle.Intersects*"></a>
  <h4 id="VRageMath_Rectangle_Intersects_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.Intersects(VRageMath.Rectangle)">Intersects(Rectangle)</h4>
  <div class="markdown level1 summary"><p>Determines whether a specified Rectangle intersects with this Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Intersects(Rectangle value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Rectangle to evaluate.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Intersects_" data-uid="VRageMath.Rectangle.Intersects*"></a>
  <h4 id="VRageMath_Rectangle_Intersects_VRageMath_Rectangle__System_Boolean__" data-uid="VRageMath.Rectangle.Intersects(VRageMath.Rectangle@,System.Boolean@)">Intersects(ref Rectangle, out Boolean)</h4>
  <div class="markdown level1 summary"><p>Determines whether a specified Rectangle intersects with this Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Intersects(ref Rectangle value, out bool result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value</span></td>
        <td><p>The Rectangle to evaluate</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] true if the specified Rectangle intersects with this one; false otherwise.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Offset_" data-uid="VRageMath.Rectangle.Offset*"></a>
  <h4 id="VRageMath_Rectangle_Offset_System_Int32_System_Int32_" data-uid="VRageMath.Rectangle.Offset(System.Int32,System.Int32)">Offset(Int32, Int32)</h4>
  <div class="markdown level1 summary"><p>Changes the position of the Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Offset(int offsetX, int offsetY)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">offsetX</span></td>
        <td><p>Change in the x-position.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">System.Int32</span></td>
        <td><span class="parametername">offsetY</span></td>
        <td><p>Change in the y-position.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Offset_" data-uid="VRageMath.Rectangle.Offset*"></a>
  <h4 id="VRageMath_Rectangle_Offset_VRageMath_Point_" data-uid="VRageMath.Rectangle.Offset(VRageMath.Point)">Offset(Point)</h4>
  <div class="markdown level1 summary"><p>Changes the position of the Rectangle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Offset(Point amount)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Point.html">Point</a></td>
        <td><span class="parametername">amount</span></td>
        <td><p>The values to adjust the position of the Rectangle by.</p>
</td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_ToString_" data-uid="VRageMath.Rectangle.ToString*"></a>
  <h4 id="VRageMath_Rectangle_ToString" data-uid="VRageMath.Rectangle.ToString">ToString()</h4>
  <div class="markdown level1 summary"><p>Retrieves a string representation of the current object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string ToString()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.String</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Union_" data-uid="VRageMath.Rectangle.Union*"></a>
  <h4 id="VRageMath_Rectangle_Union_VRageMath_Rectangle_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.Union(VRageMath.Rectangle,VRageMath.Rectangle)">Union(Rectangle, Rectangle)</h4>
  <div class="markdown level1 summary"><p>Creates a new Rectangle that exactly contains two other rectangles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Rectangle Union(Rectangle value1, Rectangle value2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Rectangle to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Rectangle to contain.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_Union_" data-uid="VRageMath.Rectangle.Union*"></a>
  <h4 id="VRageMath_Rectangle_Union_VRageMath_Rectangle__VRageMath_Rectangle__VRageMath_Rectangle__" data-uid="VRageMath.Rectangle.Union(VRageMath.Rectangle@,VRageMath.Rectangle@,VRageMath.Rectangle@)">Union(ref Rectangle, ref Rectangle, out Rectangle)</h4>
  <div class="markdown level1 summary"><p>Creates a new Rectangle that exactly contains two other rectangles.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Union(ref Rectangle value1, ref Rectangle value2, out Rectangle result)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value1</span></td>
        <td><p>The first Rectangle to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">value2</span></td>
        <td><p>The second Rectangle to contain.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">result</span></td>
        <td><p>[OutAttribute] The Rectangle that must be the union of the first two rectangles.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="operators">Operators
  </h3>
  
  
  <a id="VRageMath_Rectangle_op_Equality_" data-uid="VRageMath.Rectangle.op_Equality*"></a>
  <h4 id="VRageMath_Rectangle_op_Equality_VRageMath_Rectangle_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.op_Equality(VRageMath.Rectangle,VRageMath.Rectangle)">Equality(Rectangle, Rectangle)</h4>
  <div class="markdown level1 summary"><p>Compares two rectangles for equality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator ==(Rectangle a, Rectangle b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>Source rectangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>Source rectangle.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  
  
  <a id="VRageMath_Rectangle_op_Inequality_" data-uid="VRageMath.Rectangle.op_Inequality*"></a>
  <h4 id="VRageMath_Rectangle_op_Inequality_VRageMath_Rectangle_VRageMath_Rectangle_" data-uid="VRageMath.Rectangle.op_Inequality(VRageMath.Rectangle,VRageMath.Rectangle)">Inequality(Rectangle, Rectangle)</h4>
  <div class="markdown level1 summary"><p>Compares two rectangles for inequality.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool operator !=(Rectangle a, Rectangle b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">a</span></td>
        <td><p>Source rectangle.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="VRageMath.Rectangle.html">Rectangle</a></td>
        <td><span class="parametername">b</span></td>
        <td><p>Source rectangle.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Boolean</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>
          
          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <div class="toggle-mode">
                <div class="icon">
                  <i aria-hidden="true">☀</i>
                </div>
                <label class="switch">
                  <input type="checkbox" id="switch-style">
                  <span class="slider round"></span>
                </label>
                <div class="icon">
                  <i aria-hidden="true">☾</i>
                </div>
              </div>
          
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
              <h5>In This Article</h5>
              <div></div>
              <!-- <p><a class="back-to-top" href="#top">Back to top</a><p> -->
              </nav>
            </div>
          </div>
        </div>
      </div>
      
      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
            <div class="pull-left">
              
              <span>Generated by <strong>DocFX</strong></span>
            </div>
            <div class="toggle-mode pull-right visible-sm visible-xs">
              <div class="icon">
                <i aria-hidden="true">☀</i>
              </div>
              <label class="switch">
                <input type="checkbox" id="switch-style-m">
                <span class="slider round"></span>
              </label>
              <div class="icon">
                <i aria-hidden="true">☾</i>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript" src="../styles/toggle-theme.js"></script>
      </footer>    </div>
    
    <script type="text/javascript" src="../styles/docfx.vendor.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
